#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Query Coordinator for IFESS Server

Implements queue-based query distribution and result aggregation to prevent
client interference and ensure complete data collection.
"""

import threading
import time
import uuid
import logging
from datetime import datetime
from collections import defaultdict
from queue import Queue, Empty
import json

class QueryCoordinator:
    """
    Enhanced query coordinator with queue-based distribution and result aggregation
    """
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.QueryCoordinator")
        
        # Query management
        self.active_queries = {}  # query_id -> QueryExecution
        self.query_queue = Queue()
        self.result_aggregator = ResultAggregator()
        
        # Client management
        self.client_queues = {}  # client_id -> Queue
        self.client_status = {}  # client_id -> status info
        
        # Coordination locks
        self.coordinator_lock = threading.RLock()
        self.clients_lock = threading.RLock()
        
        # Worker threads
        self.coordinator_thread = None
        self.running = False
        
        self.start_coordinator()
    
    def start_coordinator(self):
        """Start the query coordinator thread"""
        if self.coordinator_thread and self.coordinator_thread.is_alive():
            return
            
        self.running = True
        self.coordinator_thread = threading.Thread(target=self._coordinator_loop, daemon=True)
        self.coordinator_thread.start()
        self.logger.info("Query coordinator started")
    
    def stop_coordinator(self):
        """Stop the query coordinator"""
        self.running = False
        if self.coordinator_thread:
            self.coordinator_thread.join(timeout=5)
        self.logger.info("Query coordinator stopped")
    
    def register_client(self, client_id, client_name):
        """Register a client for query coordination"""
        with self.clients_lock:
            self.client_queues[client_id] = Queue()
            self.client_status[client_id] = {
                'name': client_name,
                'status': 'idle',
                'last_query': None,
                'last_activity': time.time(),
                'total_queries': 0,
                'successful_queries': 0,
                'failed_queries': 0
            }
        self.logger.info(f"Client registered for coordination: {client_name} ({client_id})")
    
    def unregister_client(self, client_id):
        """Unregister a client from coordination"""
        with self.clients_lock:
            if client_id in self.client_queues:
                del self.client_queues[client_id]
            if client_id in self.client_status:
                del self.client_status[client_id]
        self.logger.info(f"Client unregistered from coordination: {client_id}")
    
    def submit_query(self, query, target_clients=None, query_variables=None, timeout=300):
        """
        Submit a query for coordinated execution
        
        :param query: SQL query to execute
        :param target_clients: List of client IDs to target (None for all)
        :param query_variables: Variables to substitute in query
        :param timeout: Query timeout in seconds
        :return: query_id for tracking
        """
        query_id = str(uuid.uuid4())
        
        # Replace variables in query
        processed_query = query
        if query_variables:
            for var_name, var_value in query_variables.items():
                processed_query = processed_query.replace(f'#{var_name}#', str(var_value))
        
        # Determine target clients
        with self.clients_lock:
            if target_clients is None:
                target_clients = list(self.client_queues.keys())
            else:
                # Filter to only include registered clients
                target_clients = [cid for cid in target_clients if cid in self.client_queues]
        
        if not target_clients:
            self.logger.warning("No valid target clients for query")
            return None
        
        # Create query execution object
        query_execution = QueryExecution(
            query_id=query_id,
            query=processed_query,
            original_query=query,
            variables=query_variables or {},
            target_clients=target_clients,
            timeout=timeout
        )
        
        with self.coordinator_lock:
            self.active_queries[query_id] = query_execution
        
        # Queue the query for processing
        self.query_queue.put(query_execution)
        
        self.logger.info(f"Query submitted for coordination: {query_id}")
        self.logger.info(f"  - Target clients: {len(target_clients)}")
        self.logger.info(f"  - Query: {processed_query[:100]}...")
        
        return query_id
    
    def get_query_status(self, query_id):
        """Get status of a coordinated query"""
        with self.coordinator_lock:
            if query_id not in self.active_queries:
                return None
            return self.active_queries[query_id].get_status()
    
    def get_query_results(self, query_id):
        """Get aggregated results for a query"""
        with self.coordinator_lock:
            if query_id not in self.active_queries:
                return None
            return self.active_queries[query_id].get_results()
    
    def report_client_result(self, client_id, query_id, result_data):
        """Report query result from a client"""
        with self.coordinator_lock:
            if query_id in self.active_queries:
                query_execution = self.active_queries[query_id]
                query_execution.add_result(client_id, result_data)
                
                # Update client status
                with self.clients_lock:
                    if client_id in self.client_status:
                        self.client_status[client_id]['status'] = 'idle'
                        self.client_status[client_id]['last_activity'] = time.time()
                        self.client_status[client_id]['successful_queries'] += 1
                
                self.logger.info(f"Result received from {client_id} for query {query_id}")
                return True
        return False
    
    def report_client_error(self, client_id, query_id, error_message):
        """Report query error from a client"""
        with self.coordinator_lock:
            if query_id in self.active_queries:
                query_execution = self.active_queries[query_id]
                query_execution.add_error(client_id, error_message)
                
                # Update client status
                with self.clients_lock:
                    if client_id in self.client_status:
                        self.client_status[client_id]['status'] = 'error'
                        self.client_status[client_id]['last_activity'] = time.time()
                        self.client_status[client_id]['failed_queries'] += 1
                
                self.logger.warning(f"Error received from {client_id} for query {query_id}: {error_message}")
                return True
        return False
    
    def get_client_status(self):
        """Get status of all registered clients"""
        with self.clients_lock:
            return self.client_status.copy()
    
    def _coordinator_loop(self):
        """Main coordinator loop"""
        while self.running:
            try:
                # Process queued queries
                try:
                    query_execution = self.query_queue.get(timeout=1)
                    self._process_query(query_execution)
                except Empty:
                    pass
                
                # Check for completed/timed out queries
                self._cleanup_completed_queries()
                
                # Update client status
                self._update_client_status()
                
            except Exception as e:
                self.logger.error(f"Error in coordinator loop: {e}")
                time.sleep(1)
    
    def _process_query(self, query_execution):
        """Process a query execution by distributing to clients"""
        query_id = query_execution.query_id
        
        self.logger.info(f"Processing query {query_id}")
        
        # Mark query as started
        query_execution.start()
        
        # Distribute to target clients
        distributed_count = 0
        for client_id in query_execution.target_clients:
            try:
                with self.clients_lock:
                    if client_id in self.client_queues:
                        # Create query message for client
                        query_message = {
                            'query_id': query_id,
                            'query': query_execution.query,
                            'timestamp': time.time(),
                            'timeout': query_execution.timeout
                        }
                        
                        # Update client status
                        if client_id in self.client_status:
                            self.client_status[client_id]['status'] = 'executing'
                            self.client_status[client_id]['last_query'] = query_id
                            self.client_status[client_id]['last_activity'] = time.time()
                            self.client_status[client_id]['total_queries'] += 1
                        
                        # This would be sent to the actual client
                        # For now, we'll mark it as distributed
                        query_execution.mark_distributed(client_id)
                        distributed_count += 1
                        
                        self.logger.debug(f"Query {query_id} distributed to client {client_id}")
                        
            except Exception as e:
                self.logger.error(f"Error distributing query to client {client_id}: {e}")
                query_execution.add_error(client_id, f"Distribution error: {e}")
        
        self.logger.info(f"Query {query_id} distributed to {distributed_count} clients")
    
    def _cleanup_completed_queries(self):
        """Clean up completed or timed out queries"""
        current_time = time.time()
        completed_queries = []
        
        with self.coordinator_lock:
            for query_id, query_execution in self.active_queries.items():
                if query_execution.is_completed() or query_execution.is_timed_out(current_time):
                    completed_queries.append(query_id)
        
        for query_id in completed_queries:
            with self.coordinator_lock:
                if query_id in self.active_queries:
                    query_execution = self.active_queries[query_id]
                    status = query_execution.get_status()
                    
                    self.logger.info(f"Query {query_id} completed: {status['status']}")
                    self.logger.info(f"  - Results: {status['results_received']}/{status['total_clients']}")
                    self.logger.info(f"  - Errors: {status['errors_received']}")
                    
                    # Keep completed queries for a while for result retrieval
                    if current_time - query_execution.completion_time > 300:  # 5 minutes
                        del self.active_queries[query_id]
    
    def _update_client_status(self):
        """Update client status based on activity"""
        current_time = time.time()
        
        with self.clients_lock:
            for client_id, status in self.client_status.items():
                # Mark clients as inactive if no activity for 60 seconds
                if current_time - status['last_activity'] > 60:
                    if status['status'] != 'inactive':
                        status['status'] = 'inactive'
                        self.logger.debug(f"Client {client_id} marked as inactive")


class QueryExecution:
    """Represents a single query execution across multiple clients"""
    
    def __init__(self, query_id, query, original_query, variables, target_clients, timeout):
        self.query_id = query_id
        self.query = query
        self.original_query = original_query
        self.variables = variables
        self.target_clients = target_clients
        self.timeout = timeout
        
        self.start_time = None
        self.completion_time = None
        self.distributed_clients = set()
        self.results = {}  # client_id -> result_data
        self.errors = {}   # client_id -> error_message
        
        self.lock = threading.RLock()
    
    def start(self):
        """Mark query as started"""
        with self.lock:
            self.start_time = time.time()
    
    def mark_distributed(self, client_id):
        """Mark query as distributed to a client"""
        with self.lock:
            self.distributed_clients.add(client_id)
    
    def add_result(self, client_id, result_data):
        """Add result from a client"""
        with self.lock:
            self.results[client_id] = {
                'data': result_data,
                'timestamp': time.time()
            }
            self._check_completion()
    
    def add_error(self, client_id, error_message):
        """Add error from a client"""
        with self.lock:
            self.errors[client_id] = {
                'error': error_message,
                'timestamp': time.time()
            }
            self._check_completion()
    
    def _check_completion(self):
        """Check if query execution is complete"""
        with self.lock:
            total_responses = len(self.results) + len(self.errors)
            if total_responses >= len(self.target_clients) and self.completion_time is None:
                self.completion_time = time.time()
    
    def is_completed(self):
        """Check if query execution is completed"""
        with self.lock:
            return self.completion_time is not None
    
    def is_timed_out(self, current_time):
        """Check if query execution has timed out"""
        with self.lock:
            if self.start_time is None:
                return False
            return (current_time - self.start_time) > self.timeout
    
    def get_status(self):
        """Get current status of query execution"""
        with self.lock:
            current_time = time.time()
            
            status = {
                'query_id': self.query_id,
                'query': self.query,
                'original_query': self.original_query,
                'variables': self.variables,
                'start_time': self.start_time,
                'completion_time': self.completion_time,
                'total_clients': len(self.target_clients),
                'distributed_clients': len(self.distributed_clients),
                'results_received': len(self.results),
                'errors_received': len(self.errors),
                'status': 'pending'
            }
            
            if self.start_time is None:
                status['status'] = 'queued'
            elif self.is_completed():
                status['status'] = 'completed'
            elif self.is_timed_out(current_time):
                status['status'] = 'timed_out'
            else:
                status['status'] = 'executing'
            
            if self.start_time:
                status['elapsed_time'] = current_time - self.start_time
            
            return status
    
    def get_results(self):
        """Get aggregated results from all clients"""
        with self.lock:
            return {
                'query_id': self.query_id,
                'status': self.get_status(),
                'results': self.results.copy(),
                'errors': self.errors.copy(),
                'aggregated_data': self._aggregate_results()
            }
    
    def _aggregate_results(self):
        """Aggregate results from multiple clients"""
        if not self.results:
            return {
                'headers': [],
                'rows': [],
                'total_rows': 0,
                'client_count': 0,
                'aggregation_method': 'none'
            }
        
        # Simple aggregation - combine all rows
        all_headers = set()
        all_rows = []
        client_metadata = []
        
        for client_id, result_info in self.results.items():
            result_data = result_info['data']
            
            # Extract headers
            headers = result_data.get('headers', [])
            all_headers.update(headers)
            
            # Extract rows
            rows = result_data.get('rows', [])
            for row in rows:
                # Add client information to each row
                enhanced_row = row.copy() if isinstance(row, dict) else {}
                enhanced_row['_client_id'] = client_id
                enhanced_row['_client_timestamp'] = result_info['timestamp']
                all_rows.append(enhanced_row)
            
            # Collect client metadata
            client_metadata.append({
                'client_id': client_id,
                'row_count': len(rows),
                'column_count': len(headers),
                'timestamp': result_info['timestamp'],
                'parsing_info': result_data.get('parsing_info', {}),
                'data_types': result_data.get('data_types', {})
            })
        
        # Create unified header list
        unified_headers = sorted(list(all_headers))
        
        return {
            'headers': unified_headers,
            'rows': all_rows,
            'total_rows': len(all_rows),
            'client_count': len(self.results),
            'client_metadata': client_metadata,
            'aggregation_method': 'union_all',
            'aggregation_timestamp': time.time()
        }


class ResultAggregator:
    """Handles result aggregation from multiple clients"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.ResultAggregator")
    
    def aggregate_results(self, results_dict):
        """
        Aggregate results from multiple clients
        
        :param results_dict: Dict of client_id -> result_data
        :return: Aggregated result structure
        """
        if not results_dict:
            return self._empty_result()
        
        # Analyze result structures
        analysis = self._analyze_results(results_dict)
        
        # Choose aggregation strategy
        if analysis['uniform_structure']:
            return self._aggregate_uniform_results(results_dict, analysis)
        else:
            return self._aggregate_mixed_results(results_dict, analysis)
    
    def _analyze_results(self, results_dict):
        """Analyze result structures to determine aggregation strategy"""
        headers_sets = []
        row_counts = []
        data_types_sets = []
        
        for client_id, result_data in results_dict.items():
            headers = result_data.get('headers', [])
            rows = result_data.get('rows', [])
            data_types = result_data.get('data_types', {})
            
            headers_sets.append(set(headers))
            row_counts.append(len(rows))
            data_types_sets.append(data_types)
        
        # Check if all clients have the same headers
        uniform_headers = len(set(frozenset(h) for h in headers_sets)) == 1
        
        return {
            'uniform_structure': uniform_headers,
            'total_clients': len(results_dict),
            'total_rows': sum(row_counts),
            'header_sets': headers_sets,
            'row_counts': row_counts,
            'data_types_sets': data_types_sets
        }
    
    def _aggregate_uniform_results(self, results_dict, analysis):
        """Aggregate results with uniform structure"""
        all_rows = []
        client_metadata = []
        
        # Get headers from first result (they're all the same)
        first_result = next(iter(results_dict.values()))
        headers = first_result.get('headers', [])
        
        for client_id, result_data in results_dict.items():
            rows = result_data.get('rows', [])
            
            # Add client metadata to each row
            for row in rows:
                enhanced_row = row.copy() if isinstance(row, dict) else {}
                enhanced_row['_source_client'] = client_id
                all_rows.append(enhanced_row)
            
            client_metadata.append({
                'client_id': client_id,
                'row_count': len(rows),
                'data_types': result_data.get('data_types', {}),
                'parsing_info': result_data.get('parsing_info', {})
            })
        
        return {
            'headers': headers + ['_source_client'],
            'rows': all_rows,
            'total_rows': len(all_rows),
            'client_count': len(results_dict),
            'client_metadata': client_metadata,
            'aggregation_method': 'uniform_union',
            'structure_analysis': analysis
        }
    
    def _aggregate_mixed_results(self, results_dict, analysis):
        """Aggregate results with mixed structures"""
        # Create union of all headers
        all_headers = set()
        for header_set in analysis['header_sets']:
            all_headers.update(header_set)
        
        unified_headers = sorted(list(all_headers))
        all_rows = []
        client_metadata = []
        
        for client_id, result_data in results_dict.items():
            headers = result_data.get('headers', [])
            rows = result_data.get('rows', [])
            
            # Normalize each row to have all headers
            for row in rows:
                normalized_row = {}
                
                # Fill in values for headers that exist
                if isinstance(row, dict):
                    for header in unified_headers:
                        normalized_row[header] = row.get(header)
                
                # Add client metadata
                normalized_row['_source_client'] = client_id
                all_rows.append(normalized_row)
            
            client_metadata.append({
                'client_id': client_id,
                'row_count': len(rows),
                'original_headers': headers,
                'data_types': result_data.get('data_types', {}),
                'parsing_info': result_data.get('parsing_info', {})
            })
        
        return {
            'headers': unified_headers + ['_source_client'],
            'rows': all_rows,
            'total_rows': len(all_rows),
            'client_count': len(results_dict),
            'client_metadata': client_metadata,
            'aggregation_method': 'mixed_union',
            'structure_analysis': analysis
        }
    
    def _empty_result(self):
        """Return empty result structure"""
        return {
            'headers': [],
            'rows': [],
            'total_rows': 0,
            'client_count': 0,
            'client_metadata': [],
            'aggregation_method': 'empty'
        }