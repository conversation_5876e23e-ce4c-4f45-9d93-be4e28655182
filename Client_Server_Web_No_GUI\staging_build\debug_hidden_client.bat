@echo off
title IFESS Hidden Client - DEBUG MODE

echo ============================================
echo   IFESS Hidden Client - DEBUG MODE
echo ============================================
echo.
echo Starting IFESS hidden client in debug mode...
echo This window will show real-time debug output and error messages.
echo Press Ctrl+C to stop the client.
echo.

echo Current directory: %CD%
echo Python version:
python --version
echo.

echo Checking for existing instances...
echo Killing any existing hidden client processes...
taskkill /F /IM "ifess_client_hidden.exe" >nul 2>&1
taskkill /F /IM "python.exe" /FI "WINDOWTITLE eq IFESS*" >nul 2>&1
timeout /t 2 /nobreak >nul

echo.
echo Checking for required files...
if exist ifess_client_hidden.py (
    echo [OK] ifess_client_hidden.py found
) else (
    echo [ERROR] ifess_client_hidden.py not found!
    echo Please ensure you are in the correct directory.
    pause
    exit /b 1
)

if exist client_config.json (
    echo [OK] client_config.json found
    echo Configuration file content:
    echo ----------------------------------------
    type client_config.json
    echo ----------------------------------------
) else (
    echo [WARNING] client_config.json not found - will create default
)

if exist client_config_oauth_tokens.json (
    echo [OK] client_config_oauth_tokens.json found
    echo OAuth configuration file content:
    echo ----------------------------------------
    type client_config_oauth_tokens.json
    echo ----------------------------------------
) else (
    echo [INFO] client_config_oauth_tokens.json not found
)

if exist common\ (
    echo [OK] common directory found
    dir common\ /b
) else (
    echo [ERROR] common directory not found!
    echo This is required for the client to function.
    pause
    exit /b 1
)

echo.
echo Testing Python imports...
python -c "import sys; sys.path.append('.'); from common.network import NetworkMessage; from common.db_utils import FirebirdConnector; print('[OK] Core modules imported successfully')"

if %ERRORLEVEL% neq 0 (
    echo [ERROR] Import test failed. Please check your environment.
    pause
    exit /b 1
)

echo.
echo Starting client with enhanced error reporting...
echo ============================================

python ifess_client_hidden.py --debug --force

set EXIT_CODE=%ERRORLEVEL%
echo.
echo ============================================
echo   Hidden client has stopped
echo   Exit code: %EXIT_CODE%
echo ============================================

if %EXIT_CODE% neq 0 (
    echo.
    echo ERROR: Client exited with non-zero exit code!
    echo Check the error messages above for details.
    echo.
    echo Recent log entries:
    echo ----------------------
    if exist ifess_client_hidden.log (
        echo Last 30 lines from log file:
        powershell "Get-Content ifess_client_hidden.log | Select-Object -Last 30"
    ) else (
        echo No log file found.
    )
    echo.
    echo Configuration files status:
    echo ----------------------
    if exist client_config.json (
        echo client_config.json exists (%~zi bytes)
    ) else (
        echo client_config.json NOT FOUND
    )
    if exist client_config_oauth_tokens.json (
        echo client_config_oauth_tokens.json exists (%~zi bytes)
    ) else (
        echo client_config_oauth_tokens.json NOT FOUND
    )
) else (
    echo.
    echo Client stopped normally.
)

echo.
echo Debug session completed. Press any key to continue...
pause >nul 