#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IFESS Configuration GUI

This application provides a graphical user interface for configuring the IFESS client.
It allows users to:
1. Configure server connection settings
2. Configure database connection settings
3. Test connections
4. Save configuration for use by the hidden client
"""

import os
import sys
import json
import socket
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import traceback
import logging
import time
import platform
import uuid
import subprocess

# Import common modules
try:
    from common.db_utils import FirebirdConnector
except ImportError:
    # Try relative import
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    try:
        from common.db_utils import FirebirdConnector
    except ImportError as e:
        logging.error(f"Failed to import required modules: {e}")
        # We'll define a fallback FirebirdConnector class below

# Get the base directory - handle both running as script and as frozen executable
if getattr(sys, 'frozen', False):
    # Running as compiled executable
    BASE_DIR = os.path.dirname(sys.executable)
else:
    # Running as script
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# Setup logging
LOG_FILE = os.path.join(BASE_DIR, "ifess_config.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("IFESS-Config")

# Constants
# Define paths relative to base directory
CONFIG_FILE = os.path.join(BASE_DIR, "client_config.json")
OAUTH_CONFIG_FILE = os.path.join(BASE_DIR, "client_config_oauth_tokens.json")
DEFAULT_PORT = 5555

# Log the configuration file path
logger.info(f"Using configuration file: {os.path.abspath(CONFIG_FILE)}")

# Fallback FirebirdConnector class if import fails
if 'FirebirdConnector' not in globals():
    class FirebirdConnector:
        """Simplified connector for testing database connection"""
        def __init__(self, db_path, username="SYSDBA", password="masterkey",
                    isql_path=None, use_localhost=True):
            self.db_path = db_path
            self.username = username
            self.password = password
            self.isql_path = isql_path
            self.use_localhost = use_localhost

            # Find isql.exe if not provided
            if not self.isql_path or not os.path.exists(self.isql_path):
                self.find_isql()

        def find_isql(self):
            """Find isql.exe in common locations"""
            common_paths = [
                "C:/Program Files/Firebird/Firebird_1_5/bin/isql.exe",
                "C:/Program Files (x86)/Firebird/Firebird_1_5/bin/isql.exe",
                "C:/Program Files/Firebird/Firebird_2_5/bin/isql.exe",
                "C:/Program Files (x86)/Firebird/Firebird_2_5/bin/isql.exe",
                "C:/Program Files/Firebird/Firebird_3_0/bin/isql.exe",
                "C:/Program Files (x86)/Firebird/Firebird_3_0/bin/isql.exe",
            ]

            for path in common_paths:
                if os.path.exists(path):
                    self.isql_path = path
                    logger.info(f"Found ISQL at: {path}")
                    return

            logger.warning("Could not find ISQL executable")

        def test_connection(self):
            """Test connection to database"""
            if not os.path.exists(self.db_path):
                return False, "Database file not found"

            if not self.isql_path or not os.path.exists(self.isql_path):
                return False, "ISQL executable not found"

            try:
                # Create a simple test query file
                import tempfile
                fd, sql_path = tempfile.mkstemp(suffix='.sql')
                output_fd, output_path = tempfile.mkstemp(suffix='.txt')

                try:
                    # Write a simple query to test connection
                    with os.fdopen(fd, 'w') as f:
                        f.write("SELECT 'Connection Test' FROM RDB$DATABASE;")

                    # Build connection string
                    if self.use_localhost:
                        connection_string = f"localhost:{self.db_path}"
                    else:
                        connection_string = self.db_path

                    # Run ISQL command
                    cmd = [
                        self.isql_path,
                        connection_string,
                        "-user", self.username,
                        "-password", self.password,
                        "-input", sql_path,
                        "-output", output_path
                    ]

                    logger.info(f"Running command: {' '.join(cmd)}")

                    process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        universal_newlines=True
                    )

                    stdout, stderr = process.communicate(timeout=30)  # Increased timeout

                    # Check if there was an error
                    if process.returncode != 0:
                        logger.warning(f"ISQL error (return code {process.returncode}): {stderr}")

                        # Try alternative command format
                        alt_cmd = [
                            self.isql_path,
                            "-u", self.username,
                            "-p", self.password,
                            connection_string,
                            "-i", sql_path,
                            "-o", output_path
                        ]

                        logger.info(f"Trying alternative command: {' '.join(alt_cmd)}")

                        alt_process = subprocess.Popen(
                            alt_cmd,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            universal_newlines=True
                        )

                        alt_stdout, alt_stderr = alt_process.communicate(timeout=30)

                        if alt_process.returncode != 0:
                            return False, f"ISQL error: {alt_stderr or stderr}"

                    # Check output file
                    with open(output_path, 'r') as f:
                        output = f.read()

                    logger.info(f"ISQL output: {output[:200]}...")

                    if "Connection Test" in output or "RDB$DATABASE" in output:
                        return True, "Connection successful"
                    else:
                        # Even if we don't see the expected output, if the command ran without errors,
                        # we'll consider it a success
                        return True, "Connection appears to be working"

                finally:
                    # Clean up temporary files
                    try:
                        os.unlink(sql_path)
                        os.unlink(output_path)
                    except Exception as e:
                        logger.warning(f"Error cleaning up temp files: {e}")

            except Exception as e:
                logger.error(f"Error testing database connection: {e}")
                logger.error(traceback.format_exc())
                return False, f"Error: {str(e)}"

class ConfigApp:
    """Configuration GUI application"""
    def __init__(self, root):
        self.root = root
        self.root.title("IFESS Client Configuration")
        self.root.geometry("600x650")
        self.root.resizable(True, True)

        # Set icon if available
        try:
            self.root.iconbitmap("MAINICON.ico")
        except:
            pass

        # Variables for form fields
        self.server_address = tk.StringVar(value="localhost")
        self.server_port = tk.IntVar(value=DEFAULT_PORT)
        self.client_id = tk.StringVar(value=f"client_{uuid.uuid4().hex[:8]}")
        self.display_name = tk.StringVar(value=f"FDB-Client-{platform.node()}")
        self.reconnect_interval = tk.IntVar(value=5)

        self.db_path = tk.StringVar()
        self.db_username = tk.StringVar(value="SYSDBA")
        self.db_password = tk.StringVar(value="masterkey")
        self.isql_path = tk.StringVar()
        self.use_localhost = tk.BooleanVar(value=True)

        # MEGA cloud storage credentials
        self.mega_email = tk.StringVar(value="<EMAIL>")
        self.mega_password = tk.StringVar(value="ptrj@123")

        # Google Drive credentials
        self.gdrive_credentials_file = tk.StringVar(value="")

        # Scheduled upload settings
        self.enable_scheduled_upload = tk.BooleanVar(value=False)
        self.upload_hour = tk.IntVar(value=2)  # Default 2 AM
        self.upload_minute = tk.IntVar(value=0)  # Default 0 minutes
        self.upload_service = tk.StringVar(value="gdrive")  # Default to Google Drive

        # Create UI
        self.create_widgets()

        # Load configuration if exists
        self.load_config()

        # Initialize token_downloader in the background
        self.initialize_token_downloader()

    def create_widgets(self):
        """Create UI widgets"""
        # Main frame with padding
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Create notebook (tabs)
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=5)

        # Server tab
        server_frame = ttk.Frame(notebook, padding="10")
        notebook.add(server_frame, text="Server Connection")

        # Database tab
        db_frame = ttk.Frame(notebook, padding="10")
        notebook.add(db_frame, text="Database Connection")

        # MEGA Cloud Storage tab
        mega_frame = ttk.Frame(notebook, padding="10")
        notebook.add(mega_frame, text="MEGA Cloud Storage")

        # Google Drive tab
        gdrive_frame = ttk.Frame(notebook, padding="10")
        notebook.add(gdrive_frame, text="Google Drive")

        # Scheduled Upload tab
        schedule_frame = ttk.Frame(notebook, padding="10")
        notebook.add(schedule_frame, text="Scheduled Upload")

        # Status tab
        status_frame = ttk.Frame(notebook, padding="10")
        notebook.add(status_frame, text="Status")

        # Server connection settings
        ttk.Label(server_frame, text="Server Connection Settings", font=("", 12, "bold")).grid(row=0, column=0, columnspan=3, sticky="w", pady=(0, 10))

        ttk.Label(server_frame, text="Server Address:").grid(row=1, column=0, sticky="w", pady=2)
        ttk.Entry(server_frame, textvariable=self.server_address, width=30).grid(row=1, column=1, sticky="we", pady=2)

        ttk.Label(server_frame, text="Server Port:").grid(row=2, column=0, sticky="w", pady=2)
        ttk.Entry(server_frame, textvariable=self.server_port, width=10).grid(row=2, column=1, sticky="w", pady=2)

        ttk.Label(server_frame, text="Client ID:").grid(row=3, column=0, sticky="w", pady=2)
        ttk.Entry(server_frame, textvariable=self.client_id, width=30).grid(row=3, column=1, sticky="we", pady=2)

        ttk.Label(server_frame, text="Display Name:").grid(row=4, column=0, sticky="w", pady=2)
        ttk.Entry(server_frame, textvariable=self.display_name, width=30).grid(row=4, column=1, sticky="we", pady=2)

        ttk.Label(server_frame, text="Reconnect Interval (seconds):").grid(row=5, column=0, sticky="w", pady=2)
        ttk.Entry(server_frame, textvariable=self.reconnect_interval, width=10).grid(row=5, column=1, sticky="w", pady=2)

        # Test server connection button
        ttk.Button(server_frame, text="Test Server Connection", command=self.test_server_connection).grid(row=6, column=0, columnspan=2, sticky="w", pady=(10, 2))

        # Server connection status
        ttk.Label(server_frame, text="Connection Status:").grid(row=7, column=0, sticky="w", pady=2)
        self.server_status_label = ttk.Label(server_frame, text="Not tested", foreground="gray")
        self.server_status_label.grid(row=7, column=1, sticky="w", pady=2)

        # Database connection settings
        ttk.Label(db_frame, text="Database Connection Settings", font=("", 12, "bold")).grid(row=0, column=0, columnspan=3, sticky="w", pady=(0, 10))

        ttk.Label(db_frame, text="Database Path:").grid(row=1, column=0, sticky="w", pady=2)
        ttk.Entry(db_frame, textvariable=self.db_path, width=40).grid(row=1, column=1, sticky="we", pady=2)
        ttk.Button(db_frame, text="Browse...", command=self.browse_db_path).grid(row=1, column=2, sticky="w", padx=5, pady=2)

        ttk.Label(db_frame, text="Username:").grid(row=2, column=0, sticky="w", pady=2)
        ttk.Entry(db_frame, textvariable=self.db_username, width=20).grid(row=2, column=1, sticky="w", pady=2)

        ttk.Label(db_frame, text="Password:").grid(row=3, column=0, sticky="w", pady=2)
        ttk.Entry(db_frame, textvariable=self.db_password, width=20, show="*").grid(row=3, column=1, sticky="w", pady=2)

        ttk.Label(db_frame, text="ISQL Path:").grid(row=4, column=0, sticky="w", pady=2)
        ttk.Entry(db_frame, textvariable=self.isql_path, width=40).grid(row=4, column=1, sticky="we", pady=2)
        ttk.Button(db_frame, text="Browse...", command=self.browse_isql_path).grid(row=4, column=2, sticky="w", padx=5, pady=2)

        ttk.Checkbutton(db_frame, text="Use localhost prefix", variable=self.use_localhost).grid(row=5, column=0, columnspan=2, sticky="w", pady=2)

        # Test database connection button
        ttk.Button(db_frame, text="Test Database Connection", command=self.test_db_connection).grid(row=6, column=0, columnspan=2, sticky="w", pady=(10, 2))

        # Database connection status
        ttk.Label(db_frame, text="Connection Status:").grid(row=7, column=0, sticky="w", pady=2)
        self.db_status_label = ttk.Label(db_frame, text="Not tested", foreground="gray")
        self.db_status_label.grid(row=7, column=1, sticky="w", pady=2)

        # Status information
        ttk.Label(status_frame, text="Application Status", font=("", 12, "bold")).grid(row=0, column=0, columnspan=2, sticky="w", pady=(0, 10))

        # Configuration file status
        ttk.Label(status_frame, text="Configuration File:").grid(row=1, column=0, sticky="w", pady=2)
        self.config_status_label = ttk.Label(status_frame, text="Not loaded", foreground="gray")
        self.config_status_label.grid(row=1, column=1, sticky="w", pady=2)

        # Add information about the hidden client
        ttk.Label(status_frame, text="Hidden Client Information", font=("", 12, "bold")).grid(row=3, column=0, columnspan=2, sticky="w", pady=(20, 10))

        info_text = (
            "The hidden client (ifess_client_hidden.exe) runs without any visible window or taskbar icon.\n\n"
            "To start the hidden client:\n"
            "1. Configure and save settings in this application\n"
            "2. Run ifess_client_hidden.exe\n\n"
            "To view logs and debug information:\n"
            "Run ifess_client_debug.exe while the hidden client is running"
        )

        info_label = ttk.Label(status_frame, text=info_text, wraplength=500, justify="left")
        info_label.grid(row=4, column=0, columnspan=2, sticky="w", pady=5)

        # Buttons frame at the bottom
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=10)

        # Save button
        save_button = ttk.Button(buttons_frame, text="Save Configuration", command=self.save_config)
        save_button.pack(side=tk.LEFT, padx=5)

        # Save and run button
        save_run_button = ttk.Button(buttons_frame, text="Save and Run Hidden Client", command=self.save_and_run)
        save_run_button.pack(side=tk.LEFT, padx=5)

        # Exit button
        exit_button = ttk.Button(buttons_frame, text="Exit", command=self.root.destroy)
        exit_button.pack(side=tk.RIGHT, padx=5)

        # MEGA cloud storage settings
        ttk.Label(mega_frame, text="MEGA Cloud Storage Settings", font=("", 12, "bold")).grid(row=0, column=0, columnspan=3, sticky="w", pady=(0, 10))

        ttk.Label(mega_frame, text="Email:").grid(row=1, column=0, sticky="w", pady=2)
        ttk.Entry(mega_frame, textvariable=self.mega_email, width=30).grid(row=1, column=1, sticky="we", pady=2)

        ttk.Label(mega_frame, text="Password:").grid(row=2, column=0, sticky="w", pady=2)
        ttk.Entry(mega_frame, textvariable=self.mega_password, width=30, show="*").grid(row=2, column=1, sticky="we", pady=2)

        # Test MEGA connection button
        ttk.Button(mega_frame, text="Test MEGA Connection", command=self.test_mega_connection).grid(row=3, column=0, columnspan=2, sticky="w", pady=(10, 2))

        # MEGA connection status
        ttk.Label(mega_frame, text="Connection Status:").grid(row=4, column=0, sticky="w", pady=2)
        self.mega_status_label = ttk.Label(mega_frame, text="Not tested", foreground="gray")
        self.mega_status_label.grid(row=4, column=1, sticky="w", pady=2)

        # MEGA information
        info_text = (
            "MEGA cloud storage is used for backing up database files.\n\n"
            "The credentials provided here will be used by the hidden client to upload database files to MEGA.\n\n"
            "Files will be uploaded to the 'IFESS_Backups' folder in the MEGA account."
        )

        info_label = ttk.Label(mega_frame, text=info_text, wraplength=500, justify="left")
        info_label.grid(row=5, column=0, columnspan=2, sticky="w", pady=(20, 5))

        # Google Drive settings
        ttk.Label(gdrive_frame, text="Google Drive Settings", font=("", 12, "bold")).grid(row=0, column=0, columnspan=3, sticky="w", pady=(0, 10))

        ttk.Label(gdrive_frame, text="Credentials File:").grid(row=1, column=0, sticky="w", pady=2)
        ttk.Entry(gdrive_frame, textvariable=self.gdrive_credentials_file, width=40).grid(row=1, column=1, sticky="we", pady=2)
        ttk.Button(gdrive_frame, text="Browse...", command=self.browse_gdrive_credentials).grid(row=1, column=2, sticky="w", padx=5, pady=2)

        # Test Google Drive connection button
        ttk.Button(gdrive_frame, text="Test Google Drive Connection", command=self.test_gdrive_connection).grid(row=2, column=0, columnspan=2, sticky="w", pady=(10, 2))

        # Google Drive connection status
        ttk.Label(gdrive_frame, text="Connection Status:").grid(row=3, column=0, sticky="w", pady=2)
        self.gdrive_status_label = ttk.Label(gdrive_frame, text="Not tested", foreground="gray")
        self.gdrive_status_label.grid(row=3, column=1, sticky="w", pady=2)

        # Google Drive information
        info_text = (
            "Google Drive is used for backing up database files.\n\n"
            "The credentials file provided here will be used by the hidden client to upload database files to Google Drive.\n\n"
            "Files will be uploaded to the 'Backup_PTRJ/IFESS/{client_id}/' folder in Google Drive."
        )

        info_label = ttk.Label(gdrive_frame, text=info_text, wraplength=500, justify="left")
        info_label.grid(row=4, column=0, columnspan=2, sticky="w", pady=(20, 5))

        # Scheduled Upload settings
        ttk.Label(schedule_frame, text="Scheduled Upload Settings", font=("", 12, "bold")).grid(row=0, column=0, columnspan=3, sticky="w", pady=(0, 10))

        # Enable scheduled upload checkbox
        ttk.Checkbutton(schedule_frame, text="Enable scheduled daily upload", variable=self.enable_scheduled_upload).grid(row=1, column=0, columnspan=3, sticky="w", pady=5)

        # Time settings
        ttk.Label(schedule_frame, text="Upload Time:").grid(row=2, column=0, sticky="w", pady=2)
        
        # Time picker frame with enhanced layout
        time_frame = ttk.LabelFrame(schedule_frame, text="Daily Upload Time (24-hour format)", padding="10")
        time_frame.grid(row=2, column=1, columnspan=2, sticky="w", pady=5)

        # Main time selection row
        time_selection_frame = ttk.Frame(time_frame)
        time_selection_frame.pack(fill=tk.X, pady=(0, 10))

        # Hour selection
        hour_container = ttk.Frame(time_selection_frame)
        hour_container.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(hour_container, text="Hour:", font=("", 9, "bold")).pack()
        hour_spinbox = tk.Spinbox(
            hour_container,
            from_=0, to=23, width=4,
            textvariable=self.upload_hour,
            justify='center',
            font=("", 12),
            command=self.update_time_display,
            wrap=True
        )
        hour_spinbox.pack()
        hour_spinbox.bind('<KeyRelease>', self.on_time_change)
        hour_spinbox.bind('<FocusOut>', self.on_time_change)

        # Separator
        ttk.Label(time_selection_frame, text=":", font=("", 16, "bold")).pack(side=tk.LEFT, padx=5)

        # Minute selection
        minute_container = ttk.Frame(time_selection_frame)
        minute_container.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(minute_container, text="Minute:", font=("", 9, "bold")).pack()
        minute_spinbox = tk.Spinbox(
            minute_container,
            from_=0, to=59, width=4,
            textvariable=self.upload_minute,
            justify='center',
            font=("", 12),
            command=self.update_time_display,
            wrap=True
        )
        minute_spinbox.pack()
        minute_spinbox.bind('<KeyRelease>', self.on_time_change)
        minute_spinbox.bind('<FocusOut>', self.on_time_change)

        # Time display
        time_display_container = ttk.Frame(time_selection_frame)
        time_display_container.pack(side=tk.LEFT, padx=20)

        ttk.Label(time_display_container, text="Selected Time:", font=("", 9, "bold")).pack()
        self.time_display_label = ttk.Label(
            time_display_container,
            text="02:00",
            font=("", 14, "bold"),
            foreground="blue"
        )
        self.time_display_label.pack()

        # Preset time buttons
        preset_frame = ttk.Frame(time_frame)
        preset_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(preset_frame, text="Quick Select:", font=("", 9, "bold")).pack(anchor=tk.W)

        preset_buttons_frame = ttk.Frame(preset_frame)
        preset_buttons_frame.pack(fill=tk.X, pady=(5, 0))

        # Common preset times
        presets = [
            ("12:00 AM", 0, 0),
            ("2:00 AM", 2, 0),
            ("6:00 AM", 6, 0),
            ("12:00 PM", 12, 0),
            ("6:00 PM", 18, 0),
            ("11:00 PM", 23, 0)
        ]

        for i, (label, hour, minute) in enumerate(presets):
            btn = ttk.Button(
                preset_buttons_frame,
                text=label,
                width=8,
                command=lambda h=hour, m=minute: self.set_preset_time(h, m)
            )
            btn.pack(side=tk.LEFT, padx=(0, 5))

        # Store spinbox references for validation
        self.hour_spinbox = hour_spinbox
        self.minute_spinbox = minute_spinbox

        # Upload service selection
        ttk.Label(schedule_frame, text="Upload Service:").grid(row=3, column=0, sticky="w", pady=2)
        service_combo = ttk.Combobox(schedule_frame, textvariable=self.upload_service, width=15, state="readonly")
        service_combo['values'] = ("gdrive", "mega")
        service_combo.grid(row=3, column=1, sticky="w", pady=2)

        # Schedule information
        info_text = (
            "Scheduled uploads will automatically backup your database at the specified time each day.\n\n"
            "The hidden client must be running for scheduled uploads to work.\n\n"
            "Choose between Google Drive or MEGA for the scheduled uploads.\n\n"
            "Note: This operates independently of server-triggered uploads."
        )

        info_label = ttk.Label(schedule_frame, text=info_text, wraplength=500, justify="left")
        info_label.grid(row=4, column=0, columnspan=3, sticky="w", pady=(20, 5))

        # Configure grid columns to expand
        server_frame.columnconfigure(1, weight=1)
        db_frame.columnconfigure(1, weight=1)
        mega_frame.columnconfigure(1, weight=1)
        gdrive_frame.columnconfigure(1, weight=1)
        schedule_frame.columnconfigure(1, weight=1)
        status_frame.columnconfigure(1, weight=1)

    def browse_db_path(self):
        """Browse for database file"""
        file_path = filedialog.askopenfilename(
            title="Select Database File",
            filetypes=[("Firebird Database", "*.fdb"), ("All Files", "*.*")]
        )
        if file_path:
            self.db_path.set(file_path)

    def browse_isql_path(self):
        """Browse for ISQL executable"""
        file_path = filedialog.askopenfilename(
            title="Select ISQL Executable",
            filetypes=[("Executable", "*.exe"), ("All Files", "*.*")]
        )
        if file_path:
            self.isql_path.set(file_path)

    def browse_gdrive_credentials(self):
        """Browse for Google Drive credentials file"""
        file_path = filedialog.askopenfilename(
            title="Select Google Drive Credentials File",
            filetypes=[("JSON Files", "*.json"), ("All Files", "*.*")]
        )
        if file_path:
            self.gdrive_credentials_file.set(file_path)

    def test_server_connection(self):
        """Test connection to server"""
        address = self.server_address.get()
        try:
            port = int(self.server_port.get())
        except:
            messagebox.showerror("Error", "Invalid port number")
            return

        # Update status
        self.server_status_label.config(text="Testing...", foreground="blue")
        self.root.update_idletasks()

        # Run test in a separate thread to avoid freezing UI
        def run_test():
            try:
                # Try to connect to server
                s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                s.settimeout(5)
                result = s.connect_ex((address, port))
                s.close()

                if result == 0:
                    # Connection successful
                    self.root.after(0, lambda: self.server_status_label.config(
                        text="Connected successfully", foreground="green"))
                else:
                    # Connection failed
                    self.root.after(0, lambda: self.server_status_label.config(
                        text=f"Connection failed (Error {result})", foreground="red"))
            except Exception as e:
                # Error occurred
                self.root.after(0, lambda: self.server_status_label.config(
                    text=f"Error: {str(e)}", foreground="red"))

        threading.Thread(target=run_test, daemon=True).start()

    def test_db_connection(self):
        """Test connection to database"""
        # Update status
        self.db_status_label.config(text="Testing...", foreground="blue")
        self.root.update_idletasks()

        # Run test in a separate thread to avoid freezing UI
        def run_test():
            try:
                # Log the parameters we're using
                logger.info(f"Testing database connection with:")
                logger.info(f"  DB Path: {self.db_path.get()}")
                logger.info(f"  Username: {self.db_username.get()}")
                logger.info(f"  ISQL Path: {self.isql_path.get()}")
                logger.info(f"  Use localhost: {self.use_localhost.get()}")

                # Create connector
                connector = FirebirdConnector(
                    db_path=self.db_path.get(),
                    username=self.db_username.get(),
                    password=self.db_password.get(),
                    isql_path=self.isql_path.get(),
                    use_localhost=self.use_localhost.get()
                )

                # Test connection
                try:
                    # First try the test_connection method
                    if hasattr(connector, 'test_connection') and callable(connector.test_connection):
                        result = connector.test_connection()
                        logger.info(f"test_connection result: {result}")
                        
                        # Handle both tuple and boolean return types
                        if isinstance(result, tuple):
                            success, message = result
                        elif isinstance(result, bool):
                            success = result
                            message = "Connection successful" if success else "Connection failed"
                        else:
                            success = bool(result)
                            message = "Connection test completed"
                    else:
                        # If test_connection doesn't exist, try execute_query
                        result = connector.execute_query("SELECT 'Connection Test' FROM RDB$DATABASE")
                        success = True
                        message = "Connection successful"
                        logger.info(f"execute_query result: {result}")
                except Exception as conn_error:
                    logger.error(f"Connection test error: {conn_error}")
                    success = False
                    message = str(conn_error)

                if success:
                    # Connection successful
                    self.root.after(0, lambda: self.db_status_label.config(
                        text="Connected successfully", foreground="green"))
                else:
                    # Connection failed
                    self.root.after(0, lambda: self.db_status_label.config(
                        text=f"Connection failed: {message}", foreground="red"))
            except Exception as e:
                # Error occurred
                logger.error(f"Error in test_db_connection: {e}")
                logger.error(traceback.format_exc())
                self.root.after(0, lambda: self.db_status_label.config(
                    text=f"Error: {str(e)}", foreground="red"))

        threading.Thread(target=run_test, daemon=True).start()

    def initialize_token_downloader(self):
        """Initialize token_downloader in the background"""
        def run_init():
            try:
                logger.info("Initializing token_downloader in the background")

                # Try to import token_downloader
                try:
                    import token_downloader
                    logger.info("token_downloader module imported successfully")

                    # Try to get token from Google Drive
                    try:
                        logger.info("Getting token from Google Drive in the background")
                        token_data = token_downloader.get_token()
                        if token_data:
                            logger.info("Token retrieved successfully from Google Drive")

                            # Update credentials file path to point to token.json
                            token_file = os.path.join(BASE_DIR, "token.json")
                            self.gdrive_credentials_file.set(token_file)
                            logger.info(f"Updated credentials file path to: {token_file}")
                        else:
                            logger.warning("Failed to get token from Google Drive in the background")
                    except Exception as token_error:
                        logger.error(f"Error getting token from Google Drive: {token_error}")
                        logger.error(traceback.format_exc())
                except ImportError:
                    logger.warning("token_downloader module not found, skipping background initialization")
            except Exception as e:
                logger.error(f"Error initializing token_downloader: {e}")
                logger.error(traceback.format_exc())

        # Run initialization in a separate thread
        threading.Thread(target=run_init, daemon=True).start()

    def test_mega_connection(self):
        """Test connection to MEGA cloud storage"""
        # Update status
        self.mega_status_label.config(text="Testing...", foreground="blue")
        self.root.update_idletasks()

        # Run test in a separate thread to avoid freezing UI
        def run_test():
            try:
                # Log the parameters we're using
                logger.info(f"Testing MEGA connection with:")
                logger.info(f"  Email: {self.mega_email.get()}")

                # Check for Python version compatibility with MEGA library
                if sys.version_info.major == 3 and sys.version_info.minor >= 13:
                    logger.error("MEGA library is not compatible with Python 3.13+")
                    self.root.after(0, lambda: self.mega_status_label.config(
                        text="Error: MEGA library is not compatible with Python 3.13+", foreground="red"))

                    # Show additional information about the error
                    messagebox.showinfo(
                        "MEGA Compatibility Issue",
                        "The MEGA library is not compatible with Python 3.13+ due to the removal of 'asyncio.coroutine'.\n\n"
                        "To use MEGA functionality, you need to either:\n"
                        "1. Use Python 3.10 or 3.11 instead of 3.13\n"
                        "2. Wait for the MEGA library to be updated\n\n"
                        "Your credentials have been saved and will work when using a compatible Python version."
                    )
                    return

                # Try to import MEGA library
                try:
                    from mega import Mega
                except ImportError:
                    logger.error("Failed to import MEGA library. Try installing with: pip install mega.py")
                    self.root.after(0, lambda: self.mega_status_label.config(
                        text="Error: MEGA library not installed", foreground="red"))
                    return
                except Exception as e:
                    logger.error(f"Error importing MEGA library: {e}")
                    self.root.after(0, lambda: self.mega_status_label.config(
                        text=f"Error: MEGA library not compatible with Python {sys.version_info.major}.{sys.version_info.minor}", foreground="red"))
                    return

                # Initialize MEGA client
                try:
                    mega = Mega()

                    # Try to login
                    m = mega.login(self.mega_email.get(), self.mega_password.get())

                    if m:
                        # Get user details
                        try:
                            user_details = m.get_user()
                            logger.info(f"Logged in as: {user_details}")
                        except Exception as e:
                            logger.warning(f"Could not get user details: {e}")

                        # Get storage space details
                        try:
                            quota = m.get_quota()
                            space_used = m.get_storage_space()
                            space_total = quota

                            # Convert to GB
                            space_used_gb = space_used / (1024.0**3)
                            space_total_gb = space_total / (1024.0**3)

                            logger.info(f"Storage: {space_used_gb:.2f} GB used of {space_total_gb:.2f} GB total")

                            # Update status with storage details
                            self.root.after(0, lambda: self.mega_status_label.config(
                                text=f"Connected successfully ({space_used_gb:.2f} GB used of {space_total_gb:.2f} GB total)",
                                foreground="green"))
                        except Exception as e:
                            logger.warning(f"Could not get storage details: {e}")
                            # Still mark as successful even if we couldn't get storage details
                            self.root.after(0, lambda: self.mega_status_label.config(
                                text="Connected successfully", foreground="green"))
                    else:
                        # Login failed
                        logger.error("Login failed, no MEGA client returned")
                        self.root.after(0, lambda: self.mega_status_label.config(
                            text="Login failed: Invalid credentials", foreground="red"))
                except Exception as e:
                    # Error occurred
                    logger.error(f"Error logging in to MEGA: {e}")
                    logger.error(traceback.format_exc())
                    self.root.after(0, lambda: self.mega_status_label.config(
                        text=f"Login failed: {str(e)}", foreground="red"))
            except Exception as e:
                # Error occurred
                logger.error(f"Error in test_mega_connection: {e}")
                logger.error(traceback.format_exc())
                self.root.after(0, lambda: self.mega_status_label.config(
                    text=f"Error: {str(e)}", foreground="red"))

        threading.Thread(target=run_test, daemon=True).start()

    def test_gdrive_connection(self):
        """Test connection to Google Drive using token from Google Drive"""
        # Update status
        self.gdrive_status_label.config(text="Testing...", foreground="blue")
        self.root.update_idletasks()

        # Run test in a separate thread to avoid freezing UI
        def run_test():
            try:
                # First, try to import token_downloader
                try:
                    # Update status
                    self.root.after(0, lambda: self.gdrive_status_label.config(
                        text="Downloading token from Google Drive...", foreground="blue"))

                    # Try to import token_downloader
                    try:
                        import token_downloader
                        logger.info("Using token_downloader to get token from Google Drive")
                    except ImportError:
                        # Try to install token_downloader dependencies
                        logger.warning("token_downloader module not found, trying to install dependencies")
                        self.root.after(0, lambda: self.gdrive_status_label.config(
                            text="Installing dependencies...", foreground="blue"))

                        try:
                            import subprocess
                            subprocess.check_call([sys.executable, "-m", "pip", "install", "requests", "python-dateutil", "google-auth", "google-api-python-client"])
                            logger.info("Dependencies installed successfully")
                        except Exception as install_error:
                            logger.error(f"Error installing dependencies: {install_error}")
                            self.root.after(0, lambda: self.gdrive_status_label.config(
                                text=f"Error installing dependencies: {str(install_error)}", foreground="red"))
                            return

                    # Get token from Google Drive using token_downloader
                    self.root.after(0, lambda: self.gdrive_status_label.config(
                        text="Getting token from Google Drive...", foreground="blue"))

                    # Get the token
                    token_data = token_downloader.get_token()
                    if not token_data:
                        logger.error("Failed to get token from Google Drive")
                        self.root.after(0, lambda: self.gdrive_status_label.config(
                            text="Error: Failed to get token from Google Drive", foreground="red"))
                        return

                    logger.info("Token retrieved successfully from Google Drive")
                    self.root.after(0, lambda: self.gdrive_status_label.config(
                        text="Token retrieved, testing connection...", foreground="blue"))

                    # Now test connection using the retrieved token
                    try:
                        # Try to import Google Drive libraries
                        try:
                            from googleapiclient.discovery import build
                            from google.auth.transport.requests import Request
                            from google.oauth2.credentials import Credentials
                        except ImportError:
                            logger.error("Failed to import Google Drive libraries. Try installing with: pip install google-auth google-auth-oauthlib google-api-python-client")
                            self.root.after(0, lambda: self.gdrive_status_label.config(
                                text="Error: Google Drive libraries not installed", foreground="red"))
                            return

                        # Create credentials from token data
                        creds = Credentials.from_authorized_user_info(
                            token_data,
                            ['https://www.googleapis.com/auth/drive.file']
                        )

                        # Build the service
                        logger.info("Building Google Drive API service...")
                        service = build('drive', 'v3', credentials=creds)
                        logger.info("Google Drive API service built successfully")

                        # Get account info
                        about = service.about().get(fields="user,storageQuota").execute()
                        user_info = about.get('user', {})
                        storage_info = about.get('storageQuota', {})

                        # Extract user and storage details
                        display_name = user_info.get('displayName', 'Unknown')
                        email_address = user_info.get('emailAddress', 'Unknown')

                        # Storage info in bytes
                        usage = int(storage_info.get('usage', 0))
                        limit = int(storage_info.get('limit', 0))

                        # Convert to GB
                        usage_gb = usage / (1024.0**3)
                        limit_gb = limit / (1024.0**3) if limit > 0 else 0

                        logger.info(f"Logged in as: {display_name} ({email_address})")
                        if limit > 0:
                            logger.info(f"Storage: {usage_gb:.2f} GB used of {limit_gb:.2f} GB total")
                            self.root.after(0, lambda: self.gdrive_status_label.config(
                                text=f"Connected as {display_name} ({usage_gb:.2f} GB used of {limit_gb:.2f} GB total)",
                                foreground="green"))
                        else:
                            logger.info(f"Storage: {usage_gb:.2f} GB used")
                            self.root.after(0, lambda: self.gdrive_status_label.config(
                                text=f"Connected as {display_name} ({usage_gb:.2f} GB used)",
                                foreground="green"))

                        # Update credentials file path to point to token.json
                        token_file = os.path.join(BASE_DIR, "token.json")
                        self.gdrive_credentials_file.set(token_file)
                        logger.info(f"Updated credentials file path to: {token_file}")

                    except Exception as oauth_error:
                        logger.error(f"Error testing OAuth connection: {oauth_error}")
                        logger.error(traceback.format_exc())
                        self.root.after(0, lambda: self.gdrive_status_label.config(
                            text=f"Error testing OAuth connection: {str(oauth_error)}", foreground="red"))

                except Exception as token_error:
                    logger.error(f"Error with token_downloader: {token_error}")
                    logger.error(traceback.format_exc())

                    # Fall back to service account method if token_downloader fails
                    self.root.after(0, lambda: self.gdrive_status_label.config(
                        text="Token download failed, trying service account...", foreground="blue"))

                    # Log the parameters we're using
                    logger.info(f"Testing Google Drive connection with service account:")
                    logger.info(f"  Credentials file: {self.gdrive_credentials_file.get()}")

                    # Check if credentials file exists
                    credentials_file = self.gdrive_credentials_file.get()
                    if not credentials_file:
                        logger.error("No credentials file specified")
                        self.root.after(0, lambda: self.gdrive_status_label.config(
                            text="Error: No credentials file specified", foreground="red"))
                        return

                    if not os.path.exists(credentials_file):
                        logger.error(f"Credentials file not found: {credentials_file}")
                        self.root.after(0, lambda: self.gdrive_status_label.config(
                            text=f"Error: Credentials file not found", foreground="red"))
                        return

                    # Try to import Google Drive libraries
                    try:
                        from googleapiclient.discovery import build
                        from google.oauth2 import service_account
                    except ImportError:
                        logger.error("Failed to import Google Drive libraries. Try installing with: pip install google-auth google-api-python-client")
                        self.root.after(0, lambda: self.gdrive_status_label.config(
                            text="Error: Google Drive libraries not installed", foreground="red"))
                        return

                    # Define the scopes
                    SCOPES = ['https://www.googleapis.com/auth/drive.file']

                    # Try to authenticate with service account
                    try:
                        logger.info(f"Loading service account credentials from: {credentials_file}")

                        # Create credentials from service account file
                        creds = service_account.Credentials.from_service_account_file(
                            credentials_file,
                            scopes=SCOPES
                        )

                        logger.info("Service account credentials loaded successfully")

                        # Build the service
                        logger.info("Building Google Drive API service...")
                        try:
                            service = build('drive', 'v3', credentials=creds)
                            logger.info("Google Drive API service built successfully")

                            # Get account info
                            about = service.about().get(fields="user,storageQuota").execute()
                            user_info = about.get('user', {})
                            storage_info = about.get('storageQuota', {})

                            # Extract user and storage details
                            display_name = user_info.get('displayName', 'Unknown')
                            email_address = user_info.get('emailAddress', 'Unknown')

                            # Storage info in bytes
                            usage = int(storage_info.get('usage', 0))
                            limit = int(storage_info.get('limit', 0))

                            # Convert to GB
                            usage_gb = usage / (1024.0**3)
                            limit_gb = limit / (1024.0**3) if limit > 0 else 0

                            logger.info(f"Logged in as: {display_name} ({email_address})")
                            if limit > 0:
                                logger.info(f"Storage: {usage_gb:.2f} GB used of {limit_gb:.2f} GB total")
                                self.root.after(0, lambda: self.gdrive_status_label.config(
                                    text=f"Connected as {display_name} ({usage_gb:.2f} GB used of {limit_gb:.2f} GB total)",
                                    foreground="green"))
                            else:
                                logger.info(f"Storage: {usage_gb:.2f} GB used")
                                self.root.after(0, lambda: self.gdrive_status_label.config(
                                    text=f"Connected as {display_name} ({usage_gb:.2f} GB used)",
                                    foreground="green"))
                        except Exception as e:
                            logger.error(f"Error building service: {e}")
                            logger.error(traceback.format_exc())
                            self.root.after(0, lambda: self.gdrive_status_label.config(
                                text=f"Error building service: {str(e)}", foreground="red"))
                    except Exception as e:
                        logger.error(f"Error authenticating with Google Drive: {e}")
                        logger.error(traceback.format_exc())
                        self.root.after(0, lambda: self.gdrive_status_label.config(
                            text=f"Authentication error: {str(e)}", foreground="red"))
            except Exception as e:
                logger.error(f"Error in test_gdrive_connection: {e}")
                logger.error(traceback.format_exc())
                self.root.after(0, lambda: self.gdrive_status_label.config(
                    text=f"Error: {str(e)}", foreground="red"))

        threading.Thread(target=run_test, daemon=True).start()

    def load_config(self):
        """Load configuration from file, prioritizing OAuth config"""
        try:
            config = None
            config_source = None
            
            # First try to load OAuth config (contains Google Drive credentials)
            if os.path.exists(OAUTH_CONFIG_FILE):
                try:
                    with open(OAUTH_CONFIG_FILE, 'r') as f:
                        config = json.load(f)
                    config_source = OAUTH_CONFIG_FILE
                    logger.info(f"Configuration loaded from OAuth config: {OAUTH_CONFIG_FILE}")
                except json.JSONDecodeError as e:
                    logger.error(f"Error parsing OAuth config file: {e}")
                    
            # Fall back to regular config file
            if not config and os.path.exists(CONFIG_FILE):
                try:
                    with open(CONFIG_FILE, 'r') as f:
                        config = json.load(f)
                    config_source = CONFIG_FILE
                    logger.info(f"Configuration loaded from basic config: {CONFIG_FILE}")
                except json.JSONDecodeError as e:
                    logger.error(f"Error parsing config file: {e}")
            
            if config:
                # Load server settings
                self.server_address.set(config.get('server_address', 'localhost'))
                self.server_port.set(config.get('server_port', DEFAULT_PORT))
                self.client_id.set(config.get('client_id', self.client_id.get()))
                self.display_name.set(config.get('display_name', self.display_name.get()))
                self.reconnect_interval.set(config.get('reconnect_interval', 5))

                # Load database settings
                db_config = config.get('database', {})
                self.db_path.set(db_config.get('path', ''))
                self.db_username.set(db_config.get('username', 'SYSDBA'))
                self.db_password.set(db_config.get('password', 'masterkey'))
                self.isql_path.set(db_config.get('isql_path', ''))
                self.use_localhost.set(db_config.get('use_localhost', True))

                # Load MEGA settings
                mega_config = config.get('mega', {})
                if mega_config:
                    self.mega_email.set(mega_config.get('email', '<EMAIL>'))
                    self.mega_password.set(mega_config.get('password', 'ptrj@123'))
                    logger.info(f"Loaded MEGA credentials for: {self.mega_email.get()}")

                # Load Google Drive settings - handle both old and new format
                gdrive_config = config.get('gdrive', {})
                if gdrive_config:
                    # Check for token file setting (new format)
                    token_file = gdrive_config.get('token_file', '')
                    if token_file:
                        # Convert relative path to absolute for display
                        if not os.path.isabs(token_file):
                            token_file = os.path.join(BASE_DIR, token_file)
                        self.gdrive_credentials_file.set(token_file)
                        logger.info(f"Loaded Google Drive token file: {token_file}")
                    else:
                        # Fall back to old credentials_file setting
                        credentials_file = gdrive_config.get('credentials_file', '')
                        if credentials_file:
                            self.gdrive_credentials_file.set(credentials_file)
                            logger.info(f"Loaded Google Drive credentials file: {credentials_file}")

                # Load scheduled upload settings (ensure defaults are set)
                schedule_config = config.get('scheduled_upload', {})
                self.enable_scheduled_upload.set(schedule_config.get('enabled', False))
                self.upload_hour.set(schedule_config.get('hour', 2))
                self.upload_minute.set(schedule_config.get('minute', 0))
                self.upload_service.set(schedule_config.get('service', 'gdrive'))
                logger.info(f"Loaded scheduled upload settings: enabled={self.enable_scheduled_upload.get()}, time={self.upload_hour.get()}:{self.upload_minute.get():02d}, service={self.upload_service.get()}")

                # Update time display
                self.update_time_display()

                # Update status
                self.config_status_label.config(
                    text=f"Loaded from {os.path.abspath(config_source)}",
                    foreground="green"
                )

                logger.info(f"Configuration loaded from {config_source}")
            else:
                # Config file doesn't exist
                self.config_status_label.config(
                    text="Configuration file not found, will create new one",
                    foreground="blue"
                )
                logger.info("Configuration file not found, will create new one")
        except Exception as e:
            # Error loading config
            self.config_status_label.config(
                text=f"Error loading configuration: {str(e)}",
                foreground="red"
            )
            logger.error(f"Error loading configuration: {e}")
            logger.error(traceback.format_exc())

    def save_config(self):
        """Save configuration to file"""
        try:
            # Create config dictionary
            config = {
                'server_address': self.server_address.get(),
                'server_port': self.server_port.get(),
                'client_id': self.client_id.get(),
                'display_name': self.display_name.get(),
                'reconnect_interval': self.reconnect_interval.get(),
                'database': {
                    'path': self.db_path.get(),
                    'username': self.db_username.get(),
                    'password': self.db_password.get(),
                    'isql_path': self.isql_path.get(),
                    'use_localhost': self.use_localhost.get()
                },
                'mega': {
                    'email': self.mega_email.get(),
                    'password': self.mega_password.get()
                },
                'gdrive': {
                    'credentials_file': self.gdrive_credentials_file.get()
                },
                'scheduled_upload': {
                    'enabled': self.enable_scheduled_upload.get(),
                    'hour': self.upload_hour.get(),
                    'minute': self.upload_minute.get(),
                    'service': self.upload_service.get()
                }
            }

            # Determine which config file to use as primary
            # If OAuth config exists, use it as primary; otherwise use regular config
            primary_config_file = OAUTH_CONFIG_FILE if os.path.exists(OAUTH_CONFIG_FILE) else CONFIG_FILE
            secondary_config_file = CONFIG_FILE if primary_config_file == OAUTH_CONFIG_FILE else None

            # Save to primary config file
            if primary_config_file == OAUTH_CONFIG_FILE:
                # Load existing OAuth config to preserve other settings
                try:
                    with open(OAUTH_CONFIG_FILE, 'r') as f:
                        oauth_config = json.load(f)
                    
                    # Update with new settings
                    oauth_config.update(config)
                    config = oauth_config
                    
                    logger.info("Using OAuth config as primary configuration file")
                except Exception as e:
                    logger.warning(f"Failed to load existing OAuth config, creating new: {e}")

            with open(primary_config_file, 'w') as f:
                json.dump(config, f, indent=2)

            logger.info(f"Configuration saved to primary file: {primary_config_file}")

            # Also save to secondary config file for backward compatibility
            if secondary_config_file:
                try:
                    with open(secondary_config_file, 'w') as f:
                        json.dump(config, f, indent=2)
                    logger.info(f"Configuration also saved to secondary file: {secondary_config_file}")
                except Exception as e:
                    logger.warning(f"Failed to save to secondary config file: {e}")

            # Update status
            self.config_status_label.config(
                text=f"Saved to {os.path.abspath(primary_config_file)}",
                foreground="green"
            )

            logger.info(f"Configuration saved successfully")
            logger.info(f"Primary config file: {primary_config_file}")
            logger.info(f"MEGA credentials saved for: {self.mega_email.get()}")
            messagebox.showinfo("Success", f"Configuration saved successfully to:\n{os.path.abspath(primary_config_file)}")
            return True
        except Exception as e:
            # Error saving config
            self.config_status_label.config(
                text=f"Error saving configuration: {str(e)}",
                foreground="red"
            )
            logger.error(f"Error saving configuration: {e}")
            logger.error(traceback.format_exc())
            messagebox.showerror("Error", f"Error saving configuration: {str(e)}")
            return False

    def save_and_run(self):
        """Save configuration and run hidden client"""
        if self.save_config():
            try:
                # Check if hidden client exists
                client_exe = "ifess_client_hidden.exe"
                if not os.path.exists(client_exe):
                    messagebox.showerror("Error", f"Hidden client executable not found: {client_exe}")
                    return

                # Run hidden client
                subprocess.Popen([client_exe], creationflags=subprocess.CREATE_NO_WINDOW)

                messagebox.showinfo("Success", "Hidden client started successfully")
            except Exception as e:
                logger.error(f"Error starting hidden client: {e}")
                logger.error(traceback.format_exc())
                messagebox.showerror("Error", f"Error starting hidden client: {str(e)}")

    def update_time_display(self):
        """Update the time display in the GUI"""
        try:
            hour = int(self.upload_hour.get())
            minute = int(self.upload_minute.get())

            # Validate values
            if 0 <= hour <= 23 and 0 <= minute <= 59:
                selected_time = f"{hour:02d}:{minute:02d}"
                self.time_display_label.config(text=selected_time, foreground="blue")
            else:
                self.time_display_label.config(text="Invalid", foreground="red")
        except (ValueError, TypeError):
            self.time_display_label.config(text="Invalid", foreground="red")

    def on_time_change(self, event=None):
        """Handle changes to the time spinboxes"""
        self.root.after(100, self.update_time_display)  # Delay to allow value to update

    def set_preset_time(self, hour, minute):
        """Set preset time values"""
        try:
            self.upload_hour.set(hour)
            self.upload_minute.set(minute)
            self.update_time_display()
            logger.info(f"Set preset time to {hour:02d}:{minute:02d}")
        except Exception as e:
            logger.error(f"Error setting preset time: {e}")
            messagebox.showerror("Error", f"Error setting preset time: {str(e)}")

def main():
    """Main function"""
    root = tk.Tk()
    app = ConfigApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
