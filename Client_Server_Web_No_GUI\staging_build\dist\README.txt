IFESS Client Suite - Standalone Distribution 
============================================== 
Built on: 06/06/2025 19:59:55,93 
 
This package contains two standalone IFESS applications and one debug tool: 
 
1. ifess_client_hidden.exe - Main background service client 
   - Run with: run_hidden_client.bat 
   - Runs silently in background 
 
2. debug_hidden_client.bat - Debug mode for hidden client 
   - Run with: debug_hidden_client.bat 
   - Shows console output and error messages for troubleshooting 
 
3. ifess_config_gui.exe - Configuration GUI 
   - Run with: run_config_gui.bat 
   - Configure database and server settings 
 
CREDENTIAL FILES INCLUDED: 
- token.json: Google Drive authentication token 
- ptrj-backup-services-account.json: Service account credentials 
- client_secrets.json: OAuth client secrets 
- client_config_oauth_tokens.json: Full configuration with credentials 
 
All executables are completely standalone and portable. 
Credential files are bundled and configured for immediate use. 
No additional authentication setup required. 
No Python installation or additional dependencies required. 
