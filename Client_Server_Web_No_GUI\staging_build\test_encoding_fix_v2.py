#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script to verify encoding fix works - better simulation of PyInstaller conditions
"""

import sys
import os

class MockStdout:
    """Mock stdout object that simulates PyInstaller conditions"""
    def __init__(self, has_encoding=True, encoding_value=None, has_buffer=True):
        if has_encoding:
            self.encoding = encoding_value  # Can be None to simulate PyInstaller
        if has_buffer:
            self.buffer = sys.stdout.buffer if hasattr(sys.stdout, 'buffer') else None
        
    def write(self, text):
        print(text, end='')
        
    def flush(self):
        pass

class MockStderr:
    """Mock stderr object that simulates PyInstaller conditions"""
    def __init__(self, has_encoding=True, encoding_value=None, has_buffer=True):
        if has_encoding:
            self.encoding = encoding_value  # Can be None to simulate PyInstaller
        if has_buffer:
            self.buffer = sys.stderr.buffer if hasattr(sys.stderr, 'buffer') else None
        
    def write(self, text):
        print(text, end='', file=sys.stderr)
        
    def flush(self):
        pass

def test_encoding_fix_logic(mock_stdout, mock_stderr):
    """Test the encoding fix logic with mock objects"""
    print(f"Testing with stdout.encoding = {getattr(mock_stdout, 'encoding', 'NO_ATTR')}")
    print(f"Testing with stderr.encoding = {getattr(mock_stderr, 'encoding', 'NO_ATTR')}")
    
    try:
        # This is the exact same logic from the fixed ifess_client_hidden.py
        if hasattr(mock_stdout, 'encoding') and mock_stdout.encoding is not None:
            if mock_stdout.encoding.lower() != 'utf-8':
                print("Would apply stdout encoding fix (condition 1)")
        elif hasattr(mock_stdout, 'buffer'):  # Fallback for compiled executables
            print("Would apply stdout encoding fix (condition 2 - PyInstaller fallback)")
            
        if hasattr(mock_stderr, 'encoding') and mock_stderr.encoding is not None:
            if mock_stderr.encoding.lower() != 'utf-8':
                print("Would apply stderr encoding fix (condition 1)")
        elif hasattr(mock_stderr, 'buffer'):  # Fallback for compiled executables
            print("Would apply stderr encoding fix (condition 2 - PyInstaller fallback)")
            
        return True
        
    except AttributeError as e:
        print(f"❌ AttributeError occurred: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error occurred: {e}")
        return False

def run_all_tests():
    """Run all test scenarios"""
    print("Testing IFESS Client Encoding Fix Logic")
    print("=" * 60)
    
    test_results = []
    
    # Test 1: Normal conditions (encoding exists and is utf-8)
    print("\n1. Testing Normal Conditions (encoding='utf-8'):")
    mock_stdout = MockStdout(has_encoding=True, encoding_value='utf-8', has_buffer=True)
    mock_stderr = MockStderr(has_encoding=True, encoding_value='utf-8', has_buffer=True) 
    result1 = test_encoding_fix_logic(mock_stdout, mock_stderr)
    test_results.append(("Normal conditions", result1))
    
    # Test 2: PyInstaller conditions (encoding is None but buffer exists)
    print("\n2. Testing PyInstaller Conditions (encoding=None, buffer exists):")
    mock_stdout = MockStdout(has_encoding=True, encoding_value=None, has_buffer=True)
    mock_stderr = MockStderr(has_encoding=True, encoding_value=None, has_buffer=True)
    result2 = test_encoding_fix_logic(mock_stdout, mock_stderr)
    test_results.append(("PyInstaller conditions", result2))
    
    # Test 3: No encoding attribute at all
    print("\n3. Testing No Encoding Attribute:")
    mock_stdout = MockStdout(has_encoding=False, encoding_value=None, has_buffer=True)
    mock_stderr = MockStderr(has_encoding=False, encoding_value=None, has_buffer=True)
    result3 = test_encoding_fix_logic(mock_stdout, mock_stderr)
    test_results.append(("No encoding attribute", result3))
    
    # Test 4: No buffer attribute either
    print("\n4. Testing No Buffer Attribute:")
    mock_stdout = MockStdout(has_encoding=False, encoding_value=None, has_buffer=False)
    mock_stderr = MockStderr(has_encoding=False, encoding_value=None, has_buffer=False)
    result4 = test_encoding_fix_logic(mock_stdout, mock_stderr)
    test_results.append(("No buffer attribute", result4))
    
    # Test 5: Different encoding (not utf-8)
    print("\n5. Testing Different Encoding (cp1252):")
    mock_stdout = MockStdout(has_encoding=True, encoding_value='cp1252', has_buffer=True)
    mock_stderr = MockStderr(has_encoding=True, encoding_value='cp1252', has_buffer=True)
    result5 = test_encoding_fix_logic(mock_stdout, mock_stderr)
    test_results.append(("Different encoding", result5))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST RESULTS SUMMARY:")
    all_passed = True
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("The encoding fix should prevent the AttributeError in PyInstaller!")
        return 0
    else:
        print("💥 SOME TESTS FAILED!")
        print("The fix may need additional work.")
        return 1

if __name__ == "__main__":
    sys.exit(run_all_tests()) 