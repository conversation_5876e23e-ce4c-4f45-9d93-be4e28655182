#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Configuration Manager for IFESS Client

Handles configuration loading, saving, and validation for the IFESS client system.
Supports dynamic ISQL path detection and configuration management.
"""

import os
import json
import logging
import platform
import subprocess
from datetime import datetime

class ConfigManager:
    """
    Enhanced configuration manager with automatic ISQL path detection and validation
    """
    
    def __init__(self, config_file_path=None, base_dir=None):
        """
        Initialize configuration manager
        
        :param config_file_path: Path to the configuration file
        :param base_dir: Base directory for the application
        """
        self.logger = logging.getLogger(f"{__name__}.ConfigManager")
        
        # Determine base directory
        if base_dir is None:
            if hasattr(sys, 'frozen'):
                # Running as compiled executable
                self.base_dir = os.path.dirname(sys.executable)
            else:
                # Running as script
                self.base_dir = os.path.dirname(os.path.abspath(__file__))
        else:
            self.base_dir = base_dir
            
        # Determine config file path
        if config_file_path is None:
            self.config_file = os.path.join(self.base_dir, "client_config_oauth_tokens.json")
        else:
            self.config_file = config_file_path
            
        self.config = {}
        self._load_config()
    
    def _load_config(self):
        """Load configuration from file"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                self.logger.info(f"Configuration loaded from: {self.config_file}")
            except Exception as e:
                self.logger.error(f"Error loading configuration: {e}")
                self.config = {}
        else:
            self.logger.warning(f"Configuration file not found: {self.config_file}")
            self.config = {}
            
        # Ensure required sections exist
        self._ensure_config_structure()
    
    def _ensure_config_structure(self):
        """Ensure configuration has all required sections"""
        default_structure = {
            "server_address": "localhost",
            "server_port": 5555,
            "reconnect_interval": 5,
            "client_id": f"client_{platform.node()}",
            "display_name": f"FDB-Client-{platform.node()}",
            "database": {
                "path": "",
                "username": "sysdba",
                "password": "masterkey",
                "isql_path": "",
                "use_localhost": True
            },
            "connection": {
                "exponential_backoff": {
                    "enabled": True,
                    "base_interval": 5,
                    "max_interval": 300,
                    "jitter_percent": 20
                },
                "keep_alive": True,
                "timeout": None
            },
            "query_execution": {
                "max_retries": 3,
                "timeout_seconds": 120,
                "result_format": "enhanced_json",
                "enable_type_inference": True,
                "preserve_original_headers": True
            }
        }
        
        # Merge with existing config
        for key, value in default_structure.items():
            if key not in self.config:
                self.config[key] = value
            elif isinstance(value, dict) and isinstance(self.config[key], dict):
                for subkey, subvalue in value.items():
                    if subkey not in self.config[key]:
                        self.config[key][subkey] = subvalue
    
    def detect_and_set_isql_path(self):
        """
        Detect and set ISQL path automatically
        """
        self.logger.info("Detecting ISQL path...")
        
        # Check if ISQL path is already configured and valid
        current_path = self.get_isql_path()
        if current_path and os.path.exists(current_path) and self._test_isql(current_path):
            self.logger.info(f"Using existing ISQL path: {current_path}")
            return current_path
        
        # Common ISQL installation paths
        possible_paths = [
            r'C:\Program Files (x86)\Firebird-1.5.6.5026-0_win32_Manual\bin\isql.exe',
            r'C:\Program Files (x86)\Firebird\bin\isql.exe',
            r'C:\Program Files\Firebird\Firebird_2_5\bin\isql.exe',
            r'C:\Program Files\Firebird\Firebird_3_0\bin\isql.exe',
            r'C:\Program Files\Firebird\bin\isql.exe',
            r'C:\Program Files (x86)\Firebird\Firebird_1_5\bin\isql.exe',
            r'C:\Program Files (x86)\Firebird\Firebird_2_1\bin\isql.exe',
            r'C:\Program Files (x86)\Firebird\Firebird_2_5\bin\isql.exe',
            r'C:\Program Files (x86)\Firebird\Firebird_3_0\bin\isql.exe'
        ]
        
        # Try to find working ISQL installation
        for path in possible_paths:
            if os.path.exists(path):
                self.logger.debug(f"Testing ISQL at: {path}")
                if self._test_isql(path):
                    self.logger.info(f"Found working ISQL at: {path}")
                    self.set_isql_path(path)
                    return path
                else:
                    self.logger.warning(f"ISQL found but test failed: {path}")
        
        # Try Windows registry detection
        registry_path = self._detect_isql_from_registry()
        if registry_path:
            self.logger.info(f"Found ISQL via registry: {registry_path}")
            self.set_isql_path(registry_path)
            return registry_path
        
        # Try environment variables
        env_path = self._detect_isql_from_environment()
        if env_path:
            self.logger.info(f"Found ISQL via environment: {env_path}")
            self.set_isql_path(env_path)
            return env_path
        
        self.logger.error("Could not automatically detect ISQL path")
        return None
    
    def _test_isql(self, isql_path):
        """Test if ISQL executable works"""
        try:
            result = subprocess.run([isql_path, "-h"], 
                                  capture_output=True, 
                                  text=True, 
                                  timeout=10)
            return True  # If it doesn't crash, it's probably working
        except Exception as e:
            self.logger.debug(f"ISQL test failed for {isql_path}: {e}")
            return False
    
    def _detect_isql_from_registry(self):
        """Try to detect ISQL path from Windows registry"""
        if platform.system() != 'Windows':
            return None
            
        try:
            import winreg
            
            # Common registry keys for Firebird
            registry_keys = [
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Firebird Project\Firebird Server\Instances"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Firebird Project\Firebird Server\Instances"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\FirebirdSQL\Firebird\CurrentVersion"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\FirebirdSQL\Firebird\CurrentVersion")
            ]
            
            for hkey, subkey in registry_keys:
                try:
                    with winreg.OpenKey(hkey, subkey) as key:
                        try:
                            install_path, _ = winreg.QueryValueEx(key, "DefaultInstance")
                            isql_path = os.path.join(install_path, "bin", "isql.exe")
                            if os.path.exists(isql_path) and self._test_isql(isql_path):
                                return isql_path
                        except:
                            # Try other value names
                            for value_name in ["RootDirectory", "InstallPath", "DefaultPath"]:
                                try:
                                    install_path, _ = winreg.QueryValueEx(key, value_name)
                                    isql_path = os.path.join(install_path, "bin", "isql.exe")
                                    if os.path.exists(isql_path) and self._test_isql(isql_path):
                                        return isql_path
                                except:
                                    continue
                except:
                    continue
                    
        except ImportError:
            self.logger.debug("winreg not available - cannot check registry")
        except Exception as e:
            self.logger.debug(f"Registry detection failed: {e}")
            
        return None
    
    def _detect_isql_from_environment(self):
        """Try to detect ISQL from environment variables"""
        # Check PATH environment variable
        path_env = os.environ.get('PATH', '')
        for path_dir in path_env.split(os.pathsep):
            isql_path = os.path.join(path_dir, "isql.exe")
            if os.path.exists(isql_path) and self._test_isql(isql_path):
                return isql_path
        
        # Check common environment variables
        env_vars = ['FIREBIRD_HOME', 'FB_HOME', 'FIREBIRD']
        for env_var in env_vars:
            fb_home = os.environ.get(env_var)
            if fb_home:
                isql_path = os.path.join(fb_home, "bin", "isql.exe")
                if os.path.exists(isql_path) and self._test_isql(isql_path):
                    return isql_path
        
        return None
    
    def get_isql_path(self):
        """Get configured ISQL path"""
        return self.config.get('database', {}).get('isql_path', '')
    
    def set_isql_path(self, path):
        """Set ISQL path in configuration"""
        if 'database' not in self.config:
            self.config['database'] = {}
        self.config['database']['isql_path'] = path
        self.save_config()
    
    def get_database_config(self):
        """Get database configuration"""
        return self.config.get('database', {})
    
    def set_database_config(self, db_path=None, username=None, password=None, use_localhost=None):
        """Set database configuration"""
        if 'database' not in self.config:
            self.config['database'] = {}
            
        if db_path is not None:
            self.config['database']['path'] = db_path
        if username is not None:
            self.config['database']['username'] = username
        if password is not None:
            self.config['database']['password'] = password
        if use_localhost is not None:
            self.config['database']['use_localhost'] = use_localhost
            
        self.save_config()
    
    def get_query_execution_config(self):
        """Get query execution configuration"""
        return self.config.get('query_execution', {})
    
    def save_config(self):
        """Save configuration to file"""
        try:
            # Add metadata
            self.config['_metadata'] = {
                'last_updated': datetime.now().isoformat(),
                'version': '2.0',
                'auto_detected_isql': self.get_isql_path() != ''
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Configuration saved to: {self.config_file}")
        except Exception as e:
            self.logger.error(f"Error saving configuration: {e}")
    
    def validate_config(self):
        """Validate configuration and return validation results"""
        issues = []
        
        # Check ISQL path
        isql_path = self.get_isql_path()
        if not isql_path:
            issues.append("ISQL path not configured")
        elif not os.path.exists(isql_path):
            issues.append(f"ISQL executable not found: {isql_path}")
        elif not self._test_isql(isql_path):
            issues.append(f"ISQL executable test failed: {isql_path}")
        
        # Check database path
        db_config = self.get_database_config()
        db_path = db_config.get('path', '')
        if not db_path:
            issues.append("Database path not configured")
        elif not os.path.exists(db_path):
            issues.append(f"Database file not found: {db_path}")
        
        # Check connection settings
        server_address = self.config.get('server_address', '')
        server_port = self.config.get('server_port', 0)
        if not server_address:
            issues.append("Server address not configured")
        if not server_port or not isinstance(server_port, int) or server_port <= 0:
            issues.append("Invalid server port configuration")
        
        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'config_file': self.config_file,
            'isql_path': isql_path,
            'database_path': db_path
        }
    
    def create_firebird_connector(self):
        """Create a FirebirdConnector instance with current configuration"""
        from common.db_utils import FirebirdConnector
        
        db_config = self.get_database_config()
        
        return FirebirdConnector(
            db_path=db_config.get('path'),
            username=db_config.get('username', 'sysdba'),
            password=db_config.get('password', 'masterkey'),
            isql_path=db_config.get('isql_path'),
            use_localhost=db_config.get('use_localhost', True),
            config_file=self.config_file
        )
    
    def get_config(self):
        """Get full configuration dictionary"""
        return self.config.copy()
    
    def setup_initial_config(self):
        """Setup initial configuration with auto-detection"""
        self.logger.info("Setting up initial configuration...")
        
        # Try to auto-detect ISQL
        isql_path = self.detect_and_set_isql_path()
        
        if isql_path:
            self.logger.info(f"ISQL auto-detection successful: {isql_path}")
        else:
            self.logger.warning("ISQL auto-detection failed - manual configuration required")
        
        # Save initial configuration
        self.save_config()
        
        return self.validate_config()