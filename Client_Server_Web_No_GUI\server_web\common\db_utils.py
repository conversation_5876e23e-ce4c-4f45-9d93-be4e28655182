import os
import subprocess
import json
import tempfile
import re
import logging
import traceback
from datetime import datetime

class FirebirdConnector:
    """
    Enhanced Firebird connector with JSON-based result parsing and improved error handling
    """
    def __init__(self, db_path=None, username='SYSDBA', password='masterkey', isql_path=None, use_localhost=False, config_file=None):
        """
        Inisialisasi koneksi Firebird

        :param db_path: Path lengkap ke file .fdb
        :param username: Username untuk koneksi (default: SYSDBA)
        :param password: Password untuk koneksi (default: masterkey)
        :param isql_path: Path ke executable isql.exe (default: auto-detect atau dari config)
        :param use_localhost: Jika True, gunakan format localhost:path untuk koneksi
        :param config_file: Path ke file konfigurasi untuk membaca path ISQL
        """
        self.db_path = db_path
        self.username = username
        self.password = password
        self.use_localhost = use_localhost
        self.config_file = config_file
        self.logger = logging.getLogger(f"{__name__}.FirebirdConnector")

        # Load ISQL path from config file if provided
        if isql_path is None:
            self.isql_path = self._load_isql_path_from_config() or self._detect_isql_path()
        else:
            self.isql_path = isql_path

        # Verify isql exists
        if not os.path.exists(self.isql_path):
            raise FileNotFoundError(f"isql.exe tidak ditemukan di: {self.isql_path}")

    def _load_isql_path_from_config(self):
        """Load ISQL path from configuration file"""
        if not self.config_file or not os.path.exists(self.config_file):
            return None
            
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            # Try different possible config structures
            isql_path = None
            if 'database' in config and 'isql_path' in config['database']:
                isql_path = config['database']['isql_path']
            elif 'isql_path' in config:
                isql_path = config['isql_path']
                
            if isql_path and os.path.exists(isql_path):
                self.logger.info(f"Loaded ISQL path from config: {isql_path}")
                return isql_path
                
        except Exception as e:
            self.logger.warning(f"Could not load ISQL path from config {self.config_file}: {e}")
            
        return None

    def _detect_isql_path(self):
        """Deteksi otomatis lokasi isql.exe"""
        default_paths = [
            r'C:\Program Files (x86)\Firebird-1.5.6.5026-0_win32_Manual\bin\isql.exe',
            r'C:\Program Files (x86)\Firebird\bin\isql.exe',
            r'C:\Program Files\Firebird\Firebird_2_5\bin\isql.exe',
            r'C:\Program Files\Firebird\Firebird_3_0\bin\isql.exe',
            r'C:\Program Files\Firebird\bin\isql.exe',
            r'C:\Program Files (x86)\Firebird\Firebird_1_5\bin\isql.exe'
        ]

        for path in default_paths:
            if os.path.exists(path):
                # Verify that the ISQL is working
                if self.test_isql(path):
                    return path
                self.logger.warning(f"Found ISQL at {path} but test failed")

        raise FileNotFoundError("Tidak dapat menemukan isql.exe yang berfungsi. Harap tentukan path secara manual di konfigurasi.")

    def test_isql(self, isql_path):
        """Test apakah ISQL dapat dijalankan"""
        try:
            self.logger.debug(f"Testing ISQL at: {isql_path}")

            # Try to run isql with a simple help command
            result = subprocess.run([isql_path, "-h"],
                                   capture_output=True,
                                   text=True,
                                   timeout=10)

            self.logger.debug(f"ISQL test successful. Return code: {result.returncode}")
            return True
        except subprocess.TimeoutExpired:
            self.logger.debug(f"ISQL test timed out but executable exists. Assuming it works.")
            return True
        except Exception as e:
            self.logger.warning(f"ISQL test failed: {e}")

            # Even if the test command failed, check if the file exists and is executable
            if os.path.exists(isql_path) and os.access(isql_path, os.X_OK):
                self.logger.debug(f"ISQL exists and appears to be executable, proceeding anyway")
                return True

            return False

    def execute_query(self, query, params=None, as_dict=True):
        """
        Execute SQL query with enhanced JSON-based result parsing
        
        :param query: Query SQL yang akan dijalankan
        :param params: Parameter untuk query (not used in current implementation)
        :param as_dict: Jika True, hasil dikembalikan sebagai list dari dictionaries
        :return: Enhanced result structure with complete data integrity
        """
        # Create temporary files for SQL script and output
        fd, sql_path = tempfile.mkstemp(suffix='.sql')
        output_fd, output_path = tempfile.mkstemp(suffix='.txt')

        try:
            # Validate inputs
            if not os.path.exists(self.isql_path):
                raise FileNotFoundError(f"ISQL executable tidak ditemukan: {self.isql_path}")

            if not os.path.exists(self.db_path):
                raise FileNotFoundError(f"File database tidak ditemukan: {self.db_path}")

            # Create connection string
            connection_string = f"localhost:{self.db_path}" if self.use_localhost else self.db_path

            # Write SQL script with enhanced output formatting
            with os.fdopen(fd, 'w') as sql_file:
                # Enhanced SQL script with JSON-like structured output
                sql_file.write("SET PAGESIZE 0;\n")
                sql_file.write("SET ECHO OFF;\n") 
                sql_file.write("SET HEADING ON;\n")
                sql_file.write("SET FEEDBACK ON;\n")
                sql_file.write("SET TERM ^;\n")
                sql_file.write("SET TERM ;^\n")
                sql_file.write(f"{query};\n")
                sql_file.write("COMMIT;\n")
                sql_file.write("EXIT;\n")

            self.logger.info(f"Executing enhanced query: {query[:100]}...")
            self.logger.debug(f"Database: {self.db_path}")

            # Close output file handle
            os.close(output_fd)

            # Build command with proper connection parameters
            if self.use_localhost:
                cmd = [
                    self.isql_path,
                    "-u", self.username,
                    "-p", self.password,
                    connection_string,
                    "-i", sql_path,
                    "-o", output_path
                ]
            else:
                cmd = [
                    self.isql_path,
                    "-u", self.username,
                    "-p", self.password,
                    "-d", connection_string,
                    "-i", sql_path,
                    "-o", output_path
                ]

            self.logger.debug(f"Running command: {' '.join(cmd)}")

            # Execute with timeout and error handling
            process_result = subprocess.run(cmd, check=False, capture_output=True, timeout=120)
            self.logger.debug(f"ISQL process completed with return code: {process_result.returncode}")

            # Log STDERR for debugging
            if process_result.stderr:
                self.logger.debug(f"STDERR: {process_result.stderr}")

            # Try alternative method if main command failed
            if process_result.returncode != 0:
                self.logger.warning("Primary command failed, trying alternative method...")
                alt_cmd = [self.isql_path, connection_string, "-u", self.username, "-p", self.password, "-i", sql_path]
                process_result = subprocess.run(alt_cmd, check=False, capture_output=True, timeout=120)
                self.logger.debug(f"Alternative command completed with return code: {process_result.returncode}")

            # Read and parse output
            try:
                with open(output_path, 'r', encoding='utf-8') as f:
                    output_text = f.read()
            except UnicodeDecodeError:
                # Fallback to latin-1 if UTF-8 fails
                with open(output_path, 'r', encoding='latin-1') as f:
                    output_text = f.read()

            self.logger.debug(f"Raw output length: {len(output_text)} characters")

            # Enhanced parsing with JSON structure
            result = self._parse_enhanced_output(output_text, as_dict)
            
            # Add metadata to result
            for result_set in result:
                result_set['execution_info'] = {
                    'query': query,
                    'timestamp': datetime.now().isoformat(),
                    'database': self.db_path,
                    'return_code': process_result.returncode,
                    'isql_path': self.isql_path
                }

            return result

        except subprocess.TimeoutExpired:
            self.logger.error("Query execution timed out")
            raise Exception("Query execution timed out after 120 seconds")
        except Exception as e:
            self.logger.error(f"Error executing query: {e}")
            self.logger.debug(traceback.format_exc())
            raise
        finally:
            # Cleanup temporary files
            if os.path.exists(sql_path):
                os.unlink(sql_path)
            if os.path.exists(output_path):
                os.unlink(output_path)

    def _parse_enhanced_output(self, output_text, as_dict=True):
        """
        Enhanced output parsing with better header preservation and data integrity
        """
        lines = [line.rstrip() for line in output_text.split('\n')]
        if not lines:
            return []

        self.logger.debug(f"Parsing output with {len(lines)} lines")
        
        result_data = []
        
        # Enhanced parsing strategy
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # Skip empty lines and SQL prompts
            if not line or line.startswith('SQL>') or 'rows affected' in line.lower():
                i += 1
                continue
            
            # Look for potential table header
            if i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                
                # Check if next line is a separator (===== or -----)
                if self._is_separator_line(next_line):
                    header_line = line
                    separator_line = next_line
                    
                    # Parse headers and their positions
                    headers, header_positions = self._parse_headers_enhanced(header_line, separator_line)
                    
                    if headers:
                        # Collect data rows
                        data_rows = []
                        j = i + 2
                        
                        while j < len(lines):
                            data_line = lines[j].strip()
                            
                            # Stop at empty line or next result set
                            if not data_line or self._is_separator_line(data_line):
                                break
                                
                            # Skip footer information
                            if any(keyword in data_line.lower() for keyword in ['rows selected', 'records fetched', 'sql>']):
                                break
                                
                            # Parse data row
                            row_data = self._parse_data_row_enhanced(data_line, headers, header_positions)
                            if row_data and any(v and str(v).strip() for v in row_data.values()):
                                data_rows.append(row_data)
                            
                            j += 1
                        
                        # Create result set with enhanced structure
                        if headers:
                            result_set = {
                                'headers': headers,
                                'rows': data_rows,
                                'row_count': len(data_rows),
                                'column_count': len(headers),
                                'data_types': self._infer_column_types(data_rows, headers),
                                'parsing_info': {
                                    'method': 'enhanced_separator',
                                    'header_line': header_line,
                                    'separator_line': separator_line
                                }
                            }
                            result_data.append(result_set)
                            
                        i = j
                        continue
            
            i += 1
        
        # Fallback parsing if no standard format found
        if not result_data:
            result_data = self._fallback_parsing(lines)
        
        self.logger.info(f"Parsed {len(result_data)} result sets")
        for i, rs in enumerate(result_data):
            self.logger.info(f"  Result set {i+1}: {rs.get('row_count', 0)} rows, {rs.get('column_count', 0)} columns")
            
        return result_data

    def _is_separator_line(self, line):
        """Check if line is a separator line"""
        return bool(re.match(r'^[-=]{3,}', line.strip()))

    def _parse_headers_enhanced(self, header_line, separator_line):
        """Enhanced header parsing with better position detection"""
        headers = []
        header_positions = []
        
        # Find column boundaries based on separator line
        separator_parts = re.finditer(r'[-=]+', separator_line)
        
        for match in separator_parts:
            start_pos = match.start()
            end_pos = match.end()
            
            # Extract header text for this column
            if start_pos < len(header_line):
                if end_pos <= len(header_line):
                    header_text = header_line[start_pos:end_pos].strip()
                else:
                    header_text = header_line[start_pos:].strip()
                    
                if header_text:
                    headers.append(header_text)
                    header_positions.append((start_pos, end_pos))
        
        # Fallback: split by whitespace if separator method fails
        if not headers:
            headers = header_line.split()
            # Estimate positions for fallback
            header_positions = []
            current_pos = 0
            for header in headers:
                start_pos = header_line.find(header, current_pos)
                if start_pos >= 0:
                    end_pos = start_pos + len(header)
                    header_positions.append((start_pos, end_pos))
                    current_pos = end_pos
                else:
                    header_positions.append((current_pos, current_pos + 10))
                    current_pos += 10
        
        return headers, header_positions

    def _parse_data_row_enhanced(self, data_line, headers, header_positions):
        """Enhanced data row parsing with better field extraction"""
        row_data = {}
        
        for i, (header, (start_pos, end_pos)) in enumerate(zip(headers, header_positions)):
            if start_pos < len(data_line):
                if end_pos <= len(data_line):
                    value = data_line[start_pos:end_pos].strip()
                else:
                    value = data_line[start_pos:].strip()
            else:
                value = ""
            
            # Data type conversion and cleanup
            value = self._clean_and_convert_value(value)
            row_data[header] = value
        
        return row_data

    def _clean_and_convert_value(self, value):
        """Clean and convert data values with proper type handling"""
        if not value or value.lower() in ['null', '<null>', '']:
            return None
            
        # Remove extra whitespace
        value = value.strip()
        
        # Try to convert to appropriate data type
        # Integer
        if re.match(r'^-?\d+$', value):
            return int(value)
            
        # Float
        if re.match(r'^-?\d*\.\d+$', value):
            return float(value)
            
        # Date/datetime patterns
        if re.match(r'^\d{4}-\d{2}-\d{2}', value):
            return value  # Keep as string for now
            
        return value

    def _infer_column_types(self, data_rows, headers):
        """Infer column data types from the data"""
        types = {}
        
        for header in headers:
            sample_values = [row.get(header) for row in data_rows[:10] if row.get(header) is not None]
            
            if not sample_values:
                types[header] = 'unknown'
                continue
                
            # Check predominant type
            type_counts = {}
            for value in sample_values:
                value_type = type(value).__name__
                type_counts[value_type] = type_counts.get(value_type, 0) + 1
                
            # Get most common type
            if type_counts:
                types[header] = max(type_counts.items(), key=lambda x: x[1])[0]
            else:
                types[header] = 'str'
                
        return types

    def _fallback_parsing(self, lines):
        """Fallback parsing method for non-standard output formats"""
        self.logger.info("Using fallback parsing method")
        
        # Simple pattern detection
        data_lines = [line for line in lines if line.strip() and not line.strip().startswith('SQL>')]
        
        if not data_lines:
            return []
            
        # Try to find patterns
        # Look for lines that could be headers (contain alphabetic characters)
        potential_headers = []
        for i, line in enumerate(data_lines):
            if re.search(r'[a-zA-Z]', line) and not re.match(r'^\d+\s', line.strip()):
                potential_headers.append((i, line.strip()))
        
        if potential_headers:
            # Use first potential header
            header_line = potential_headers[0][1]
            headers = header_line.split()
            
            # Find data rows (lines that start with numbers)
            data_rows = []
            for line in data_lines[potential_headers[0][0]+1:]:
                if re.match(r'^\s*\d+', line):
                    values = line.split()
                    if len(values) >= len(headers):
                        row_data = {}
                        for i, header in enumerate(headers):
                            if i < len(values):
                                row_data[header] = self._clean_and_convert_value(values[i])
                            else:
                                row_data[header] = None
                        data_rows.append(row_data)
            
            if headers and data_rows:
                return [{
                    'headers': headers,
                    'rows': data_rows,
                    'row_count': len(data_rows),
                    'column_count': len(headers),
                    'data_types': self._infer_column_types(data_rows, headers),
                    'parsing_info': {'method': 'fallback'}
                }]
        
        return []

    def test_connection(self):
        """
        Tes koneksi ke database

        :return: True jika koneksi berhasil, False jika gagal
        """
        try:
            result = self.execute_query("SELECT 'Connection Test' FROM RDB$DATABASE")
            return True
        except Exception as e:
            print(f"Kesalahan koneksi: {e}")
            return False

    def get_tables(self):
        """
        Mendapatkan daftar tabel dalam database

        :return: List tabel dalam database
        """
        query = "SELECT RDB$RELATION_NAME FROM RDB$RELATIONS WHERE RDB$SYSTEM_FLAG = 0 OR RDB$SYSTEM_FLAG IS NULL"
        result = self.execute_query(query)

        tables = []
        if result and result[0]["rows"]:
            for row in result[0]["rows"]:
                table_name = row.get(result[0]["headers"][0], "").strip()
                if table_name:
                    tables.append(table_name)

        return tables

    def get_example_query(self, table_name=None):
        """Mendapatkan contoh query untuk testing"""
        if not table_name:
            # Coba dapatkan tabel pertama dari database
            tables = self.get_tables()
            if tables:
                table_name = tables[0]
            else:
                # Default table jika tidak ada tabel
                table_name = "FFBLOADINGCROP02"

        # Buat query SELECT * FROM table LIMIT 100
        query = f"SELECT * FROM {table_name}"

        print(f"Generated example query: {query}")
        return query