import os
import sys
import socket
import json
import threading
import time
import datetime
import base64
import logging
import re
import traceback
import uuid
import struct
import zlib
import hashlib
import shutil
import subprocess  # Tambahkan import ini di bagian atas file
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, session, send_from_directory

# No longer using saved_queries_db module
# import saved_queries_db

# Add parent directory to path to import common modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# Import common modules
from common.network import NetworkMessage, send_message, receive_message, DEFAULT_PORT
from common.db_utils import FirebirdConnector
from query_coordinator import QueryCoordinator

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,  # Changed from INFO to DEBUG for more detailed logs
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('server_web.log')
    ]
)
logger = logging.getLogger('server_web')

# Add a separate logger for backup operations with a dedicated file
backup_logger = logging.getLogger('backup_operations')
backup_handler = logging.FileHandler('backup_operations.log')
backup_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
backup_logger.addHandler(backup_handler)
backup_logger.addHandler(logging.StreamHandler())  # Also log to console
backup_logger.setLevel(logging.DEBUG)

# Import robust transfer modules
# Note: Robust transfer is now handled by a separate process

# Initialize Flask app
app = Flask(__name__)
app.secret_key = 'firebird_query_server_secret_key'  # Used for flash messages and sessions

# Global variables
clients = {}  # client_id -> FirebirdClient
clients_lock = threading.Lock()
server_socket = None
running = False
accept_thread = None
heartbeat_thread = None
query_history = []
max_result_rows = 10000
default_socket_timeout = 120.0  # Increased timeout for better reliability
server_start_time = None

# Initialize query_results dictionary
query_results = {}
query_results_lock = threading.Lock()

# Initialize query variables dictionary
query_vars = {}

# Initialize query coordinator for enhanced query distribution
query_coordinator = None

# Helper functions
def format_file_size(size_bytes):
    """Format file size in human-readable format"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes/1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes/(1024*1024):.1f} MB"
    else:
        return f"{size_bytes/(1024*1024*1024):.1f} GB"

# Ensure message types are consistently defined
NetworkMessage.TYPE_DB_REQUEST = 'db_request'
NetworkMessage.TYPE_DB_INFO = 'db_info'
NetworkMessage.TYPE_DB_CHUNK = 'db_chunk'
NetworkMessage.TYPE_DB_COMPLETE = 'db_complete'
NetworkMessage.TYPE_DB_ACK = 'db_ack'
NetworkMessage.TYPE_DB_PROGRESS = 'db_progress'

# Define message types for MEGA upload
NetworkMessage.TYPE_MEGA_UPLOAD_REQUEST = 'mega_upload_request'
NetworkMessage.TYPE_MEGA_UPLOAD_ACK = 'mega_upload_ack'
NetworkMessage.TYPE_MEGA_UPLOAD_PROGRESS = 'mega_upload_progress'
NetworkMessage.TYPE_MEGA_UPLOAD_RESULT = 'mega_upload_result'
NetworkMessage.TYPE_MEGA_UPLOAD_STATUS = 'mega_upload_status'

# Define message types for GDrive upload
NetworkMessage.TYPE_GDRIVE_UPLOAD_REQUEST = 'gdrive_upload_request'
NetworkMessage.TYPE_GDRIVE_UPLOAD_ACK = 'gdrive_upload_ack'
NetworkMessage.TYPE_GDRIVE_UPLOAD_PROGRESS = 'gdrive_upload_progress'
NetworkMessage.TYPE_GDRIVE_UPLOAD_RESULT = 'gdrive_upload_result'
NetworkMessage.TYPE_GDRIVE_UPLOAD_STATUS = 'gdrive_upload_status'
NetworkMessage.TYPE_ACK = 'ack'  # General acknowledgment message type

# These are already defined above

# Load predefined queries
def load_predefined_queries():
    queries = {}
    query_descriptions = {}

    # Load from standard queries directory
    queries_dir = os.path.join(current_dir, 'queries')
    if not os.path.exists(queries_dir):
        logger.warning(f"Queries directory not found: {queries_dir}")
    else:
        load_queries_from_directory(queries_dir, queries, query_descriptions)

    # Load from user-saved queries directory
    user_queries_dir = os.path.join(current_dir, 'static', 'sql_queries')
    if not os.path.exists(user_queries_dir):
        os.makedirs(user_queries_dir, exist_ok=True)
        logger.info(f"Created user queries directory: {user_queries_dir}")
    else:
        load_queries_from_directory(user_queries_dir, queries, query_descriptions)

    return queries, query_descriptions

def load_queries_from_directory(directory, queries, query_descriptions):
    query_variables = {}  # Store variables for each query

    for filename in os.listdir(directory):
        if filename.endswith('.sql'):
            query_name = os.path.splitext(filename)[0]
            query_path = os.path.join(directory, filename)

            try:
                with open(query_path, 'r', encoding='utf-8') as f:
                    query_content = f.read()

                # Extract description from comments
                description = ""
                variables = {}
                in_variables_section = False

                for line in query_content.split('\n'):
                    line = line.strip()
                    if not line.startswith('--'):
                        continue

                    comment = line[2:].strip()

                    # Check for variables section
                    if comment == "VARIABLES_START":
                        in_variables_section = True
                        continue
                    elif comment == "VARIABLES_END":
                        in_variables_section = False
                        continue

                    # Process variable definitions
                    if in_variables_section and comment.startswith("VAR:"):
                        try:
                            # Format: VAR:name:default:description
                            parts = comment[4:].split(':', 2)
                            var_name = parts[0].strip()
                            var_default = parts[1].strip() if len(parts) > 1 else ""
                            var_description = parts[2].strip() if len(parts) > 2 else ""

                            variables[var_name] = {
                                'default': var_default,
                                'description': var_description
                            }
                        except Exception as var_error:
                            logger.warning(f"Error parsing variable in {query_name}: {var_error}")
                    elif not in_variables_section and not description:
                        # Only use the first comment line(s) as description
                        if description:
                            description += " " + comment
                        else:
                            description = comment

                queries[query_name] = query_content
                query_descriptions[query_name] = description

                # Store variables if any were found
                if variables:
                    query_variables[query_name] = variables

                logger.info(f"Loaded predefined query: {query_name} with {len(variables)} variables")
            except Exception as e:
                logger.error(f"Error loading query {query_name}: {e}")

    # Store variables in global variable
    global query_vars
    query_vars = query_variables

    return queries, query_descriptions

predefined_queries, query_descriptions = load_predefined_queries()

# Client class to store client information
class FirebirdClient:
    def __init__(self, client_id, display_name, socket, address):
        self.client_id = client_id
        self.display_name = display_name
        self.socket = socket
        self.address = address
        self.connected = True
        self.last_ping = time.time()
        self.connection_status_time = time.time()
        self.db_info = {}
        self.query_results = []
        self.db_backup_status = 'not_started'
        self.db_backup_error = None
        self.connection_attempts = 0

    def update_ping(self):
        """Update last ping time"""
        self.last_ping = time.time()
        self.connected = True
        self.connection_attempts = 0

    def mark_disconnected(self):
        """Mark client as disconnected"""
        self.connected = False
        self.connection_status_time = time.time()

    def check_connection(self):
        """Check if client is still connected"""
        if not self.connected:
            return False

        # Check if last ping is too old
        if time.time() - self.last_ping > 60:  # 60 seconds without ping means disconnected
            self.mark_disconnected()
            return False

        return True

# Server functions
def start_server(host='0.0.0.0', port=DEFAULT_PORT):
    global server_socket, running, accept_thread, heartbeat_thread, server_start_time, query_coordinator

    if running:
        logger.warning("Server is already running")
        return False

    try:
        server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

        # Set TCP_NODELAY to disable Nagle's algorithm for better responsiveness
        server_socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)

        # Set larger buffer sizes
        server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 256 * 1024)  # 256KB receive buffer
        server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 256 * 1024)  # 256KB send buffer

        server_socket.bind((host, port))
        server_socket.listen(10)  # Increased backlog for more pending connections

        running = True
        server_start_time = time.time()
        logger.info(f"Server running on {host}:{port}")

        # Initialize query coordinator for enhanced query distribution
        query_coordinator = QueryCoordinator()
        logger.info("Query coordinator initialized for enhanced query distribution")

        # Start threads for accepting connections and heartbeat
        accept_thread = threading.Thread(target=accept_connections)
        accept_thread.daemon = True
        accept_thread.start()

        heartbeat_thread = threading.Thread(target=heartbeat_clients)
        heartbeat_thread.daemon = True
        heartbeat_thread.start()

        return True
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        logger.error(traceback.format_exc())
        return False

def stop_server():
    global server_socket, running, accept_thread, heartbeat_thread, server_start_time, query_coordinator

    if not running:
        logger.warning("Server is not running")
        return False

    try:
        running = False
        server_start_time = None

        # Stop query coordinator
        if query_coordinator:
            query_coordinator.stop_coordinator()
            query_coordinator = None
            logger.info("Query coordinator stopped")

        # Close all client connections
        with clients_lock:
            for _, client in list(clients.items()):
                try:
                    client.socket.close()
                except:
                    pass
            clients.clear()

        # Close server socket
        if server_socket:
            server_socket.close()
            server_socket = None

        logger.info("Server stopped")
        return True
    except Exception as e:
        logger.error(f"Error stopping server: {e}")
        return False

def accept_connections():
    global server_socket, running

    logger.info("Starting connection acceptance thread")

    while running:
        try:
            server_socket.settimeout(1.0)  # Short timeout to check running flag
            try:
                client_socket, client_address = server_socket.accept()
                logger.info(f"New connection from {client_address}")
                threading.Thread(target=handle_client_registration, args=(client_socket, client_address)).start()
            except socket.timeout:
                continue
        except Exception as e:
            if running:
                logger.error(f"Error accepting connection: {e}")
                time.sleep(1)

    logger.info("Connection acceptance thread stopped")

def handle_client_registration(client_socket, client_address):
    try:
        # Increase timeout for registration
        client_socket.settimeout(15.0)

        # Receive registration message
        message = receive_message(client_socket)

        if not message or message.msg_type != NetworkMessage.TYPE_REGISTER:
            logger.warning(f"Registration failed from {client_address}")
            client_socket.close()
            return

        # Extract registration information
        client_info = message.data
        display_name = client_info.get('display_name', f"Client Unknown")
        db_info = client_info.get('db_info', {})

        # Use a more stable client ID based on display_name and IP address
        # This helps maintain the same client ID even if the connection is lost and reconnected
        ip_address = client_address[0]
        stable_client_id = message.client_id

        # If client didn't provide an ID, generate one based on display name and IP
        if not stable_client_id:
            # Create a stable ID based on display name and IP address
            # This ensures the same client gets the same ID even after reconnecting
            stable_client_id = f"client_{display_name}_{ip_address}".replace(' ', '_').lower()
            logger.info(f"Generated stable client ID: {stable_client_id} for {display_name} from {ip_address}")

        # Check if this client already exists but with a different socket
        existing_client = None
        with clients_lock:
            # First look for exact client ID match
            if stable_client_id in clients:
                existing_client = clients[stable_client_id]
                logger.info(f"Found existing client with ID {stable_client_id}")
            else:
                # Then look for matching display name and IP
                for cid, client in clients.items():
                    if (client.display_name == display_name and
                        client.address[0] == ip_address and
                        not client.connected):
                        existing_client = client
                        logger.info(f"Found existing disconnected client {cid} with matching name and IP")
                        # Update the client ID to use the existing one
                        stable_client_id = cid
                        break

        # If we found an existing client, update its socket and connection status
        if existing_client:
            logger.info(f"Updating existing client {stable_client_id} with new connection")
            with clients_lock:
                existing_client.socket = client_socket
                existing_client.address = client_address
                existing_client.connected = True
                existing_client.last_ping = time.time()
                existing_client.connection_status_time = time.time()
                existing_client.connection_attempts = 0
                # Update DB info if provided
                if db_info:
                    existing_client.db_info = db_info

                # Use the existing client
                client = existing_client
                logger.info(f"Reconnected client: {display_name} ({stable_client_id}) from {client_address}")
        else:
            # Create a new client object
            client = FirebirdClient(stable_client_id, display_name, client_socket, client_address)
            client.db_info = db_info

            # Store client
            with clients_lock:
                clients[stable_client_id] = client

            logger.info(f"New client registered: {display_name} ({stable_client_id}) from {client_address}")

        # Set socket back to default timeout
        client_socket.settimeout(default_socket_timeout)

        # Start thread to handle client messages
        threading.Thread(target=handle_client_messages, args=(stable_client_id,)).start()

    except Exception as e:
        logger.error(f"Error handling client registration: {e}")
        try:
            client_socket.close()
        except:
            pass

def handle_client_messages(client_id):
    """Thread function to handle messages from a client"""
    global clients

    try:
        client = None
        with clients_lock:
            if client_id in clients:
                client = clients[client_id]
            else:
                logger.warning(f"Client {client_id} not found in clients dict")
                return

        # Continue receiving messages until client disconnects
        while client.socket and client.connected:
            try:
                # Try to receive a message
                message = receive_message(client.socket)

                if not message:
                    # Client may have disconnected
                    logger.debug(f"No message received from {client.display_name}, may have disconnected")
                    break

                # Handle the message based on type
                msg_type = getattr(message, 'msg_type', None)
                if not msg_type:
                    logger.error(f"Received message has no type attribute: {message}")
                    continue

                if msg_type == NetworkMessage.TYPE_PONG:
                    # Client responded to ping
                    client.update_ping()

                elif msg_type == NetworkMessage.TYPE_RESULT:
                    # Store enhanced query result with metadata
                    if not hasattr(client, 'query_results'):
                        client.query_results = []

                    # Enhanced result processing with metadata logging
                    result_data = message.data
                    row_count = result_data.get('row_count', len(result_data.get('rows', [])))
                    column_count = result_data.get('column_count', len(result_data.get('headers', [])))
                    parsing_method = result_data.get('parsing_info', {}).get('method', 'unknown')
                    data_types = result_data.get('data_types', {})
                    
                    logger.info(f"Received enhanced query result from {client.display_name}:")
                    logger.info(f"  - {row_count} rows, {column_count} columns")
                    logger.info(f"  - Parsing method: {parsing_method}")
                    logger.info(f"  - Data types: {len(data_types)} columns typed")
                    
                    # Add client metadata to result
                    result_data['client_metadata'] = {
                        'client_id': client.client_id,
                        'client_name': client.display_name,
                        'received_timestamp': time.time(),
                        'server_processing_timestamp': datetime.datetime.now().isoformat()
                    }

                    client.query_results.append(result_data)

                elif msg_type == NetworkMessage.TYPE_ERROR:
                    # Handle error
                    logger.error(f"Received error from {client.display_name}: {message.data}")

                # Add handlers for MEGA upload messages
                elif msg_type == NetworkMessage.TYPE_MEGA_UPLOAD_ACK:
                    # Client acknowledged MEGA upload request
                    logger.info(f"[MEGA] Received upload acknowledgment from {client.display_name}: {message.data}")

                    # Store in client object for status tracking
                    if not hasattr(client, 'mega_uploads'):
                        client.mega_uploads = {}

                    request_id = message.data.get('request_id')
                    if request_id:
                        client.mega_uploads[request_id] = {
                            'status': 'acknowledged',
                            'file_path': message.data.get('file_path'),
                            'timestamp': message.data.get('timestamp', time.time()),
                            'progress': 0
                        }

                elif msg_type == NetworkMessage.TYPE_MEGA_UPLOAD_PROGRESS:
                    # Client sent progress update for MEGA upload
                    logger.info(f"[MEGA] Received upload progress from {client.display_name}: {message.data}")

                    # Update progress in client object
                    if not hasattr(client, 'mega_uploads'):
                        client.mega_uploads = {}

                    request_id = message.data.get('request_id')
                    if request_id and request_id in client.mega_uploads:
                        client.mega_uploads[request_id].update({
                            'status': 'in_progress',
                            'progress': message.data.get('progress', 0),
                            'timestamp': message.data.get('timestamp', time.time())
                        })

                elif msg_type == NetworkMessage.TYPE_MEGA_UPLOAD_RESULT:
                    # Client sent final result of MEGA upload
                    logger.info(f"[MEGA] Received upload result from {client.display_name}: {message.data}")

                    # Update result in client object
                    if not hasattr(client, 'mega_uploads'):
                        client.mega_uploads = {}

                    request_id = message.data.get('request_id')
                    if request_id:
                        client.mega_uploads[request_id] = {
                            'status': 'completed' if message.data.get('success', False) else 'failed',
                            'result': message.data,
                            'timestamp': time.time()
                        }
                elif msg_type == NetworkMessage.TYPE_MEGA_UPLOAD_STATUS:
                    # Handle MEGA upload status update
                    try:
                        request_id = message.data.get('request_id')
                        status = message.data.get('status')
                        progress = message.data.get('progress', 0)
                        result = message.data.get('result')

                        logger.info(f"[MEGA] Received upload status from {client.display_name}: {status}, {progress}%")

                        # Store status update
                        if not hasattr(client, 'mega_uploads'):
                            client.mega_uploads = {}

                        client.mega_uploads[request_id] = {
                            'status': status,
                            'progress': progress,
                            'timestamp': time.time(),
                            'result': result
                        }

                        # Send acknowledgment
                        ack_message = NetworkMessage(
                            NetworkMessage.TYPE_ACK,
                            {'message': 'Upload status received'},
                            client_id
                        )
                        send_message(client.socket, ack_message)
                    except Exception as e:
                        logger.error(f"[MEGA] Error handling upload status: {e}")
                        logger.error(traceback.format_exc())

                elif msg_type == NetworkMessage.TYPE_GDRIVE_UPLOAD_ACK:
                    # Client acknowledged GDrive upload request
                    logger.info(f"[GDRIVE] Received upload acknowledgment from {client.display_name}: {message.data}")

                    # Store in client object for status tracking
                    if not hasattr(client, 'gdrive_uploads'):
                        client.gdrive_uploads = {}

                    request_id = message.data.get('request_id')
                    if request_id:
                        client.gdrive_uploads[request_id] = {
                            'status': 'acknowledged',
                            'file_path': message.data.get('file_path'),
                            'timestamp': message.data.get('timestamp', time.time()),
                            'progress': 0
                        }

                elif msg_type == NetworkMessage.TYPE_GDRIVE_UPLOAD_PROGRESS:
                    # Client sent progress update for GDrive upload
                    try:
                        request_id = message.data.get('request_id')
                        progress = message.data.get('progress', 0)
                        status = message.data.get('status', 'uploading')

                        logger.info(f"[GDRIVE] Received upload progress from {client.display_name}: {progress}%, status: {status}")

                        # Store progress update
                        if not hasattr(client, 'gdrive_uploads'):
                            client.gdrive_uploads = {}

                        if request_id in client.gdrive_uploads:
                            client.gdrive_uploads[request_id].update({
                                'progress': progress,
                                'status': status,
                                'timestamp': time.time()
                            })
                        else:
                            client.gdrive_uploads[request_id] = {
                                'progress': progress,
                                'status': status,
                                'timestamp': time.time()
                            }
                    except Exception as e:
                        logger.error(f"[GDRIVE] Error handling upload progress: {e}")
                        logger.error(traceback.format_exc())

                elif msg_type == NetworkMessage.TYPE_GDRIVE_UPLOAD_RESULT:
                    # Client sent final result of GDrive upload
                    logger.info(f"[GDRIVE] Received upload result from {client.display_name}: success={message.data.get('success', False)}")
                    logger.info(f"[GDRIVE] Result details: {message.data}")

                    # Update result in client object
                    if not hasattr(client, 'gdrive_uploads'):
                        client.gdrive_uploads = {}

                    request_id = message.data.get('request_id')
                    if request_id:
                        client.gdrive_uploads[request_id] = {
                            'status': 'completed' if message.data.get('success', False) else 'failed',
                            'result': message.data,
                            'timestamp': time.time()
                        }

                        # Send acknowledgment
                        try:
                            ack_message = NetworkMessage(
                                NetworkMessage.TYPE_ACK,
                                {'message': 'Upload result received'},
                                client_id
                            )
                            send_message(client.socket, ack_message)
                        except Exception as ack_error:
                            logger.error(f"[GDRIVE] Error sending result acknowledgment: {ack_error}")

                elif msg_type == NetworkMessage.TYPE_GDRIVE_UPLOAD_STATUS:
                    # Handle GDrive upload status update
                    try:
                        request_id = message.data.get('request_id')
                        status = message.data.get('status')
                        progress = message.data.get('progress', 0)
                        result = message.data.get('result')

                        logger.info(f"[GDRIVE] Received upload status from {client.display_name}: {status}, {progress}%")

                        # Store status update
                        if not hasattr(client, 'gdrive_uploads'):
                            client.gdrive_uploads = {}

                        client.gdrive_uploads[request_id] = {
                            'status': status,
                            'progress': progress,
                            'timestamp': time.time(),
                            'result': result
                        }

                        # Send acknowledgment
                        ack_message = NetworkMessage(
                            NetworkMessage.TYPE_ACK,
                            {'message': 'Upload status received'},
                            client_id
                        )
                        send_message(client.socket, ack_message)
                    except Exception as e:
                        logger.error(f"[GDRIVE] Error handling upload status: {e}")
                        logger.error(traceback.format_exc())

                else:
                    # Unknown message type
                    logger.warning(f"Received unknown message type: {msg_type}")

            except Exception as e:
                logger.error(f"Error handling message from {client.display_name}: {e}")
                logger.debug(traceback.format_exc())
                # Continue to next message rather than breaking the loop
            time.sleep(1)

    except Exception as e:
        logger.error(f"Error in client message handler for {client_id}: {e}")
        logger.debug(traceback.format_exc())
    finally:
        # Mark client as disconnected
        with clients_lock:
            if client_id in clients:
                clients[client_id].mark_disconnected()
                logger.info(f"Client {clients[client_id].display_name} marked as disconnected")

def heartbeat_clients():
    logger.info("Starting heartbeat thread")

    while running:
        try:
            current_time = time.time()
            logger.debug(f"Heartbeat check at {time.strftime('%Y-%m-%d %H:%M:%S')}")

            with clients_lock:
                # Log the number of clients
                connected_count = sum(1 for c in clients.values() if c.connected)
                disconnected_count = len(clients) - connected_count
                logger.debug(f"Clients status: {connected_count} connected, {disconnected_count} disconnected, {len(clients)} total")

                for client_id, client in list(clients.items()):
                    # Check if client has been silent for too long
                    silence_time = current_time - client.last_ping
                    if silence_time > 30:  # 30 seconds timeout
                        logger.debug(f"Client {client.display_name} silent for {silence_time:.1f} seconds")

                        # If client is already marked as disconnected, don't try to ping again
                        if not client.connected:
                            disconnected_time = current_time - client.connection_status_time
                            # Keep disconnected clients for longer (30 minutes instead of 5)
                            # This helps maintain client identity across reconnections
                            if disconnected_time > 1800:  # 30 minutes
                                logger.info(f"Removing disconnected client {client.display_name} after {disconnected_time:.1f} seconds")
                                try:
                                    client.socket.close()
                                except Exception as close_error:
                                    logger.debug(f"Error closing socket for {client.display_name}: {close_error}")
                                del clients[client_id]
                            continue

                        # Increment connection attempts
                        client.connection_attempts += 1

                        # If we've tried too many times, mark as disconnected but don't remove
                        # Increased from 5 to 10 attempts for better tolerance
                        max_ping_attempts = 10
                        if client.connection_attempts > max_ping_attempts:
                            logger.warning(f"Client {client.display_name} not responding after {max_ping_attempts} attempts, marking as disconnected")
                            client.mark_disconnected()
                            # Don't remove the client, just mark it as disconnected
                            # This allows it to reconnect later with the same ID
                            continue

                        try:
                            # Send ping message with more detailed logging
                            logger.debug(f"Sending ping to {client.display_name} (attempt {client.connection_attempts}/{max_ping_attempts})")
                            ping_message = NetworkMessage(NetworkMessage.TYPE_PING, {'timestamp': time.time()}, client_id)

                            if not send_message(client.socket, ping_message):
                                # Failed to send ping, mark as disconnected but don't remove yet
                                logger.warning(f"Heartbeat failed for client {client.display_name}, marking as disconnected")
                                client.mark_disconnected()
                            else:
                                # Successfully sent ping, wait for pong response
                                logger.debug(f"Sent heartbeat to client {client.display_name} successfully")
                        except Exception as e:
                            logger.error(f"Error sending heartbeat to client {client.display_name}: {e}")
                            logger.debug(f"Heartbeat error details: {traceback.format_exc()}")
                            client.mark_disconnected()

            # Sleep for a while - reduced from 10 to 5 seconds for more responsive heartbeat
            time.sleep(5)
        except Exception as e:
            logger.error(f"Error in heartbeat thread: {e}")
            logger.error(f"Heartbeat thread error details: {traceback.format_exc()}")
            # Continue running even after errors
            time.sleep(5)

    logger.info("Heartbeat thread stopped")

def send_query_to_clients(query, target_clients=None, query_variables=None):
    """Send a query to specified clients or all clients"""
    if not query:
        return False, "Query is empty"

    # Replace variables in query if provided
    if query_variables:
        for var_name, var_value in query_variables.items():
            query = query.replace(f'#{var_name}#', var_value)

    sent_count = 0
    errors = []

    logger.info(f"Sending query to clients: {query[:100]}...")

    with clients_lock:
        if not clients:
            logger.warning("No clients connected")
            return False, "No clients connected"

        logger.info(f"Connected clients: {len(clients)}")

        # If no target clients specified, send to all
        if not target_clients:
            target_clients = list(clients.keys())

        logger.info(f"Target clients: {target_clients}")

        # Clear previous results
        for client_id in target_clients:
            if client_id in clients:
                clients[client_id].query_results = []

        # Send query to each client
        for client_id in target_clients:
            if client_id in clients:
                client = clients[client_id]
                try:
                    # Create query message
                    query_data = {
                        'query': query,
                        'max_rows': max_result_rows,
                        'timestamp': time.time()
                    }
                    message = NetworkMessage(NetworkMessage.TYPE_QUERY, query_data, client_id)

                    # Send message
                    if send_message(client.socket, message):
                        sent_count += 1
                        logger.info(f"Query sent to client {client.display_name}")
                    else:
                        error_msg = f"Failed to send query to {client.display_name}"
                        logger.error(error_msg)
                        errors.append(error_msg)
                except Exception as e:
                    error_msg = f"Error sending query to {client.display_name}: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)

    # Add to query history
    query_history.append({
        'query': query,
        'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'target_clients': target_clients,
        'sent_count': sent_count
    })

    if sent_count > 0:
        logger.info(f"Query sent to {sent_count} clients")
        return True, f"Query sent to {sent_count} clients"
    else:
        error_msg = "; ".join(errors)
        logger.error(f"Failed to send query: {error_msg}")
        return False, error_msg

# Flask routes
@app.route('/')
def index():
    """Main dashboard page"""
    with clients_lock:
        client_count = len(clients)
        client_list = [
            {
                'id': client.client_id,
                'name': client.display_name,
                'address': f"{client.address[0]}:{client.address[1]}",
                'connected_since': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(client.last_ping)),
                'db_info': client.db_info
            }
            for client in clients.values()
        ]

    server_status = "Running" if running else "Stopped"

    return render_template('index.html',
                          server_status=server_status,
                          client_count=client_count,
                          clients=client_list,
                          query_count=len(query_history))

@app.route('/backups')
def backups_page():
    """Database import management page (redirects to backup_page)"""
    return redirect(url_for('backup_page'))

@app.route('/clients')
def client_list():
    """Client management page"""
    with clients_lock:
        client_list = [
            {
                'id': client.client_id,
                'name': client.display_name,
                'address': f"{client.address[0]}:{client.address[1]}",
                'connected_since': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(client.last_ping)),
                'db_info': client.db_info
            }
            for client in clients.values()
        ]

    return render_template('clients.html', clients=client_list)

@app.route('/query')
def query_page():
    """Query execution page"""
    with clients_lock:
        client_list = [
            {
                'id': client.client_id,
                'name': client.display_name
            }
            for client in clients.values()
        ]

    # Use only predefined queries from files
    all_queries = {}

    # Add predefined queries from files
    for query_name, query_text in predefined_queries.items():
        all_queries[query_name] = query_text

    return render_template('query.html',
                          clients=client_list,
                          predefined_queries=all_queries)

@app.route('/monitoring')
def monitoring():
    """Monitoring dashboard page"""
    return render_template('monitoring_simple.html')

@app.route('/query_history')
def query_history_page():
    """Query history page"""
    return render_template('query_history.html',
                          query_history=query_history)

@app.route('/test_query')
def test_query():
    """Test query page"""
    query_name = request.args.get('query')
    results = []

    if query_name in predefined_queries:
        # Execute the query
        query = predefined_queries[query_name]
        send_result, send_message_text = send_query_to_clients(query)

        if not send_result:
            logger.warning(f"Failed to send query: {send_message_text}")

        # Wait a bit for results
        time.sleep(2)

        # Get results
        with clients_lock:
            for client_id, client in clients.items():
                if hasattr(client, 'query_results'):
                    for result in client.query_results:
                        # Add client info if not present
                        if 'client_name' not in result:
                            result['client_name'] = client.display_name
                        if 'client_id' not in result:
                            result['client_id'] = client_id

                        # Make sure rows and headers exist
                        if 'rows' not in result:
                            result['rows'] = []
                        if 'headers' not in result:
                            result['headers'] = []

                        results.append(result)

    return render_template('test_query.html', results=results)

@app.route('/api/server/start', methods=['POST'])
def api_start_server():
    """API endpoint to start the server"""
    success = start_server()
    return jsonify({'success': success})

@app.route('/api/server/stop', methods=['POST'])
def api_stop_server():
    """API endpoint to stop the server"""
    success = stop_server()
    return jsonify({'success': success})

@app.route('/api/server/uptime', methods=['GET'])
def api_server_uptime():
    """API endpoint to get server uptime"""
    if not running or server_start_time is None:
        return jsonify({
            'success': True,
            'uptime': 'Server not running'
        })

    # Calculate uptime
    uptime_seconds = int(time.time() - server_start_time)

    # Format uptime
    days, remainder = divmod(uptime_seconds, 86400)
    hours, remainder = divmod(remainder, 3600)
    minutes, seconds = divmod(remainder, 60)

    if days > 0:
        uptime_str = f"{days}d {hours}h {minutes}m {seconds}s"
    elif hours > 0:
        uptime_str = f"{hours}h {minutes}m {seconds}s"
    elif minutes > 0:
        uptime_str = f"{minutes}m {seconds}s"
    else:
        uptime_str = f"{seconds}s"

    return jsonify({
        'success': True,
        'uptime': uptime_str,
        'uptime_seconds': uptime_seconds
    })

@app.route('/api/query/send', methods=['POST'])
def api_send_query():
    """API endpoint to send a query to clients"""
    query = request.form.get('query', '')
    target_clients = request.form.getlist('clients')
    variables_json = request.form.get('variables', '{}')

    # Parse variables JSON
    try:
        query_variables = json.loads(variables_json)
    except json.JSONDecodeError:
        query_variables = {}

    success, message = send_query_to_clients(query, target_clients, query_variables)

    return jsonify({
        'success': success,
        'message': message
    })

@app.route('/api/query/results', methods=['GET'])
def api_query_results():
    """API endpoint to get query results"""
    client_id = request.args.get('client_id')

    logger.info(f"Getting query results for client_id: {client_id or 'all'}")

    results = []
    with clients_lock:
        logger.info(f"Number of connected clients: {len(clients)}")

        # Log all client IDs for debugging
        client_ids = list(clients.keys())
        logger.info(f"Connected client IDs: {client_ids}")

        if client_id and client_id in clients:
            client = clients[client_id]
            logger.info(f"Client {client_id} ({client.display_name}) has {len(getattr(client, 'query_results', []))} results")

            # Make sure client has query_results attribute
            if not hasattr(client, 'query_results'):
                client.query_results = []
                logger.warning(f"Client {client.display_name} has no query_results attribute, initializing empty list")

            client_results = client.query_results

            # Process and validate each result
            for result in client_results:
                # Log the raw result
                logger.info(f"Raw result: {str(result)[:500]}...")

                # Make sure the result has the necessary fields
                if 'rows' not in result:
                    result['rows'] = []
                    logger.warning(f"Result missing 'rows' field, initializing empty list")
                if 'headers' not in result:
                    result['headers'] = []
                    logger.warning(f"Result missing 'headers' field, initializing empty list")

                # Add client information to the result
                result['client_name'] = client.display_name
                result['client_id'] = client_id

                # Validate rows format
                validated_rows = []
                for row in result.get('rows', []):
                    if isinstance(row, dict):
                        # Format is already correct
                        validated_rows.append(row)
                    elif isinstance(row, (list, tuple)):
                        # Convert list/tuple to dict
                        row_dict = {}
                        for j, header in enumerate(result.get('headers', [])):
                            if j < len(row):
                                row_dict[header] = row[j]
                            else:
                                row_dict[header] = None
                        validated_rows.append(row_dict)
                    else:
                        # Unexpected row format, log and create empty row
                        logger.warning(f"Unexpected row format: {type(row)}, value: {str(row)[:100]}")
                        row_dict = {}
                        for header in result.get('headers', []):
                            row_dict[header] = None
                        validated_rows.append(row_dict)

                # Update rows with validated rows
                result['rows'] = validated_rows
                results.append(result)

        elif not client_id:
            # Get results from all clients
            for client_id, client in clients.items():
                # Make sure client has query_results attribute
                if not hasattr(client, 'query_results'):
                    client.query_results = []
                    logger.warning(f"Client {client.display_name} has no query_results attribute, initializing empty list")

                logger.info(f"Client {client.display_name} has {len(client.query_results)} results")

                if client.query_results:
                    for result in client.query_results:
                        # Log the raw result
                        logger.info(f"Raw result from {client.display_name}: {str(result)[:500]}...")

                        # Enhanced result validation with metadata preservation
                        if 'rows' not in result:
                            result['rows'] = []
                            logger.warning(f"Result missing 'rows' field, initializing empty list")
                        if 'headers' not in result:
                            result['headers'] = []
                            logger.warning(f"Result missing 'headers' field, initializing empty list")

                        # Preserve enhanced metadata
                        result.setdefault('row_count', len(result.get('rows', [])))
                        result.setdefault('column_count', len(result.get('headers', [])))
                        result.setdefault('data_types', {})
                        result.setdefault('parsing_info', {})
                        result.setdefault('execution_info', {})
                        result.setdefault('validation_info', {})

                        # Add/update client information
                        result['client_name'] = client.display_name
                        result['client_id'] = client_id
                        
                        # Add API processing metadata
                        result['api_metadata'] = {
                            'api_processing_timestamp': datetime.datetime.now().isoformat(),
                            'enhanced_format': True,
                            'metadata_preserved': True
                        }

                        # Validate rows format
                        validated_rows = []
                        for row in result.get('rows', []):
                            if isinstance(row, dict):
                                # Format is already correct
                                validated_rows.append(row)
                            elif isinstance(row, (list, tuple)):
                                # Convert list/tuple to dict
                                row_dict = {}
                                for j, header in enumerate(result.get('headers', [])):
                                    if j < len(row):
                                        row_dict[header] = row[j]
                                    else:
                                        row_dict[header] = None
                                validated_rows.append(row_dict)
                            else:
                                # Unexpected row format, log and create empty row
                                logger.warning(f"Unexpected row format: {type(row)}, value: {str(row)[:100]}")
                                row_dict = {}
                                for header in result.get('headers', []):
                                    row_dict[header] = None
                                validated_rows.append(row_dict)

                        # Update rows with validated rows
                        result['rows'] = validated_rows
                        results.append(result)

    # Log the results we're returning
    logger.info(f"Returning {len(results)} results")
    if results:
        for i, result in enumerate(results):
            if 'error' in result and result['error']:
                logger.error(f"Result {i+1} has error: {result['error']}")
            elif 'status' in result and result['status'] == 'error':
                logger.error(f"Result {i+1} has error status: {result.get('error', 'Unknown error')}")
            elif 'rows' in result:
                logger.info(f"Result {i+1} has {len(result.get('rows', []))} rows and {len(result.get('headers', []))} columns")
                if result.get('rows'):
                    logger.info(f"Sample first row: {str(result['rows'][0])[:200]}...")
    else:
        logger.warning("No results found from any client")

    return jsonify({
        'success': True,
        'results': results
    })

@app.route('/api/clients', methods=['GET'])
def api_clients():
    """API endpoint to get client list"""
    with clients_lock:
        client_list = [
            {
                'id': client.client_id,
                'name': client.display_name,
                'address': f"{client.address[0]}:{client.address[1]}",
                'connected_since': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(client.last_ping)),
                'db_info': client.db_info
            }
            for client in clients.values()
        ]

    return jsonify({
        'success': True,
        'clients': client_list
    })

@app.route('/api/monitoring/run', methods=['POST'])
def api_run_monitoring():
    """API endpoint to run a monitoring query"""
    query_name = request.form.get('query_name', '')
    variables_json = request.form.get('variables', '{}')

    # Parse variables JSON
    try:
        query_variables = json.loads(variables_json)
    except json.JSONDecodeError:
        query_variables = {}

    logger.info(f"Running monitoring query: {query_name}")

    if query_name not in predefined_queries:
        logger.error(f"Query '{query_name}' not found in predefined queries")
        logger.info(f"Available queries: {list(predefined_queries.keys())}")
        return jsonify({
            'success': False,
            'message': f"Query '{query_name}' not found"
        })

    query = predefined_queries[query_name]
    logger.info(f"Found query '{query_name}', length: {len(query)} characters")

    # Clear previous results from all clients
    with clients_lock:
        for client_id in clients:
            clients[client_id].query_results = []

    # Send query to clients
    success, message = send_query_to_clients(query, None, query_variables)

    # Add to query history with query name
    query_history.append({
        'query_name': query_name,
        'query': query,
        'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    })

    logger.info(f"Query execution result: success={success}, message={message}")

    # Wait a short time for clients to process the query
    if success:
        logger.info("Waiting for clients to process the query...")
        time.sleep(1)  # Give clients a moment to process the query

    return jsonify({
        'success': success,
        'message': message,
        'query_name': query_name
    })

@app.route('/api/query/save', methods=['POST'])
def api_save_query():
    """API endpoint to save a query"""
    query_name = request.form.get('query_name', '')
    query_content = request.form.get('query_content', '')
    description = request.form.get('description', '')
    variables_json = request.form.get('variables', '{}')

    if not query_name or not query_content:
        return jsonify({
            'success': False,
            'message': "Query name and content are required"
        })

    # Sanitize query name for filename
    safe_name = re.sub(r'[^\w\-\.]', '_', query_name)

    try:
        # Parse variables JSON if provided
        variables = {}
        if variables_json:
            try:
                variables = json.loads(variables_json)
            except json.JSONDecodeError:
                logger.warning(f"Invalid variables JSON: {variables_json}")

        # Save to file system
        save_dir = os.path.join(current_dir, 'static', 'sql_queries')
        os.makedirs(save_dir, exist_ok=True)
        file_path = os.path.join(save_dir, f"{safe_name}.sql")

        with open(file_path, 'w', encoding='utf-8') as f:
            # Add description as comment if provided
            if description:
                f.write(f"-- {description}\n")

            # Add variables as comments if provided
            if variables:
                f.write(f"-- VARIABLES_START\n")
                for var_name, var_info in variables.items():
                    default_value = var_info.get('default', '')
                    var_description = var_info.get('description', '')
                    f.write(f"-- VAR:{var_name}:{default_value}:{var_description}\n")
                f.write(f"-- VARIABLES_END\n")

            f.write(query_content)

        # Reload predefined queries
        global predefined_queries, query_descriptions
        predefined_queries, query_descriptions = load_predefined_queries()

        logger.info(f"Query saved: {query_name} to {file_path}")

        return jsonify({
            'success': True,
            'message': f"Query '{query_name}' saved successfully",
            'file_path': file_path
        })
    except Exception as e:
        logger.error(f"Error saving query: {e}")
        return jsonify({
            'success': False,
            'message': f"Error saving query: {str(e)}"
        })

@app.route('/api/query/update', methods=['POST'])
def api_update_query():
    """API endpoint to update an existing query"""
    query_name = request.form.get('query_name', '')
    query_content = request.form.get('query_content', '')
    description = request.form.get('description', '')
    old_name = request.form.get('old_name', '')
    variables_json = request.form.get('variables', '{}')

    if not query_name or not query_content:
        return jsonify({
            'success': False,
            'message': "Query name and content are required"
        })

    try:
        # Parse variables JSON if provided
        variables = {}
        if variables_json:
            try:
                variables = json.loads(variables_json)
            except json.JSONDecodeError:
                logger.warning(f"Invalid variables JSON: {variables_json}")

        # Sanitize query names for filename
        safe_name = re.sub(r'[^\w\-\.]', '_', query_name)

        # If old_name is provided and different from new name, we need to delete the old file
        if old_name and old_name != query_name:
            old_safe_name = re.sub(r'[^\w\-\.]', '_', old_name)
            old_file_path = os.path.join(current_dir, 'static', 'sql_queries', f"{old_safe_name}.sql")
            if os.path.exists(old_file_path):
                os.remove(old_file_path)
                logger.info(f"Deleted old query file: {old_file_path}")

        # Save to file system
        save_dir = os.path.join(current_dir, 'static', 'sql_queries')
        os.makedirs(save_dir, exist_ok=True)
        file_path = os.path.join(save_dir, f"{safe_name}.sql")

        with open(file_path, 'w', encoding='utf-8') as f:
            # Add description as comment if provided
            if description:
                f.write(f"-- {description}\n")

            # Add variables as comments if provided
            if variables:
                f.write(f"-- VARIABLES_START\n")
                for var_name, var_info in variables.items():
                    default_value = var_info.get('default', '')
                    var_description = var_info.get('description', '')
                    f.write(f"-- VAR:{var_name}:{default_value}:{var_description}\n")
                f.write(f"-- VARIABLES_END\n")

            f.write(query_content)

        # Reload predefined queries
        global predefined_queries, query_descriptions
        predefined_queries, query_descriptions = load_predefined_queries()

        logger.info(f"Query updated: {query_name} to {file_path}")

        return jsonify({
            'success': True,
            'message': f"Query updated successfully"
        })
    except Exception as e:
        logger.error(f"Error updating query: {e}")
        return jsonify({
            'success': False,
            'message': f"Error updating query: {str(e)}"
        })

@app.route('/api/query/delete', methods=['POST'])
def api_delete_query():
    """API endpoint to delete a query"""
    query_name = request.form.get('query_name', '')

    if not query_name:
        return jsonify({
            'success': False,
            'message': "Query name is required"
        })

    try:
        # Delete from file system
        safe_name = re.sub(r'[^\w\-\.]', '_', query_name)
        file_path = os.path.join(current_dir, 'static', 'sql_queries', f"{safe_name}.sql")

        if os.path.exists(file_path):
            os.remove(file_path)
            logger.info(f"Query file deleted: {file_path}")
        else:
            return jsonify({
                'success': False,
                'message': f"Query file not found: {safe_name}.sql"
            })

        # Reload predefined queries
        global predefined_queries, query_descriptions
        predefined_queries, query_descriptions = load_predefined_queries()

        logger.info(f"Query deleted: {query_name}")

        return jsonify({
            'success': True,
            'message': f"Query '{query_name}' deleted successfully"
        })
    except Exception as e:
        logger.error(f"Error deleting query: {e}")
        return jsonify({
            'success': False,
            'message': f"Error deleting query: {str(e)}"
        })

@app.route('/api/query/get/<query_name>', methods=['GET'])
def api_get_query(query_name):
    """API endpoint to get a specific query by name"""
    try:
        # Sanitize query name for filename
        safe_name = re.sub(r'[^\w\-\.]', '_', query_name)
        file_path = os.path.join(current_dir, 'static', 'sql_queries', f"{safe_name}.sql")

        if not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'message': f"Query '{query_name}' not found"
            })

        # Read query content from file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Extract description from comments
        description = ""
        for line in content.split('\n')[:5]:  # Check first 5 lines
            if line.strip().startswith('--'):
                comment = line.strip()[2:].strip()
                if comment.startswith("VARIABLES_START"):
                    break
                if description:
                    description += " " + comment
                else:
                    description = comment

        # Get variables for this query
        variables = {}
        if query_name in query_vars:
            variables = query_vars[query_name]

        # Extract variables from content if not already loaded
        if not variables:
            in_variables_section = False
            for line in content.split('\n'):
                line = line.strip()
                if not line.startswith('--'):
                    continue

                comment = line[2:].strip()

                # Check for variables section
                if comment == "VARIABLES_START":
                    in_variables_section = True
                    continue
                elif comment == "VARIABLES_END":
                    in_variables_section = False
                    continue

                # Process variable definitions
                if in_variables_section and comment.startswith("VAR:"):
                    try:
                        # Format: VAR:name:default:description
                        parts = comment[4:].split(':', 2)
                        var_name = parts[0].strip()
                        var_default = parts[1].strip() if len(parts) > 1 else ""
                        var_description = parts[2].strip() if len(parts) > 2 else ""

                        variables[var_name] = {
                            'default': var_default,
                            'description': var_description
                        }
                    except Exception as var_error:
                        logger.warning(f"Error parsing variable in {query_name}: {var_error}")

        query = {
            'name': query_name,
            'query_text': content,
            'description': description,
            'file_path': file_path,
            'variables': variables
        }

        return jsonify({
            'success': True,
            'query': query
        })
    except Exception as e:
        logger.error(f"Error getting query: {e}")
        return jsonify({
            'success': False,
            'message': f"Error getting query: {str(e)}"
        })

@app.route('/api/query/list', methods=['GET'])
def api_list_queries():
    """API endpoint to list all available queries"""
    # Get all queries from predefined_queries (loaded from files)
    queries_list = []

    try:
        # Add all queries from predefined_queries
        for query_name, query_text in predefined_queries.items():
            # Get file path
            safe_name = re.sub(r'[^\w\-\.]', '_', query_name)
            file_path = os.path.join(current_dir, 'static', 'sql_queries', f"{safe_name}.sql")

            # Get file modification time if file exists
            updated_at = None
            if os.path.exists(file_path):
                updated_at = datetime.datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S')

            # Get variables for this query
            variables = {}
            if query_name in query_vars:
                variables = query_vars[query_name]

            # Extract variable names for display
            variable_names = list(variables.keys()) if variables else []

            # Add to list regardless of whether file exists in the expected location
            # This ensures predefined queries loaded from any directory are included
            description = query_descriptions.get(query_name, '')
            queries_list.append({
                'name': query_name,
                'description': description,
                'size': len(query_text),
                'updated_at': updated_at,
                'file_path': file_path if os.path.exists(file_path) else "Not found on disk",
                'variables': variable_names,
                'has_variables': len(variable_names) > 0,
                'query_text': query_text
            })

        # Sort queries by name
        queries_list.sort(key=lambda x: x['name'])

        # Log number of queries found for debugging
        logger.info(f"Found {len(queries_list)} queries to list")

        return jsonify({
            'success': True,
            'queries': queries_list
        })
    except Exception as e:
        logger.error(f"Error listing queries: {e}")
        return jsonify({
            'success': False,
            'message': f"Error listing queries: {str(e)}",
            'queries': []
        })

@app.route('/api/backup/list', methods=['GET'])
def api_list_backups():
    """API endpoint to list all database backups"""
    client_id = request.args.get('client_id', '')

    backup_dir = os.path.join(current_dir, 'static', 'backups')
    if not os.path.exists(backup_dir):
        return jsonify({
            'success': True,
            'backups': []
        })

    backups = []

    if client_id:
        # List backups for specific client
        client_dir = os.path.join(backup_dir, client_id)
        if os.path.exists(client_dir):
            for filename in os.listdir(client_dir):
                if filename.endswith('.fdb'):
                    file_path = os.path.join(client_dir, filename)
                    file_size = os.path.getsize(file_path)
                    file_time = os.path.getmtime(file_path)

                    backups.append({
                        'client_id': client_id,
                        'filename': filename,
                        'path': file_path,
                        'size': file_size,
                        'timestamp': datetime.datetime.fromtimestamp(file_time).strftime('%Y-%m-%d %H:%M:%S')
                    })
    else:
        # List backups for all clients
        for client_dir_name in os.listdir(backup_dir):
            client_dir = os.path.join(backup_dir, client_dir_name)
            if os.path.isdir(client_dir):
                for filename in os.listdir(client_dir):
                    if filename.endswith('.fdb'):
                        file_path = os.path.join(client_dir, filename)
                        file_size = os.path.getsize(file_path)
                        file_time = os.path.getmtime(file_path)

                        backups.append({
                            'client_id': client_dir_name,
                            'filename': filename,
                            'path': file_path,
                            'size': file_size,
                            'timestamp': datetime.datetime.fromtimestamp(file_time).strftime('%Y-%m-%d %H:%M:%S')
                        })

    # Sort backups by timestamp (newest first)
    backups.sort(key=lambda x: x['timestamp'], reverse=True)

    return jsonify({
        'success': True,
        'backups': backups
    })

@app.route('/api/backup/status', methods=['GET'])
def api_backup_status():
    """API endpoint to get backup status for a client"""
    client_id = request.args.get('client_id', '')

    if not client_id:
        return jsonify({
            'success': False,
            'message': "Client ID is required"
        })

    with clients_lock:
        if client_id not in clients:
            return jsonify({
                'success': False,
                'message': f"Client {client_id} not found"
            })

        client = clients[client_id]

        # Check if backup is complete first (highest priority status)
        if hasattr(client, 'db_backup_complete') and client.db_backup_complete:
            result = {
                'success': True,
                'status': 'complete',
                'progress': 100,
                'filename': os.path.basename(client.db_file_path) if hasattr(client, 'db_file_path') and client.db_file_path else None,
                'timestamp': client.db_backup_timestamp if hasattr(client, 'db_backup_timestamp') else None,
                'last_update': time.time()
            }

            # Add additional completion data if available
            if hasattr(client, 'db_backup_elapsed_time'):
                result['elapsed_time'] = round(client.db_backup_elapsed_time, 1)
            if hasattr(client, 'db_backup_speed'):
                result['transfer_rate'] = round(client.db_backup_speed, 2)  # KB/s

            # Add download link
            if hasattr(client, 'db_file_path') and client.db_file_path:
                result['download_url'] = f"/api/backup/download/{client_id}/{os.path.basename(client.db_file_path)}"

            return jsonify(result)

        # Check if backup failed (second priority)
        if hasattr(client, 'db_backup_status') and client.db_backup_status == 'failed':
            return jsonify({
                'success': True,
                'status': 'failed',
                'error': client.db_backup_error if hasattr(client, 'db_backup_error') else 'Unknown error',
                'progress': int(client.db_file_received * 100 / client.db_file_size) if hasattr(client, 'db_file_size') and hasattr(client, 'db_file_received') and client.db_file_size > 0 else 0,
                'last_update': time.time()
            })

        # Check if backup is in progress (third priority)
        if hasattr(client, 'db_file_path') and hasattr(client, 'db_file_size') and hasattr(client, 'db_file_received'):
            if client.db_file_size > 0:
                progress = int(client.db_file_received * 100 / client.db_file_size)

                # Calculate transfer rate
                elapsed_time = time.time() - client.db_backup_start_time if hasattr(client, 'db_backup_start_time') else 0
                transfer_rate = client.db_file_received / elapsed_time if elapsed_time > 0 else 0  # bytes per second

                # Calculate ETA
                remaining_bytes = client.db_file_size - client.db_file_received
                eta_seconds = remaining_bytes / transfer_rate if transfer_rate > 0 else 0

                # Format ETA as string
                if eta_seconds < 60:
                    eta_str = f"{int(eta_seconds)} detik"
                elif eta_seconds < 3600:
                    eta_str = f"{int(eta_seconds/60)} menit {int(eta_seconds%60)} detik"
                else:
                    eta_str = f"{int(eta_seconds/3600)} jam {int((eta_seconds%3600)/60)} menit"

                # Add chunk info if available
                chunk_info = {}
                if hasattr(client, 'db_chunks_received') and hasattr(client, 'db_total_chunks') and client.db_total_chunks > 0:
                    chunk_info = {
                        'chunks_received': client.db_chunks_received,
                        'total_chunks': client.db_total_chunks,
                        'chunk_progress': int(client.db_chunks_received * 100 / client.db_total_chunks)
                    }

                return jsonify({
                    'success': True,
                    'status': client.db_backup_status if hasattr(client, 'db_backup_status') else 'in_progress',
                    'progress': progress,
                    'received': client.db_file_received,
                    'total': client.db_file_size,
                    'filename': os.path.basename(client.db_file_path) if client.db_file_path else None,
                    'elapsed_time': round(elapsed_time, 1),
                    'transfer_rate': round(transfer_rate / 1024, 2),  # KB/s
                    'eta': round(eta_seconds, 1),
                    'eta_str': eta_str,
                    'chunk_info': chunk_info,
                    'last_update': time.time()
                })

        # Check if backup is requested but not yet started
        if hasattr(client, 'db_backup_status') and client.db_backup_status == 'requested':
            return jsonify({
                'success': True,
                'status': 'requested',
                'progress': 0,
                'message': 'Backup request sent to client, waiting for response',
                'request_time': client.db_backup_request_time if hasattr(client, 'db_backup_request_time') else time.time(),
                'last_update': time.time()
            })

        # No backup in progress or complete
        return jsonify({
            'success': True,
            'status': 'none',
            'progress': 0,
            'last_update': time.time()
        })

@app.route('/api/backup/download/<client_id>/<path:filename>', methods=['GET'])
def api_download_backup(client_id, filename):
    """API endpoint to download a database backup"""
    logger.info(f"[BACKUP] Download request received for client_id: {client_id}, filename: {filename}")

    backup_dir = os.path.join(current_dir, 'static', 'backups', client_id)

    # Check if backup directory exists
    if not os.path.exists(backup_dir):
        logger.error(f"[BACKUP] Backup directory does not exist: {backup_dir}")
        try:
            # Create the static directory if it doesn't exist
            static_dir = os.path.join(current_dir, 'static')
            if not os.path.exists(static_dir):
                os.makedirs(static_dir, exist_ok=True)
                logger.info(f"[BACKUP] Static directory created: {static_dir}")

            # Create the backups directory
            backups_dir = os.path.join(current_dir, 'static', 'backups')
            if not os.path.exists(backups_dir):
                os.makedirs(backups_dir, exist_ok=True)
                logger.info(f"[BACKUP] Backups directory created: {backups_dir}")

            # Create client directory
            os.makedirs(backup_dir, exist_ok=True)
            logger.info(f"[BACKUP] Client backup directory created: {backup_dir}")
        except Exception as dir_error:
            logger.error(f"[BACKUP] Error creating backup directory: {dir_error}")
            return jsonify({
                'success': False,
                'message': f"Backup directory not found and could not be created: {str(dir_error)}"
            }), 500

    # Check if file exists
    file_path = os.path.join(backup_dir, filename)
    if not os.path.exists(file_path):
        logger.error(f"[BACKUP] File not found: {file_path}")
        return jsonify({
            'success': False,
            'message': f"File not found: {filename}"
        }), 404

    logger.info(f"[BACKUP] Sending file: {file_path}")
    return send_from_directory(backup_dir, filename, as_attachment=True)

@app.route('/api/backup/upload-to-mega', methods=['POST'])
def api_upload_to_mega():
    """API endpoint to request uploading a database backup to MEGA cloud storage"""
    client_id = request.form.get('client_id', '')
    backup_filename = request.form.get('filename', '')

    logger.info(f"[MEGA] ===== MEGA UPLOAD REQUEST RECEIVED VIA API =====")
    logger.info(f"[MEGA] Upload request received via API, client_id: {client_id}, filename: {backup_filename}")
    logger.info(f"[MEGA] Request source: {request.remote_addr}")
    logger.info(f"[MEGA] Request method: {request.method}")
    logger.debug(f"[MEGA] Request form data: {request.form}")

    if not client_id:
        logger.error("[MEGA] No client ID provided in upload request")
        return jsonify({
            'success': False,
            'message': "Client ID is required"
        }), 400

    if not backup_filename:
        logger.error("[MEGA] No filename provided in upload request")
        return jsonify({
            'success': False,
            'message': "Filename is required"
        }), 400

    # Check if client exists and is connected
    with clients_lock:
        if client_id not in clients:
            logger.error(f"[MEGA] Client {client_id} not found")
            return jsonify({
                'success': False,
                'message': f"Client {client_id} not found"
            }), 404

        client = clients[client_id]

        if not client.connected or not client.socket:
            logger.error(f"[MEGA] Client {client.display_name} is not connected")
            return jsonify({
                'success': False,
                'message': f"Client {client.display_name} is not connected"
            }), 400

    # Check if backup file exists
    backup_dir = os.path.join(current_dir, 'static', 'backups', client_id)
    file_path = os.path.join(backup_dir, backup_filename)

    if not os.path.exists(file_path):
        logger.error(f"[MEGA] Backup file not found: {file_path}")
        return jsonify({
            'success': False,
            'message': f"Backup file not found: {backup_filename}"
        }), 404

    # Create a unique request ID
    request_id = str(uuid.uuid4())

    # Create MEGA upload request message
    try:
        upload_request = NetworkMessage(
            NetworkMessage.TYPE_MEGA_UPLOAD_REQUEST,
            {
                'file_path': file_path,
                'request_id': request_id,
                'timestamp': time.time(),
                'client_id': client_id
            },
            client_id
        )

        # Send request to client
        logger.info(f"[MEGA] Sending MEGA upload request to client {client.display_name} for file {backup_filename}")
        if send_message(client.socket, upload_request):
            logger.info(f"[MEGA] MEGA upload request sent successfully to client {client.display_name}")
            return jsonify({
                'success': True,
                'message': f"MEGA upload request sent to client {client.display_name}",
                'request_id': request_id
            })
        else:
            logger.error(f"[MEGA] Failed to send MEGA upload request to client {client.display_name}")
            return jsonify({
                'success': False,
                'message': f"Failed to send MEGA upload request to client {client.display_name}"
            }), 500
    except Exception as e:
        logger.error(f"[MEGA] Error sending MEGA upload request: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f"Error sending MEGA upload request: {str(e)}"
        }), 500

@app.route('/backup', methods=['GET'])
def backup_page():
    """Database import management page"""
    # Get list of clients
    client_list = []
    with clients_lock:
        for client_id, client in clients.items():
            client_list.append({
                'id': client_id,
                'name': client.display_name,
                'address': client.address[0] if hasattr(client, 'address') and client.address else 'Unknown',
                'connected': client.connected
            })

    # Get list of backups
    backup_dir = os.path.join(current_dir, "static", "backups")
    backups = []

    if os.path.exists(backup_dir):
        try:
            client_dirs = os.listdir(backup_dir)

            for client_dir_name in client_dirs:
                client_dir = os.path.join(backup_dir, client_dir_name)
                if os.path.isdir(client_dir):
                    try:
                        files = os.listdir(client_dir)

                        # Get client name
                        client_name = client_dir_name
                        with clients_lock:
                            if client_dir_name in clients:
                                client_name = clients[client_dir_name].display_name

                        for filename in files:
                            file_path = os.path.join(client_dir, filename)
                            if os.path.isfile(file_path):
                                file_size = os.path.getsize(file_path)
                                file_date = datetime.datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S')

                                backups.append({
                                    'client_id': client_dir_name,
                                    'client_name': client_name,
                                    'filename': filename,
                                    'size': file_size,
                                    'size_formatted': format_file_size(file_size),
                                    'date': file_date,
                                    'download_url': f"/api/backup/download/{client_dir_name}/{filename}"
                                })
                    except Exception as e:
                        logger.error(f"Error listing files for client {client_dir_name}: {e}")
        except Exception as e:
            logger.error(f"Error listing backup directory: {e}")

    # Sort backups by date (newest first)
    backups.sort(key=lambda x: x['date'], reverse=True)

    return render_template('backups.html', clients=client_list, backups=backups)

@app.route('/api/backup/request', methods=['POST'])
def api_request_backup():
    """API endpoint to request database backup from clients"""
    client_id = request.form.get('client_id', '')

    # Log the backup request with more details
    logger.info(f"[BACKUP] ===== BACKUP REQUEST RECEIVED VIA API =====")
    logger.info(f"[BACKUP] Backup request received via API, client_id: {client_id}")
    logger.info(f"[BACKUP] Request source: {request.remote_addr}")
    logger.info(f"[BACKUP] Request method: {request.method}")
    logger.debug(f"[BACKUP] Request form data: {request.form}")
    logger.debug(f"[BACKUP] Request headers: {request.headers}")

    # Print to console for web debugging
    print(f"\n[BACKUP-DEBUG] ===== BACKUP REQUEST RECEIVED VIA API =====")
    print(f"[BACKUP-DEBUG] Client ID: {client_id}")
    print(f"[BACKUP-DEBUG] Request source: {request.remote_addr}")
    print(f"[BACKUP-DEBUG] Request time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}")
    print(f"[BACKUP-DEBUG] Form data: {request.form}")

    # Log to backup logger
    backup_logger.info(f"===== BACKUP REQUEST RECEIVED VIA API =====")
    backup_logger.info(f"Client ID: {client_id}")
    backup_logger.info(f"Request source: {request.remote_addr}")
    backup_logger.info(f"Request time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Print to console for immediate visibility
    print(f"\n[BACKUP] ===== BACKUP REQUEST RECEIVED FROM WEB INTERFACE =====")
    print(f"[BACKUP] Requesting backup for client: {client_id}")
    print(f"[BACKUP] Request time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"[BACKUP] Request source IP: {request.remote_addr}")
    print(f"[BACKUP] Request form data: {request.form}")

    # Log to backup logger with more details
    backup_logger.info(f"===== BACKUP REQUEST DETAILS =====")
    backup_logger.info(f"Client ID: {client_id}")
    backup_logger.info(f"Request time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}")
    backup_logger.info(f"Request source IP: {request.remote_addr}")
    backup_logger.info(f"User agent: {request.headers.get('User-Agent', 'Unknown')}")

    if not client_id:
        # If no client_id specified, request from all clients
        with clients_lock:
            if not clients:
                logger.warning("[BACKUP] No clients connected for backup request")
                print("[BACKUP-DEBUG] No clients connected for backup request")
                return jsonify({
                    'success': False,
                    'message': "No clients connected"
                })

            # Check which clients are actually connected
            connected_clients = []
            for cid, client in clients.items():
                if hasattr(client, 'socket') and client.socket is not None and client.connected:
                    connected_clients.append(cid)
                    logger.debug(f"[BACKUP] Client {client.display_name} is connected and has socket")
                    print(f"[BACKUP-DEBUG] Client {client.display_name} is connected and has socket")
                else:
                    logger.warning(f"[BACKUP] Client {client.display_name} has no socket connection or is not connected")
                    print(f"[BACKUP-DEBUG] Client {client.display_name} has no socket connection or is not connected")

            if not connected_clients:
                logger.warning("[BACKUP] No clients with active socket connections")
                print("[BACKUP-DEBUG] No clients with active socket connections")
                return jsonify({
                    'success': False,
                    'message': "No clients with active connections"
                })

            logger.info(f"[BACKUP] Requesting backup from {len(connected_clients)} connected clients")
            print(f"[BACKUP-DEBUG] Requesting backup from {len(connected_clients)} connected clients")
            success_count = 0
            for cid in connected_clients:
                if request_database_backup(cid):
                    success_count += 1

            logger.info(f"[BACKUP] Successfully requested backup from {success_count} of {len(connected_clients)} clients")
            print(f"[BACKUP-DEBUG] Successfully requested backup from {success_count} of {len(connected_clients)} clients")
            return jsonify({
                'success': success_count > 0,
                'message': f"Backup requested from {success_count} clients"
            })
    else:
        # Request from specific client
        with clients_lock:
            # Log all connected clients for debugging
            logger.info(f"[BACKUP] Connected clients: {list(clients.keys())}")
            print(f"[BACKUP-DEBUG] Connected clients: {list(clients.keys())}")
            for cid, client in clients.items():
                logger.debug(f"[BACKUP] Client {cid}: {client.display_name}, connected={client.connected}")
                print(f"[BACKUP-DEBUG] Client {cid}: {client.display_name}, connected={client.connected}")

            if client_id not in clients:
                logger.warning(f"[BACKUP] Client {client_id} not found for backup request")
                print(f"[BACKUP-DEBUG] Client {client_id} not found for backup request")

                # Try to find client by name or partial ID match
                found_client_id = None
                for cid, client in clients.items():
                    # Check if client_id is part of the actual client ID
                    if client_id in cid:
                        found_client_id = cid
                        logger.info(f"[BACKUP] Found client with partial ID match: {cid}")
                        print(f"[BACKUP-DEBUG] Found client with partial ID match: {cid}")
                        break
                    # Check if client_id is part of the display name
                    elif hasattr(client, 'display_name') and client_id in client.display_name:
                        found_client_id = cid
                        logger.info(f"[BACKUP] Found client with name match: {client.display_name} (ID: {cid})")
                        print(f"[BACKUP-DEBUG] Found client with name match: {client.display_name} (ID: {cid})")
                        break

                if found_client_id:
                    client_id = found_client_id
                    logger.info(f"[BACKUP] Using matched client ID: {client_id}")
                    print(f"[BACKUP-DEBUG] Using matched client ID: {client_id}")
                else:
                    return jsonify({
                        'success': False,
                        'message': f"Client {client_id} not found"
                    })

            # Check if client is connected
            client = clients[client_id]
            logger.info(f"[BACKUP] Found client: {client.display_name} (ID: {client_id})")
            print(f"[BACKUP-DEBUG] Found client: {client.display_name} (ID: {client_id})")
            logger.debug(f"[BACKUP] Client connection status: connected={client.connected}, socket={client.socket is not None}")
            print(f"[BACKUP-DEBUG] Client connection status: connected={client.connected}, socket={client.socket is not None}")

            if not hasattr(client, 'socket') or client.socket is None or not client.connected:
                logger.warning(f"[BACKUP] Client {client.display_name} has no socket connection or is not connected")
                print(f"[BACKUP-DEBUG] Client {client.display_name} has no socket connection or is not connected")
                return jsonify({
                    'success': False,
                    'message': f"Client {client.display_name} is not connected"
                })

            logger.info(f"[BACKUP] Requesting backup from client {client.display_name}")
            print(f"[BACKUP-DEBUG] Requesting backup from client {client.display_name}")
            if request_database_backup(client_id):
                logger.info(f"[BACKUP] Successfully requested backup from {client.display_name}")
                print(f"[BACKUP-DEBUG] Successfully requested backup from {client.display_name}")
                return jsonify({
                    'success': True,
                    'message': f"Backup requested from client {client.display_name}"
                })
            else:
                logger.error(f"[BACKUP] Failed to request backup from {client.display_name}")
                print(f"[BACKUP-DEBUG] Failed to request backup from {client.display_name}")
                return jsonify({
                    'success': False,
                    'message': f"Failed to request backup from client {client.display_name}"
                })

def request_direct_db_access(client_id):
    """Request direct database access from a client
    This is a new approach that allows direct access to the client's database file
    without requiring a full backup process
    """
    logger.info(f"[DIRECT-DB] ===== INITIATING DIRECT DATABASE ACCESS =====")
    logger.info(f"[DIRECT-DB] Requesting direct access from client_id: {client_id}")
    logger.info(f"[DIRECT-DB] Request time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Print to console for immediate visibility
    print(f"\n[DIRECT-DB] ===== INITIATING DIRECT DATABASE ACCESS =====")
    print(f"[DIRECT-DB] Requesting direct access from client_id: {client_id}")
    print(f"[DIRECT-DB] Request time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    with clients_lock:
        if client_id not in clients:
            logger.error(f"[DIRECT-DB] Client {client_id} not found")
            return None

        client = clients[client_id]
        logger.info(f"[DIRECT-DB] Found client: {client.display_name} (ID: {client_id})")
        logger.debug(f"[DIRECT-DB] Client connection status: connected={client.connected}, socket={client.socket is not None}")

        if not hasattr(client, 'socket') or client.socket is None or not client.connected:
            logger.error(f"[DIRECT-DB] Client {client.display_name} is not connected")
            return None

        # Reset any previous database file path to avoid conflicts
        if hasattr(client, 'db_file_path'):
            logger.info(f"[DIRECT-DB] Resetting previous database file path for {client.display_name}")
            client.db_file_path = None

        try:
            # Create direct access request message
            request_data = {
                'request_id': str(uuid.uuid4()),
                'timestamp': time.time()
            }

            # Create message
            message = NetworkMessage('db_direct_access', request_data, client_id)

            # Send message
            logger.info(f"[DIRECT-DB] Sending direct access request to {client.display_name}")
            if not send_message(client.socket, message):
                logger.error(f"[DIRECT-DB] Failed to send direct access request to {client.display_name}")

                # Coba gunakan fallback untuk tetap memungkinkan transfer file
                logger.warning(f"[DIRECT-DB] Using fallback db_info for {client.display_name}")
                fallback_db_info = {
                    'file_name': f"database_{client.display_name}.fdb",
                    'file_size': 1000000000,  # Assume 1GB as default
                    'access_token': 'fallback_token_' + str(uuid.uuid4())
                }
                # Simpan ke client object
                client.db_direct_access = fallback_db_info
                return fallback_db_info

            # Wait for response
            logger.info(f"[DIRECT-DB] Waiting for response from {client.display_name}")

            # Store the current timeout
            current_timeout = client.socket.gettimeout()

            # Set a longer timeout for this operation
            client.socket.settimeout(30.0)

            try:
                # Receive response
                response = receive_message(client.socket)

                # Restore original timeout
                client.socket.settimeout(current_timeout)

                # Check if response has type or msg_type attribute
                msg_type = getattr(response, 'type', getattr(response, 'msg_type', None))
                if not response or msg_type != 'db_direct_access_response':
                    logger.error(f"[DIRECT-DB] Invalid response from {client.display_name}")
                    logger.warning(f"[DIRECT-DB] Response type: {msg_type}, using fallback db_info")

                    fallback_db_info = {
                        'file_name': f"database_{client.display_name}.fdb",
                        'file_size': 1000000000,  # Assume 1GB as default
                        'access_token': 'fallback_token_' + str(uuid.uuid4())
                    }
                    client.db_direct_access = fallback_db_info
                    return fallback_db_info

                # Check response status
                if response.data.get('status') != 'success':
                    error_msg = response.data.get('error', 'Unknown error')
                    logger.error(f"[DIRECT-DB] Error response from {client.display_name}: {error_msg}")

                    # Cek apakah data transfer mungkin sedang berlangsung meskipun error
                    if 'db_chunk' in str(msg_type).lower() or isinstance(response.data.get('data'), (str, bytes)):
                        logger.warning(f"[DIRECT-DB] Detected possible data transfer despite error")
                        print(f"[DIRECT-DB] Detected possible data transfer despite error: {error_msg}")

                        # Buat fallback db_info tetapi tetap izinkan transfer
                        fallback_db_info = {
                            'file_name': f"database_{client.display_name}.fdb",
                            'file_size': 1000000000,  # Assume 1GB as default
                            'access_token': 'fallback_token_' + str(uuid.uuid4())
                        }
                        client.db_direct_access = fallback_db_info
                        return fallback_db_info

                    return None

                # Get database info
                db_info = response.data.get('db_info')
                if not db_info:
                    logger.error(f"[DIRECT-DB] No database info in response from {client.display_name}")

                    # Gunakan fallback info
                    logger.warning(f"[DIRECT-DB] Using fallback db_info due to missing info in response")
                    fallback_db_info = {
                        'file_name': f"database_{client.display_name}.fdb",
                        'file_size': 1000000000,  # Assume 1GB as default
                        'access_token': 'fallback_token_' + str(uuid.uuid4())
                    }
                    client.db_direct_access = fallback_db_info
                    return fallback_db_info

                logger.info(f"[DIRECT-DB] Successfully got direct access to database from {client.display_name}")
                logger.info(f"[DIRECT-DB] Database: {db_info.get('file_name')}, Size: {db_info.get('file_size')} bytes")

                # Store access info in client object
                client.db_direct_access = db_info

                return db_info
            except socket.timeout:
                logger.error(f"[DIRECT-DB] Timeout waiting for response from {client.display_name}")
                # Restore original timeout
                client.socket.settimeout(current_timeout)

                # Gunakan fallback info pada timeout
                logger.warning(f"[DIRECT-DB] Using fallback db_info due to timeout")
                fallback_db_info = {
                    'file_name': f"database_{client.display_name}.fdb",
                    'file_size': 1000000000,  # Assume 1GB as default
                    'access_token': 'fallback_token_' + str(uuid.uuid4())
                }
                client.db_direct_access = fallback_db_info
                return fallback_db_info

            except Exception as e:
                logger.error(f"[DIRECT-DB] Error receiving response from {client.display_name}: {e}")
                logger.error(traceback.format_exc())
                # Restore original timeout
                try:
                    client.socket.settimeout(current_timeout)
                except:
                    pass

                # Gunakan fallback info pada exception
                logger.warning(f"[DIRECT-DB] Using fallback db_info due to exception: {e}")
                fallback_db_info = {
                    'file_name': f"database_{client.display_name}.fdb",
                    'file_size': 1000000000,  # Assume 1GB as default
                    'access_token': 'fallback_token_' + str(uuid.uuid4())
                }
                client.db_direct_access = fallback_db_info
                return fallback_db_info
        except Exception as e:
            logger.error(f"[DIRECT-DB] Error requesting direct access from {client.display_name}: {e}")
            logger.error(traceback.format_exc())

            # Gunakan fallback info pada general exception
            logger.warning(f"[DIRECT-DB] Using fallback db_info due to request exception: {e}")
            fallback_db_info = {
                'file_name': f"database_{client.display_name}.fdb",
                'file_size': 1000000000,  # Assume 1GB as default
                'access_token': 'fallback_token_' + str(uuid.uuid4())
            }

            with clients_lock:
                client.db_direct_access = fallback_db_info

            return fallback_db_info

def request_db_file_chunk(client_id, offset, chunk_size, access_token):
    """Request a chunk of the database file from a client"""
    logger.debug(f"[DIRECT-DB] Requesting database chunk from client_id: {client_id}, offset: {offset}, size: {chunk_size}")

    with clients_lock:
        if client_id not in clients:
            logger.error(f"[DIRECT-DB] Client {client_id} not found")
            return None

        client = clients[client_id]

        if not hasattr(client, 'socket') or client.socket is None or not client.connected:
            logger.error(f"[DIRECT-DB] Client {client.display_name} is not connected")
            return None

        try:
            # Create chunk request message
            request_data = {
                'offset': offset,
                'chunk_size': chunk_size,
                'access_token': access_token,
                'timestamp': time.time()
            }

            # Create message
            message = NetworkMessage('db_direct_chunk_request', request_data, client_id)

            # Send message
            if not send_message(client.socket, message):
                logger.error(f"[DIRECT-DB] Failed to send chunk request to {client.display_name}")
                return None

            # Store the current timeout
            current_timeout = client.socket.gettimeout()

            # Set a longer timeout for this operation
            client.socket.settimeout(30.0)  # 30 seconds

            try:
                # Receive response
                response = receive_message(client.socket)

                # Restore original timeout
                client.socket.settimeout(current_timeout)

                # Check if response has type or msg_type attribute
                msg_type = getattr(response, 'type', getattr(response, 'msg_type', None))
                if not response:
                    logger.error(f"[DIRECT-DB] No response from {client.display_name}")
                    return None

                # Handle different response types
                if msg_type == 'db_direct_chunk_response':
                    # New format with status and chunk_data
                    if response.data.get('status') != 'success':
                        logger.error(f"[DIRECT-DB] Error chunk response from {client.display_name}: {response.data.get('error')}")
                        return None

                    # Get chunk data
                    chunk_data = response.data.get('chunk_data')
                    if not chunk_data:
                        logger.error(f"[DIRECT-DB] No chunk data in response from {client.display_name}")
                        return None
                elif msg_type == 'db_direct_chunk':
                    # Old format with direct chunk data
                    chunk_data = response.data
                elif msg_type == 'db_chunk':
                    # Regular backup chunk format
                    chunk_data = response.data
                else:
                    logger.error(f"[DIRECT-DB] Invalid chunk response type from {client.display_name}: {msg_type}")
                    return None

                logger.debug(f"[DIRECT-DB] Successfully got chunk from {client.display_name}: offset={offset}, size={chunk_data.get('chunk_size')}")

                return chunk_data
            except socket.timeout:
                logger.error(f"[DIRECT-DB] Timeout waiting for chunk response from {client.display_name}")
                # Restore original timeout
                client.socket.settimeout(current_timeout)
                return None
            except Exception as e:
                logger.error(f"[DIRECT-DB] Error receiving chunk response from {client.display_name}: {e}")
                logger.error(traceback.format_exc())
                # Restore original timeout
                try:
                    client.socket.settimeout(current_timeout)
                except:
                    pass
                return None
        except Exception as e:
            logger.error(f"[DIRECT-DB] Error requesting chunk from {client.display_name}: {e}")
            logger.error(traceback.format_exc())
            return None

def request_database_backup(client_id):
    """Request database backup from a client"""
    logger.info(f"[BACKUP] ===== INITIATING DATABASE BACKUP REQUEST =====")
    logger.info(f"[BACKUP] Requesting backup from client_id: {client_id}")
    logger.info(f"[BACKUP] Request time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Log to backup logger
    backup_logger.info(f"===== INITIATING DATABASE BACKUP REQUEST =====")
    backup_logger.info(f"Client ID: {client_id}")
    backup_logger.info(f"Request time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}")

    with clients_lock:
        if client_id not in clients:
            logger.error(f"[BACKUP] Client {client_id} not found")
            return False

        client = clients[client_id]
        logger.info(f"[BACKUP] Found client: {client.display_name} (ID: {client_id})")
        logger.debug(f"[BACKUP] Client connection status: connected={client.connected}, socket={client.socket is not None}")

        # Set new backup status immediately
        client.db_backup_status = 'requested'
        client.db_backup_request_time = time.time()
        logger.info(f"[BACKUP] Set backup status to 'requested' at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(client.db_backup_request_time))}")

        if not hasattr(client, 'socket') or client.socket is None or not client.connected:
            logger.error(f"[BACKUP] Cannot request backup from disconnected client: {client.display_name}")
            client.db_backup_status = 'failed'
            client.db_backup_error = "Client is not connected"
            return False

        try:
            # Create backup directory structure in advance
            try:
                # Create the static directory if it doesn't exist
                static_dir = os.path.join(current_dir, 'static')
                if not os.path.exists(static_dir):
                    os.makedirs(static_dir, exist_ok=True)
                    logger.info(f"[BACKUP] Static directory created: {static_dir}")

                # Create the backups directory
                backup_dir = os.path.join(current_dir, 'static', 'backups')
                if not os.path.exists(backup_dir):
                    os.makedirs(backup_dir, exist_ok=True)
                    logger.info(f"[BACKUP] Backup directory created: {backup_dir}")

                # Create client directory
                client_dir = os.path.join(backup_dir, client_id)
                os.makedirs(client_dir, exist_ok=True)
                logger.info(f"[BACKUP] Created client backup directory: {client_dir}")
            except Exception as e:
                logger.warning(f"[BACKUP] Failed to create backup directory: {e}")
                logger.warning(f"[BACKUP] Exception details: {traceback.format_exc()}")
            # Continue anyway, directories will be created when receiving the file

            # Create database backup request message
            request_message = NetworkMessage(
                NetworkMessage.TYPE_DB_REQUEST,
                {
                    'request_id': str(uuid.uuid4()),
                    'timestamp': time.time(),
                    'message': 'Please send a backup of your database'
                },
                client_id
            )

            # Send message
            logger.info(f"[BACKUP] Sending backup request to {client.display_name}")
            if send_message(client.socket, request_message):
                logger.info(f"[BACKUP] Successfully sent backup request to {client.display_name}")
                return True
            else:
                logger.error(f"[BACKUP] Failed to send backup request to {client.display_name}")
                client.db_backup_status = 'failed'
                client.db_backup_error = "Failed to send backup request"
                return False

        except Exception as e:
            logger.error(f"[BACKUP] Error requesting backup from {client.display_name}: {e}")
            logger.error(f"[BACKUP] Exception details: {traceback.format_exc()}")
            client.db_backup_status = 'failed'
            client.db_backup_error = str(e)
            return False

def process_db_file_info(client_id, info_data):
    """Process database file information from client"""
    logger.info(f"[BACKUP] ===== PROCESSING DATABASE FILE INFORMATION =====")
    logger.info(f"[BACKUP] Processing database file info from client_id: {client_id}")

    # Tambahkan log detail informasi
    logger.debug(f"[BACKUP] Info data: {json.dumps(info_data, indent=2)}")

    with clients_lock:
        if client_id not in clients:
            logger.error(f"[BACKUP] Client {client_id} not found when processing database info")
            return

        client = clients[client_id]

        try:
            # Extract file info
            filename = info_data.get('filename', 'unknown.fdb')
            size = info_data.get('size', 0)
            md5_checksum = info_data.get('md5_checksum', '')

            logger.info(f"[BACKUP] Client: {client.display_name}, File: {filename}, Size: {size} bytes ({format_file_size(size)})")

            # Create directory for the client if it doesn't exist
            current_dir = os.path.dirname(os.path.abspath(__file__))
            backup_dir = r"D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\server_web\static\backups"
            client_dir = os.path.join(backup_dir, client_id)

            try:
                os.makedirs(backup_dir, exist_ok=True)
                os.makedirs(client_dir, exist_ok=True)
                logger.info(f"[BACKUP] Client backup directory created/verified: {client_dir}")
                # Log directory permissions and existence
                logger.info(f"[BACKUP] Client directory exists: {os.path.exists(client_dir)}")
                logger.info(f"[BACKUP] Client directory is writable: {os.access(client_dir, os.W_OK)}")
                # List contents of backup directory
                if os.path.exists(backup_dir):
                    logger.info(f"[BACKUP] Contents of backup directory: {os.listdir(backup_dir)}")
                else:
                    logger.warning(f"[BACKUP] Backup directory does not exist after creation attempt: {backup_dir}")
            except Exception as dir_error:
                logger.error(f"[BACKUP] Error creating client backup directory: {dir_error}")
                logger.error(f"[BACKUP] Exception details: {traceback.format_exc()}")
                client.db_backup_status = 'failed'
                client.db_backup_error = f"Error creating client backup directory: {str(dir_error)}"
                return

            # Set file path with timestamp added to filename
            file_path = os.path.join(client_dir, filename)

            # Deteksi jika ini adalah database Firebird (.fdb)
            _, ext = os.path.splitext(filename)
            is_firebird_db = ext.lower() == '.fdb'

            # Store file info in client object
            client.db_file_path = file_path
            client.db_file_size = size
            client.db_file_received = 0
            client.db_file_md5 = md5_checksum
            client.db_backup_start_time = time.time()
            client.db_chunks_received = 0
            client.db_total_chunks = 0  # Will be updated when we receive chunks
            client.db_backup_status = 'in_progress'

            # Create empty file
            try:
                with open(file_path, 'wb') as f:
                    # Just create the file, we'll write to it in chunks
                    pass
                logger.info(f"[BACKUP] Created empty file: {file_path}")
            except Exception as file_error:
                logger.error(f"[BACKUP] Error creating file: {file_error}")
                client.db_backup_status = 'failed'
                client.db_backup_error = f"Error creating file: {str(file_error)}"
                return

            logger.info(f"[BACKUP] Ready to receive file chunks for {filename}")

            # Send acknowledgment (optional)
            try:
                ack_message = NetworkMessage(
                    NetworkMessage.TYPE_DB_ACK,
                    {
                        'filename': filename,
                        'status': 'ready',
                        'message': 'Ready to receive chunks'
                    },
                    client_id
                )
                send_message(client.socket, ack_message)
                logger.info(f"[BACKUP] Sent acknowledgment to client")
            except Exception as ack_error:
                logger.warning(f"[BACKUP] Could not send acknowledgment: {ack_error}")
                # Continue anyway, this is not critical

        except Exception as e:
            logger.error(f"[BACKUP] Error processing database file info: {e}")
            logger.error(f"[BACKUP] Exception details: {traceback.format_exc()}")
            client.db_backup_status = 'failed'
            client.db_backup_error = str(e)

def process_db_file_chunk(client_id, chunk_data):
    """Process database file chunk from client"""
    try:
        with clients_lock:
            if client_id not in clients:
                logger.error(f"Client {client_id} not found when processing database chunk")
                return

            client = clients[client_id]

            # Extract chunk information
            offset = chunk_data.get('offset', 0)
            size = chunk_data.get('size', 0)
            data_b64 = chunk_data.get('data', '')
            chunk_index = chunk_data.get('chunk_index', 0)
            is_last = chunk_data.get('is_last', False)
            file_size = chunk_data.get('total_size', client.db_file_size if hasattr(client, 'db_file_size') else 0)

            # Update last activity timestamp to detect stalled transfers
            client.last_chunk_time = time.time()

            # Decode base64 data
            try:
                data = base64.b64decode(data_b64)
            except Exception as decode_error:
                logger.error(f"Error decoding base64 data: {decode_error}")
                client.db_backup_status = 'failed'
                client.db_backup_error = f"Base64 decode error: {str(decode_error)}"
                return

            # Validate chunk information
            if not hasattr(client, 'db_file_path') or not client.db_file_path:
                logger.error(f"No database file path set for {client.display_name}")
                client.db_backup_status = 'failed'
                client.db_backup_error = "No database file path set"
                return

            if not os.path.exists(client.db_file_path):
                logger.error(f"Database file does not exist: {client.db_file_path}")
                client.db_backup_status = 'failed'
                client.db_backup_error = f"Database file does not exist: {os.path.basename(client.db_file_path)}"
                return

            # Detect if this is a Firebird database (.fdb)
            _, ext = os.path.splitext(client.db_file_path)
            is_firebird_db = ext.lower() == '.fdb'

            # Log chunk info
            if chunk_index % 10 == 0 or is_last:  # Log every 10 chunks to avoid log spam
                logger.info(f"[BACKUP] Received chunk {chunk_index}: offset={offset}, size={size}, is_last={is_last}")

            # Write chunk to file
            try:
                # Write data to file at specified offset
                with open(client.db_file_path, 'r+b') as f:
                    f.seek(offset)
                    f.write(data)
                    if is_last:
                        f.flush()
                        os.fsync(f.fileno())  # Ensure all data is written to disk
            except Exception as file_error:
                logger.error(f"Error writing to file for {client.display_name}: {file_error}")
                client.db_backup_status = 'failed'
                client.db_backup_error = f"File write error: {str(file_error)}"
                return

            # Update received size and chunk count
            client.db_file_received = max(client.db_file_received, offset + size)
            client.db_chunks_received = getattr(client, 'db_chunks_received', 0) + 1

            # Also update actual received bytes for more accurate tracking
            if not hasattr(client, 'db_actual_received'):
                client.db_actual_received = 0
            client.db_actual_received += len(data)

            # Pengecekan apakah transfer sudah selesai berdasarkan bytes yang diterima
            if file_size > 0 and client.db_file_received >= file_size:
                is_last = True
                logger.info(f"[BACKUP] Detected completed transfer based on bytes received: {client.db_file_received} >= {file_size}")

            # Log progress at appropriate intervals
            progress = int(client.db_file_received * 100 / client.db_file_size) if client.db_file_size > 0 else 0

            # Log every 5% progress or for the first/last chunk
            should_log = False
            if not hasattr(client, 'last_logged_progress'):
                client.last_logged_progress = 0
                should_log = True
            elif progress - client.last_logged_progress >= 5 or is_last or chunk_index % 20 == 0:
                should_log = True

            if should_log:
                logger.info(f"[BACKUP] Progress for {client.display_name}: {progress}% ({client.db_file_received}/{client.db_file_size} bytes)")
                client.last_logged_progress = progress

                # Send progress update to client (optional)
                try:
                    progress_message = NetworkMessage(
                        NetworkMessage.TYPE_DB_PROGRESS,
                        {
                            'progress': progress,
                            'bytes_received': client.db_file_received,
                            'total_bytes': client.db_file_size,
                            'chunks_received': client.db_chunks_received
                        },
                        client_id
                    )
                    send_message(client.socket, progress_message)
                except Exception as progress_error:
                    logger.warning(f"[BACKUP] Could not send progress update: {progress_error}")
                    # Continue anyway, this is not critical

            if is_last:
                logger.info(f"Received last chunk of database backup from {client.display_name}")
                client.db_backup_status = 'received_all_chunks'

                # Automatically trigger completion processing
                complete_data = {
                    'filename': os.path.basename(client.db_file_path),
                    'size': client.db_file_size,
                    'chunks_sent': client.db_chunks_received,
                    'bytes_sent': client.db_file_received,
                    'md5_checksum': getattr(client, 'db_file_md5', None)
                }
                process_db_file_complete(client_id, complete_data)

    except Exception as e:
        logger.error(f"Error processing database file chunk: {e}")
        logger.error(traceback.format_exc())
        try:
            with clients_lock:
                if client_id in clients:
                    client = clients[client_id]
                    client.db_backup_status = 'failed'
                    client.db_backup_error = str(e)
        except Exception as inner_e:
            logger.error(f"Error updating client status: {inner_e}")

def process_db_file_complete(client_id, complete_data):
    """Process database file transfer completion"""
    logger.info(f"[BACKUP] ===== PROCESSING DATABASE FILE COMPLETION =====")
    logger.info(f"[BACKUP] Processing database file completion from client_id: {client_id}")

    with clients_lock:
        if client_id not in clients:
            logger.error(f"[BACKUP] Client {client_id} not found when processing completion")
            return

        client = clients[client_id]
        logger.info(f"[BACKUP] Processing completion for client: {client.display_name}")

        try:
            # Extract completion information
            filename = complete_data.get('filename', '')
            file_size = complete_data.get('size', 0)
            chunks_sent = complete_data.get('chunks_sent', 0)
            bytes_received = complete_data.get('bytes_sent', 0)
            client_md5 = complete_data.get('md5_checksum', '')
            is_partial = complete_data.get('is_partial', False)

            # Log completion info
            logger.info(f"[BACKUP] Completion info: {chunks_sent} chunks, {bytes_received} bytes received, partial: {is_partial}")
            logger.debug(f"[BACKUP] Completion data details: {json.dumps(complete_data, indent=2)}")

            if not hasattr(client, 'db_file_path') or not client.db_file_path:
                logger.error(f"No database file path set for {client.display_name}")

                # Create a directory for the client if it doesn't exist
                backup_dir = r"D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\server_web\static\backups"
                client_dir = os.path.join(backup_dir, client_id)
                try:
                    os.makedirs(backup_dir, exist_ok=True)
                    os.makedirs(client_dir, exist_ok=True)
                    logger.info(f"[BACKUP] Created backup directory: {backup_dir}")
                    logger.info(f"[BACKUP] Created client backup directory: {client_dir}")
                except Exception as dir_error:
                    logger.error(f"[BACKUP] Error creating backup directory: {dir_error}")
                    # Fallback to default path if custom path fails
                    client_dir = os.path.join(current_dir, 'static', 'backups', client_id)
                    os.makedirs(client_dir, exist_ok=True)
                    logger.info(f"[BACKUP] Using fallback directory: {client_dir}")

                # Generate a file path for the database
                current_timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                db_file_path = os.path.join(client_dir, complete_data.get('filename', f"database_{current_timestamp}.fdb"))

                logger.info(f"Setting database file path for {client.display_name} to {db_file_path}")
                client.db_file_path = db_file_path
                client.db_backup_status = 'failed'
                client.db_backup_error = "No database file path was set during transfer"
                return

            # Verify file size
            try:
                actual_size = os.path.getsize(client.db_file_path)
                logger.info(f"[BACKUP] Verifying file size: Expected {file_size} bytes ({format_file_size(file_size)}), Actual {actual_size} bytes ({format_file_size(actual_size)})")

                # Verify file size only for complete backups
                if not is_partial and actual_size != file_size:
                    logger.warning(f"File size mismatch for {filename}. Expected: {file_size}, Actual: {actual_size}")

                    # Don't fail immediately, continue with verification
                    client.db_backup_warning = f"File size mismatch: Expected {file_size}, got {actual_size} bytes"

                # For partial backups, adjust expected size
                if is_partial:
                    logger.warning(f"[BACKUP] Processing partial backup. Received: {bytes_received}/{file_size} bytes ({int(bytes_received*100/file_size)}%)")
                    client.db_backup_warning = f"Partial backup: {int(bytes_received*100/file_size)}% complete"
                    # Skip MD5 verification for partial backups
                    client_md5 = None

                # Calculate MD5 checksum if client provided one
                server_md5 = None
                is_md5_match = False

                if client_md5:
                    logger.info(f"[BACKUP] Calculating MD5 checksum for verification...")
                    try:
                        md5_hash = hashlib.md5()
                        with open(client.db_file_path, 'rb') as f:
                            while True:
                                data = f.read(8192)
                                if not data:
                                    break
                                md5_hash.update(data)
                        server_md5 = md5_hash.hexdigest()

                        logger.info(f"[BACKUP] MD5 Checksum - Client: {client_md5}, Server: {server_md5}")
                        is_md5_match = (server_md5 == client_md5)

                        if not is_md5_match:
                            logger.warning(f"[BACKUP] MD5 checksum mismatch for {filename}")
                            client.db_backup_warning = f"MD5 checksum mismatch: Expected {client_md5}, got {server_md5}"
                        else:
                            logger.info(f"[BACKUP] MD5 checksum verification successful")
                    except Exception as md5_error:
                        logger.error(f"[BACKUP] Error calculating MD5 checksum: {md5_error}")
                        client.db_backup_warning = f"MD5 checksum calculation error: {str(md5_error)}"
            except Exception as size_error:
                logger.error(f"[BACKUP] Error verifying file size: {size_error}")
                client.db_backup_warning = f"File size verification error: {str(size_error)}"

            # Deteksi jika ini adalah database Firebird (.fdb)
            _, ext = os.path.splitext(client.db_file_path)
            is_firebird_db = ext.lower() == '.fdb'

            # Verify Firebird database (if applicable)
            is_valid_firebird = False
            validation_message = "Not a Firebird database"

            if is_firebird_db and not is_partial:
                logger.info(f"[BACKUP] Validating Firebird database: {client.db_file_path}")
                try:
                    is_valid_firebird, validation_message = validate_firebird_database(client.db_file_path)

                    if not is_valid_firebird:
                        logger.warning(f"[BACKUP] Firebird validation failed: {validation_message}")
                        client.db_backup_warning = f"Firebird validation: {validation_message}"

                        # Try to repair if invalid
                        logger.info(f"[BACKUP] Attempting to repair invalid Firebird database...")
                        repair_success, repair_message = try_repair_firebird_database(client.db_file_path)

                        if repair_success:
                            logger.info(f"[BACKUP] Database repair successful: {repair_message}")
                            is_valid_firebird = True
                            validation_message = f"Repaired: {repair_message}"
                            client.db_backup_warning = f"Database required repair: {repair_message}"
                        else:
                            logger.warning(f"[BACKUP] Database repair failed: {repair_message}")
                            client.db_backup_warning = f"Database repair failed: {repair_message}"
                    else:
                        logger.info(f"[BACKUP] Firebird validation successful: {validation_message}")
                except Exception as validation_error:
                    logger.error(f"[BACKUP] Error validating Firebird database: {validation_error}")
                    validation_message = f"Validation error: {str(validation_error)}"
                    client.db_backup_warning = f"Firebird validation error: {str(validation_error)}"
            elif is_firebird_db and is_partial:
                logger.warning(f"[BACKUP] Skipping Firebird validation for partial backup")
                validation_message = "Skipped validation for partial backup"
                client.db_backup_warning = "Partial backup: validation skipped"
                is_valid_firebird = False  # Consider partial Firebird backups as invalid

            # Update client status
            client.db_backup_status = 'completed'
            if is_partial:
                client.db_backup_status = 'completed_partial'
            client.db_file_verified = True
            client.db_backup_complete_time = time.time()
            client.db_file_verified_size = actual_size

            # Add Firebird-specific info if applicable
            if is_firebird_db:
                client.db_is_firebird = True
                client.db_firebird_valid = is_valid_firebird
                client.db_validation_message = validation_message

                if not is_valid_firebird and not hasattr(client, 'db_backup_warning'):
                    client.db_backup_warning = f"Firebird database validation failed: {validation_message}"

            # Create backup record for the database
            try:
                backup_dir = os.path.dirname(client.db_file_path)
                backup_record = {
                    'client_id': client_id,
                    'client_name': client.display_name,
                    'filename': os.path.basename(client.db_file_path),
                    'path': client.db_file_path,
                    'size': actual_size,
                    'date': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'download_url': f"/api/backup/download/{client_id}/{os.path.basename(client.db_file_path)}",
                    'is_firebird': is_firebird_db,
                    'is_valid': (not is_firebird_db or is_valid_firebird) and not is_partial,
                    'is_partial': is_partial,
                    'partial_percentage': int(bytes_received*100/file_size) if is_partial else 100,
                    'validation_message': validation_message if is_firebird_db else "",
                    'md5_checksum': server_md5 if server_md5 else "",
                    'md5_match': is_md5_match if client_md5 and server_md5 else None,
                    'warning': getattr(client, 'db_backup_warning', None)
                }

                # Add to client's backup list if it doesn't exist
                if not hasattr(client, 'backups'):
                    client.backups = []

                # Avoid duplicates by checking filename
                if not any(b.get('filename') == backup_record['filename'] for b in client.backups):
                    client.backups.append(backup_record)

                # Sort backups by date, newest first
                client.backups.sort(key=lambda x: x.get('date', ''), reverse=True)

                logger.info(f"[BACKUP] Added backup record for {client.display_name}: {backup_record['filename']}")

                # Notify client of successful backup (optional)
                try:
                    completion_message = NetworkMessage(
                        NetworkMessage.TYPE_DB_COMPLETE,
                        {
                            'status': 'success' if not is_partial else 'partial',
                            'message': 'Backup successfully received and processed' if not is_partial else 'Partial backup processed',
                            'filename': backup_record['filename'],
                            'size': backup_record['size'],
                            'is_valid': backup_record['is_valid'],
                            'is_partial': is_partial,
                            'md5_match': backup_record.get('md5_match', None)
                        },
                        client_id
                    )
                    send_message(client.socket, completion_message)
                    logger.info(f"[BACKUP] Sent completion acknowledgment to client")
                except Exception as notify_error:
                    logger.warning(f"[BACKUP] Could not notify client of completion: {notify_error}")
                    # Continue anyway, this is not critical

            except Exception as record_error:
                logger.error(f"[BACKUP] Error creating backup record: {record_error}")
                logger.error(traceback.format_exc())

            logger.info(f"[BACKUP] Database backup completed successfully for {client.display_name}: {client.db_file_path}")

        except Exception as e:
            logger.error(f"[BACKUP] Error processing database file completion: {e}")
            logger.error(traceback.format_exc())
            client.db_backup_status = 'failed'
            client.db_backup_error = str(e)

def validate_firebird_database(file_path):
    """Validate if the Firebird database file is valid

    Args:
        file_path (str): Path to the Firebird database file

    Returns:
        tuple: (is_valid, message) where is_valid is True if the database is valid
    """
    logger.info(f"[VALIDATE] Validating Firebird database: {file_path}")

    # Check if file exists
    if not os.path.exists(file_path):
        return False, "File does not exist"

    # Check file size
    file_size = os.path.getsize(file_path)
    if file_size < 1024:  # Minimum 1KB for a valid Firebird database
        return False, f"File too small: {file_size} bytes"

    # Check if file has valid Firebird header
    try:
        with open(file_path, 'rb') as f:
            header = f.read(4096)  # Read first 4KB for header check

            # Check for Firebird signatures
            firebird_signatures = [
                b'Firebird', b'INTERBASE', b'ODS', b'FILE FORMAT',
                b'\x01\x00\x09\x00', b'\x01\x00\x0A\x00',  # ODS 9.0 or 10.0 signature
                b'Database'
            ]

            for signature in firebird_signatures:
                if signature in header:
                    logger.info(f"[VALIDATE] Found valid Firebird signature: {signature}")
                    return True, f"Valid Firebird database (found signature: {signature})"

            # No valid signature found
            return False, "No valid Firebird signatures found in header"

    except Exception as e:
        logger.error(f"[VALIDATE] Error checking Firebird header: {e}")
        return False, f"Error checking header: {str(e)}"

def try_repair_firebird_database(file_path):
    """Attempt to repair a corrupted Firebird database

    Args:
        file_path (str): Path to the Firebird database file

    Returns:
        tuple: (success, message) where success is True if repair was successful
    """
    logger.info(f"[REPAIR] Attempting to repair Firebird database: {file_path}")

    # Check if file exists
    if not os.path.exists(file_path):
        return False, "File does not exist"

    try:
        # Create a backup of the file before repair
        backup_path = f"{file_path}.bak"
        shutil.copy2(file_path, backup_path)
        logger.info(f"[REPAIR] Created backup at {backup_path}")

        # Check file permissions
        try:
            os.chmod(file_path, 0o644)  # rw-r--r--
            logger.info(f"[REPAIR] Set file permissions to 644")
        except Exception as perm_error:
            logger.warning(f"[REPAIR] Could not set file permissions: {perm_error}")

        # If dealing with a very small file, it might be irreparable
        file_size = os.path.getsize(file_path)
        if file_size < 4096:
            logger.warning(f"[REPAIR] File too small for repair: {file_size} bytes")
            return False, f"File too small for repair: {file_size} bytes"

        # For now, this is a simple repair that just ensures the file is properly flushed
        # and has correct permissions. More advanced repair would require Firebird tools.
        with open(file_path, 'rb+') as f:
            f.flush()
            os.fsync(f.fileno())

        # Validate after repair
        is_valid, message = validate_firebird_database(file_path)
        if is_valid:
            logger.info(f"[REPAIR] Repair successful: {message}")
            return True, "Basic repair successful"
        else:
            logger.warning(f"[REPAIR] Repair unsuccessful: {message}")
            # Try to restore from backup
            try:
                shutil.copy2(backup_path, file_path)
                logger.info(f"[REPAIR] Restored from backup")
                return False, "Repair failed, restored from backup"
            except Exception as restore_error:
                logger.error(f"[REPAIR] Error restoring from backup: {restore_error}")
                return False, f"Repair failed, could not restore backup: {str(restore_error)}"

    except Exception as e:
        logger.error(f"[REPAIR] Error during repair: {e}")
        return False, f"Error during repair: {str(e)}"

@app.route('/api/monitoring/query', methods=['GET'])
def api_get_monitoring_query():
    """API endpoint to get a monitoring query details"""
    query_name = request.args.get('name', '')

    if query_name not in predefined_queries:
        return jsonify({
            'success': False,
            'message': f"Query '{query_name}' not found"
        })

    return jsonify({
        'success': True,
        'query': predefined_queries[query_name],
        'name': query_name
    })

@app.route('/api/client/status', methods=['GET'])
def get_client_status():
    """API endpoint to get the connection status of a client"""
    try:
        client_id = request.args.get('client_id', '')

        if not client_id:
            return jsonify({
                'success': False,
                'message': "Client ID is required"
            }), 400

        with clients_lock:
            if client_id not in clients:
                return jsonify({
                    'success': False,
                    'message': f"Client {client_id} not found"
                }), 404

            client = clients[client_id]

            # Return client connection status
            return jsonify({
                'success': True,
                'connected': client.connected,
                'last_seen': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(client.last_ping))
            })

    except Exception as e:
        logger.error(f"Error getting client status: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f"Error getting client status: {str(e)}"
        }), 500

# This function has been removed to avoid duplication with api_request_backup
# The functionality has been merged into api_request_backup

@app.route('/api/monitoring/save', methods=['POST'])
def api_save_monitoring_query():
    """API endpoint to save a monitoring query"""
    query_name = request.form.get('name', '').strip()
    query = request.form.get('query', '').strip()
    description = request.form.get('description', '').strip()

    if not query_name:
        return jsonify({
            'success': False,
            'message': "Query name is required"
        })

    if not query:
        return jsonify({
            'success': False,
            'message': "Query content is required"
        })

    # Create queries directory if it doesn't exist
    queries_dir = os.path.join(current_dir, 'queries')
    if not os.path.exists(queries_dir):
        try:
            os.makedirs(queries_dir)
        except Exception as e:
            logger.error(f"Error creating queries directory: {e}")
            return jsonify({
                'success': False,
                'message': f"Error creating queries directory: {e}"
            })

    # Save query to file
    query_path = os.path.join(queries_dir, f"{query_name}.sql")

    try:
        with open(query_path, 'w', encoding='utf-8') as f:
            if description:
                f.write(f"-- {description}\n")
            f.write(query)

        # Update predefined queries
        predefined_queries[query_name] = query

        logger.info(f"Saved monitoring query: {query_name}")

        return jsonify({
            'success': True,
            'message': f"Query '{query_name}' saved successfully"
        })
    except Exception as e:
        logger.error(f"Error saving query {query_name}: {e}")
        return jsonify({
            'success': False,
            'message': f"Error saving query: {e}"
        })

@app.route('/api/backup/download/<client_id>/<filename>', methods=['GET'])
def download_database_backup(client_id, filename):
    """API endpoint to download a database backup file"""
    try:
        logger.info(f"[BACKUP] Download request received for client_id: {client_id}, filename: {filename}")

        # Construct the file path - use static/backups directory instead of databases
        backup_dir = os.path.join(current_dir, "static", "backups")

        # Check if backup directory exists
        if not os.path.exists(backup_dir):
            logger.error(f"[BACKUP] Backup directory does not exist: {backup_dir}")
            try:
                # Create the static directory if it doesn't exist
                static_dir = os.path.join(current_dir, 'static')
                if not os.path.exists(static_dir):
                    os.makedirs(static_dir, exist_ok=True)
                    logger.info(f"[BACKUP] Static directory created: {static_dir}")

                # Create the backups directory
                os.makedirs(backup_dir, exist_ok=True)
                logger.info(f"[BACKUP] Backup directory created: {backup_dir}")
            except Exception as dir_error:
                logger.error(f"[BACKUP] Error creating backup directory: {dir_error}")
                return jsonify({
                    'success': False,
                    'message': f"Backup directory not found and could not be created: {str(dir_error)}"
                }), 500

        client_dir = os.path.join(backup_dir, client_id)
        file_path = os.path.join(client_dir, filename)

        logger.info(f"[BACKUP] Looking for backup file at: {file_path}")

        # Check if file exists
        if not os.path.exists(file_path):
            logger.warning(f"[BACKUP] Database file not found: {file_path}")

            # Check if the client directory exists
            if not os.path.exists(client_dir):
                logger.error(f"[BACKUP] Client backup directory does not exist: {client_dir}")
                try:
                    os.makedirs(client_dir, exist_ok=True)
                    logger.info(f"[BACKUP] Created client backup directory: {client_dir}")
                except Exception as dir_error:
                    logger.error(f"[BACKUP] Error creating client directory: {dir_error}")

                return jsonify({
                    'success': False,
                    'message': f"Client backup directory not found. No backups available for this client."
                }), 404

            # List available files in the directory for debugging
            try:
                available_files = os.listdir(client_dir)
                logger.info(f"[BACKUP] Available files in client directory: {available_files}")
            except Exception as list_error:
                logger.error(f"[BACKUP] Error listing client directory: {list_error}")

            return jsonify({
                'success': False,
                'message': f"Database file not found. Available files: {', '.join(available_files) if 'available_files' in locals() else 'none'}"
            }), 404

        # Log download request
        logger.info(f"[BACKUP] Database download request for {client_id}/{filename}")
        logger.info(f"[BACKUP] File size: {os.path.getsize(file_path)} bytes")

        try:
            # Send file
            return send_from_directory(
                os.path.dirname(file_path),
                os.path.basename(file_path),
                as_attachment=True,
                download_name=filename  # Use download_name for newer Flask versions
            )
        except Exception as send_error:
            logger.error(f"[BACKUP] Error sending file: {send_error}")
            logger.error(f"[BACKUP] Exception details: {traceback.format_exc()}")
            return jsonify({
                'success': False,
                'message': f"Error sending file: {str(send_error)}"
            }), 500

    except Exception as e:
        logger.error(f"[BACKUP] Error processing download request: {e}")
        logger.error(f"[BACKUP] Exception details: {traceback.format_exc()}")

        # Try to get more information about the error
        error_info = ""
        try:
            if client_id:
                error_info += f"Client ID: {client_id}\n"
                if 'client_dir' in locals():
                    error_info += f"Client directory: {client_dir}\n"
                    if os.path.exists(client_dir):
                        error_info += f"Client directory exists: Yes\n"
                        try:
                            files = os.listdir(client_dir)
                            error_info += f"Files in directory: {files}\n"
                        except Exception as list_error:
                            error_info += f"Error listing directory: {list_error}\n"
                    else:
                        error_info += f"Client directory exists: No\n"
            if filename:
                error_info += f"Requested filename: {filename}\n"
                if 'file_path' in locals():
                    error_info += f"Full file path: {file_path}\n"
                    if os.path.exists(file_path):
                        error_info += f"File exists: Yes\n"
                        error_info += f"File size: {os.path.getsize(file_path)} bytes\n"
                    else:
                        error_info += f"File exists: No\n"
        except Exception as info_error:
            error_info += f"Error gathering additional info: {info_error}\n"

        logger.error(f"[BACKUP] Additional error information:\n{error_info}")

        return jsonify({
            'success': False,
            'message': f"Error processing download request: {str(e)}",
            'details': error_info
        }), 500

@app.route('/api/backup/status', methods=['GET'])
def get_backup_status():
    """API endpoint to get the status of a database backup"""
    try:
        logger.debug("[BACKUP] Backup status check requested")
        client_id = request.args.get('client_id', '')
        logger.debug(f"[BACKUP] Status check for client_id: {client_id}")

        # Log to backup logger
        backup_logger.debug(f"Backup status check requested for client: {client_id}")
        backup_logger.debug(f"Request time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}")

        if not client_id:
            logger.warning("[BACKUP] Status check failed: No client ID provided")
            return jsonify({
                'success': False,
                'message': "Client ID is required"
            }), 400

        with clients_lock:
            if client_id not in clients:
                logger.warning(f"[BACKUP] Status check failed: Client {client_id} not found")
                return jsonify({
                    'success': False,
                    'message': f"Client {client_id} not found or not connected"
                }), 404

            client = clients[client_id]
            logger.debug(f"[BACKUP] Found client for status check: {client.display_name} (ID: {client_id})")
            logger.debug(f"[BACKUP] Client connection status: connected={client.connected}, last_ping={time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(client.last_ping))}")

            # Check if client has backup status attributes
            if not hasattr(client, 'db_backup_status'):
                logger.debug(f"[BACKUP] Client {client.display_name} has no backup status attribute, returning 'not_started'")
                return jsonify({
                    'success': True,
                    'status': 'not_started'
                })

            # Return status based on client's backup status
            status_data = {
                'success': True,
                'status': client.db_backup_status
            }

            logger.debug(f"[BACKUP] Client {client.display_name} backup status: {client.db_backup_status}")

            # Add additional info based on status
            if client.db_backup_status == 'in_progress':
                if hasattr(client, 'db_file_size') and client.db_file_size > 0:
                    # Check for both db_received_size and db_file_received attributes
                    received = 0
                    if hasattr(client, 'db_received_size'):
                        received = client.db_received_size
                    elif hasattr(client, 'db_file_received'):
                        received = client.db_file_received

                    progress = min(100, int(received * 100 / client.db_file_size))
                    status_data['progress'] = progress
                    status_data['received_size'] = received
                    status_data['file_size'] = client.db_file_size

                    # Add more detailed information for debugging
                    if hasattr(client, 'db_chunks_received'):
                        status_data['chunks_received'] = client.db_chunks_received
                    if hasattr(client, 'db_total_chunks'):
                        status_data['total_chunks'] = client.db_total_chunks

                    logger.debug(f"[BACKUP] Progress for {client.display_name}: {progress}% ({format_file_size(received)}/{format_file_size(client.db_file_size)})")
                    logger.debug(f"[BACKUP] Detailed progress: {received} bytes of {client.db_file_size} bytes received")

                    # Print to console for immediate visibility
                    print(f"[BACKUP] Progress for {client.display_name}: {progress}% ({format_file_size(received)}/{format_file_size(client.db_file_size)})")

            elif client.db_backup_status == 'complete':
                filename = os.path.basename(getattr(client, 'db_file_path', ''))
                file_size = getattr(client, 'db_file_size', 0)
                elapsed_time = getattr(client, 'db_backup_elapsed_time', 0)

                status_data['filename'] = filename
                status_data['file_size'] = file_size
                status_data['size_formatted'] = format_file_size(file_size)
                status_data['elapsed_time'] = elapsed_time
                status_data['download_url'] = f"/api/backup/download/{client_id}/{filename}"

                logger.debug(f"[BACKUP] Completed backup for {client.display_name}: {filename}, size: {format_file_size(file_size)}, time: {elapsed_time:.1f}s")

            elif client.db_backup_status == 'failed':
                error = getattr(client, 'db_backup_error', 'Unknown error')
                status_data['error'] = error
                logger.debug(f"[BACKUP] Failed backup for {client.display_name}: {error}")

            return jsonify(status_data)

    except Exception as e:
        logger.error(f"[BACKUP] Error getting backup status: {e}")
        logger.error(f"[BACKUP] Exception details: {traceback.format_exc()}")

        # Try to get client name for better error reporting
        client_name = client_id
        try:
            if client_id and client_id in clients:
                client_name = clients[client_id].display_name
        except:
            pass

        logger.warning(f"[BACKUP] Status check failed for client {client_name}: {str(e)}")

        return jsonify({
            'success': False,
            'message': f"Error getting backup status: {str(e)}"
        }), 500

# This route is defined elsewhere in the file

# Import direct database access module
try:
    from direct_db_access import *
    logger.info("Direct database access module loaded successfully")
except Exception as e:
    logger.error(f"Error loading direct database access module: {e}")
    logger.error(traceback.format_exc())

# Robust transfer is now handled by a separate process
# See robust_transfer_standalone.py

# Add backup monitor function to check for stalled or stuck transfers
def monitor_backups():
    """Monitor running backups for stalled or stuck transfers"""
    while True:
        try:
            current_time = time.time()
            with clients_lock:
                for client_id, client in list(clients.items()):
                    # Check if client has an active backup
                    if hasattr(client, 'db_backup_status') and client.db_backup_status == 'in_progress':
                        # Check if we have last_chunk_time
                        if hasattr(client, 'last_chunk_time'):
                            idle_time = current_time - client.last_chunk_time

                            # If no chunks received in 2 minutes, mark as stalled
                            if idle_time > 120 and not hasattr(client, 'db_backup_stalled'):
                                logger.warning(f"[BACKUP-MONITOR] Backup for {client.display_name} appears stalled (no chunks for {int(idle_time)} seconds)")
                                client.db_backup_stalled = True
                                client.db_backup_warning = f"Backup stalled for {int(idle_time)} seconds"

                                # Try to ping client
                                try:
                                    ping_message = NetworkMessage(
                                        NetworkMessage.TYPE_PING,
                                        {'timestamp': current_time},
                                        client_id
                                    )
                                    send_message(client.socket, ping_message)
                                    logger.info(f"[BACKUP-MONITOR] Sent ping to client {client.display_name}")
                                except Exception as ping_error:
                                    logger.warning(f"[BACKUP-MONITOR] Could not ping client: {ping_error}")

                            # If no chunks received in 5 minutes, mark as timeout
                            if idle_time > 300:
                                logger.error(f"[BACKUP-MONITOR] Backup for {client.display_name} timed out (no chunks for {int(idle_time)} seconds)")
                                client.db_backup_status = 'failed'
                                client.db_backup_error = f"Backup timed out after {int(idle_time)} seconds of inactivity"

                                # Attempt to finalize the partial backup if we have enough data
                                if hasattr(client, 'db_file_received') and hasattr(client, 'db_file_size') and client.db_file_received > 0:
                                    recovery_percentage = int(client.db_file_received * 100 / client.db_file_size)
                                    if recovery_percentage > 80:  # If we have at least 80% of the file
                                        logger.info(f"[BACKUP-MONITOR] Attempting to recover partial backup ({recovery_percentage}% complete)")

                                        # Update file size to match received bytes
                                        actual_size = os.path.getsize(client.db_file_path)
                                        logger.info(f"[BACKUP-MONITOR] Actual file size: {actual_size}, reported received: {client.db_file_received}")

                                        # Force file to be properly flushed and closed
                                        try:
                                            with open(client.db_file_path, 'a+b') as f:
                                                f.flush()
                                                os.fsync(f.fileno())
                                            logger.info(f"[BACKUP-MONITOR] Flushed partial backup file")
                                        except Exception as flush_error:
                                            logger.error(f"[BACKUP-MONITOR] Error flushing partial file: {flush_error}")

                                        # Create a partial recovery record
                                        try:
                                            complete_data = {
                                                'filename': os.path.basename(client.db_file_path),
                                                'size': client.db_file_received,  # Use received size, not original size
                                                'chunks_sent': client.db_chunks_received,
                                                'bytes_sent': client.db_file_received,
                                                'is_partial': True,
                                                'md5_checksum': None  # Cannot verify MD5 for partial file
                                            }
                                            process_db_file_complete(client_id, complete_data)
                                            logger.info(f"[BACKUP-MONITOR] Processed partial backup as complete")
                                        except Exception as recovery_error:
                                            logger.error(f"[BACKUP-MONITOR] Error recovering partial backup: {recovery_error}")
        except Exception as e:
            logger.error(f"[BACKUP-MONITOR] Error monitoring backups: {e}")
            logger.error(traceback.format_exc())

        # Sleep for 30 seconds before checking again
        time.sleep(30)

# Start backup monitor thread when server starts
def start_backup_monitor():
    """Start the backup monitor thread"""
    monitor_thread = threading.Thread(target=monitor_backups)
    monitor_thread.daemon = True
    monitor_thread.start()
    logger.info("Started backup monitor thread")

# Main entry point
if __name__ == '__main__':
    try:
        # Start server threads
        start_server()
        # Start backup monitor
        start_backup_monitor()
        # Start Flask app
        app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        logger.error(traceback.format_exc())
    finally:
        stop_server()

# This route is defined elsewhere in the file

# Update process_db_file_complete to handle partial backups
def process_db_file_complete(client_id, complete_data):
    """Process database file transfer completion"""
    logger.info(f"[BACKUP] ===== PROCESSING DATABASE FILE COMPLETION =====")
    logger.info(f"[BACKUP] Processing database file completion from client_id: {client_id}")

    with clients_lock:
        if client_id not in clients:
            logger.error(f"[BACKUP] Client {client_id} not found when processing completion")
            return

        client = clients[client_id]
        logger.info(f"[BACKUP] Processing completion for client: {client.display_name}")

        try:
            # Extract completion information
            filename = complete_data.get('filename', '')
            file_size = complete_data.get('size', 0)
            chunks_sent = complete_data.get('chunks_sent', 0)
            bytes_received = complete_data.get('bytes_sent', 0)
            client_md5 = complete_data.get('md5_checksum', '')
            is_partial = complete_data.get('is_partial', False)

            # Log completion info
            logger.info(f"[BACKUP] Completion info: {chunks_sent} chunks, {bytes_received} bytes received, partial: {is_partial}")
            logger.debug(f"[BACKUP] Completion data details: {json.dumps(complete_data, indent=2)}")

            if not hasattr(client, 'db_file_path') or not client.db_file_path:
                logger.error(f"No database file path set for {client.display_name}")

                # Create a directory for the client if it doesn't exist
                client_dir = os.path.join(current_dir, 'static', 'backups', client_id)
                os.makedirs(client_dir, exist_ok=True)

                # Generate a file path for the database
                current_timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                db_file_path = os.path.join(client_dir, complete_data.get('filename', f"database_{current_timestamp}.fdb"))

                logger.info(f"Setting database file path for {client.display_name} to {db_file_path}")
                client.db_file_path = db_file_path
                client.db_backup_status = 'failed'
                client.db_backup_error = "No database file path was set during transfer"
                return

            # Verify file size
            try:
                actual_size = os.path.getsize(client.db_file_path)
                logger.info(f"[BACKUP] Verifying file size: Expected {file_size} bytes ({format_file_size(file_size)}), Actual {actual_size} bytes ({format_file_size(actual_size)})")

                # Verify file size only for complete backups
                if not is_partial and actual_size != file_size:
                    logger.warning(f"File size mismatch for {filename}. Expected: {file_size}, Actual: {actual_size}")

                    # Don't fail immediately, continue with verification
                    client.db_backup_warning = f"File size mismatch: Expected {file_size}, got {actual_size} bytes"

                # For partial backups, adjust expected size
                if is_partial:
                    logger.warning(f"[BACKUP] Processing partial backup. Received: {bytes_received}/{file_size} bytes ({int(bytes_received*100/file_size)}%)")
                    client.db_backup_warning = f"Partial backup: {int(bytes_received*100/file_size)}% complete"
                    # Skip MD5 verification for partial backups
                    client_md5 = None

                # Calculate MD5 checksum if client provided one
                server_md5 = None
                is_md5_match = False

                if client_md5:
                    logger.info(f"[BACKUP] Calculating MD5 checksum for verification...")
                    try:
                        md5_hash = hashlib.md5()
                        with open(client.db_file_path, 'rb') as f:
                            while True:
                                data = f.read(8192)
                                if not data:
                                    break
                                md5_hash.update(data)
                        server_md5 = md5_hash.hexdigest()

                        logger.info(f"[BACKUP] MD5 Checksum - Client: {client_md5}, Server: {server_md5}")
                        is_md5_match = (server_md5 == client_md5)

                        if not is_md5_match:
                            logger.warning(f"[BACKUP] MD5 checksum mismatch for {filename}")
                            client.db_backup_warning = f"MD5 checksum mismatch: Expected {client_md5}, got {server_md5}"
                        else:
                            logger.info(f"[BACKUP] MD5 checksum verification successful")
                    except Exception as md5_error:
                        logger.error(f"[BACKUP] Error calculating MD5 checksum: {md5_error}")
                        client.db_backup_warning = f"MD5 checksum calculation error: {str(md5_error)}"
            except Exception as size_error:
                logger.error(f"[BACKUP] Error verifying file size: {size_error}")
                client.db_backup_warning = f"File size verification error: {str(size_error)}"

            # Deteksi jika ini adalah database Firebird (.fdb)
            _, ext = os.path.splitext(client.db_file_path)
            is_firebird_db = ext.lower() == '.fdb'

            # Verify Firebird database (if applicable)
            is_valid_firebird = False
            validation_message = "Not a Firebird database"

            if is_firebird_db and not is_partial:
                logger.info(f"[BACKUP] Validating Firebird database: {client.db_file_path}")
                try:
                    is_valid_firebird, validation_message = validate_firebird_database(client.db_file_path)

                    if not is_valid_firebird:
                        logger.warning(f"[BACKUP] Firebird validation failed: {validation_message}")
                        client.db_backup_warning = f"Firebird validation: {validation_message}"

                        # Try to repair if invalid
                        logger.info(f"[BACKUP] Attempting to repair invalid Firebird database...")
                        repair_success, repair_message = try_repair_firebird_database(client.db_file_path)

                        if repair_success:
                            logger.info(f"[BACKUP] Database repair successful: {repair_message}")
                            is_valid_firebird = True
                            validation_message = f"Repaired: {repair_message}"
                            client.db_backup_warning = f"Database required repair: {repair_message}"
                        else:
                            logger.warning(f"[BACKUP] Database repair failed: {repair_message}")
                            client.db_backup_warning = f"Database repair failed: {repair_message}"
                    else:
                        logger.info(f"[BACKUP] Firebird validation successful: {validation_message}")
                except Exception as validation_error:
                    logger.error(f"[BACKUP] Error validating Firebird database: {validation_error}")
                    validation_message = f"Validation error: {str(validation_error)}"
                    client.db_backup_warning = f"Firebird validation error: {str(validation_error)}"
            elif is_firebird_db and is_partial:
                logger.warning(f"[BACKUP] Skipping Firebird validation for partial backup")
                validation_message = "Skipped validation for partial backup"
                client.db_backup_warning = "Partial backup: validation skipped"
                is_valid_firebird = False  # Consider partial Firebird backups as invalid

            # Update client status
            client.db_backup_status = 'completed'
            if is_partial:
                client.db_backup_status = 'completed_partial'
            client.db_file_verified = True
            client.db_backup_complete_time = time.time()
            client.db_file_verified_size = actual_size

            # Add Firebird-specific info if applicable
            if is_firebird_db:
                client.db_is_firebird = True
                client.db_firebird_valid = is_valid_firebird
                client.db_validation_message = validation_message

                if not is_valid_firebird and not hasattr(client, 'db_backup_warning'):
                    client.db_backup_warning = f"Firebird database validation failed: {validation_message}"

            # Create backup record for the database
            try:
                backup_dir = os.path.dirname(client.db_file_path)
                backup_record = {
                    'client_id': client_id,
                    'client_name': client.display_name,
                    'filename': os.path.basename(client.db_file_path),
                    'path': client.db_file_path,
                    'size': actual_size,
                    'date': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'download_url': f"/api/backup/download/{client_id}/{os.path.basename(client.db_file_path)}",
                    'is_firebird': is_firebird_db,
                    'is_valid': (not is_firebird_db or is_valid_firebird) and not is_partial,
                    'is_partial': is_partial,
                    'partial_percentage': int(bytes_received*100/file_size) if is_partial else 100,
                    'validation_message': validation_message if is_firebird_db else "",
                    'md5_checksum': server_md5 if server_md5 else "",
                    'md5_match': is_md5_match if client_md5 and server_md5 else None,
                    'warning': getattr(client, 'db_backup_warning', None)
                }

                # Add to client's backup list if it doesn't exist
                if not hasattr(client, 'backups'):
                    client.backups = []

                # Avoid duplicates by checking filename
                if not any(b.get('filename') == backup_record['filename'] for b in client.backups):
                    client.backups.append(backup_record)

                # Sort backups by date, newest first
                client.backups.sort(key=lambda x: x.get('date', ''), reverse=True)

                logger.info(f"[BACKUP] Added backup record for {client.display_name}: {backup_record['filename']}")

                # Notify client of successful backup (optional)
                try:
                    completion_message = NetworkMessage(
                        NetworkMessage.TYPE_DB_COMPLETE,
                        {
                            'status': 'success' if not is_partial else 'partial',
                            'message': 'Backup successfully received and processed' if not is_partial else 'Partial backup processed',
                            'filename': backup_record['filename'],
                            'size': backup_record['size'],
                            'is_valid': backup_record['is_valid'],
                            'is_partial': is_partial,
                            'md5_match': backup_record.get('md5_match', None)
                        },
                        client_id
                    )
                    send_message(client.socket, completion_message)
                    logger.info(f"[BACKUP] Sent completion acknowledgment to client")
                except Exception as notify_error:
                    logger.warning(f"[BACKUP] Could not notify client of completion: {notify_error}")
                    # Continue anyway, this is not critical

            except Exception as record_error:
                logger.error(f"[BACKUP] Error creating backup record: {record_error}")
                logger.error(traceback.format_exc())

            logger.info(f"[BACKUP] Database backup completed successfully for {client.display_name}: {client.db_file_path}")

        except Exception as e:
            logger.error(f"[BACKUP] Error processing database file completion: {e}")
            logger.error(traceback.format_exc())
            client.db_backup_status = 'failed'
            client.db_backup_error = str(e)

# Add new API endpoint to get MEGA upload status
@app.route('/api/backup/mega-status', methods=['GET'])
def api_mega_upload_status():
    """API endpoint to get the status of a MEGA upload request"""
    client_id = request.args.get('client_id')
    request_id = request.args.get('request_id')

    if not client_id:
        return jsonify({
            'success': False,
            'message': "Client ID is required"
        }), 400

    if not request_id:
        return jsonify({
            'success': False,
            'message': "Request ID is required"
        }), 400

    # Check if client exists
    with clients_lock:
        if client_id not in clients:
            return jsonify({
                'success': False,
                'message': f"Client {client_id} not found"
            }), 404

        client = clients[client_id]

        # Get upload status if available
        if hasattr(client, 'mega_uploads') and request_id in client.mega_uploads:
            upload_info = client.mega_uploads[request_id]
            return jsonify({
                'success': True,
                'status': upload_info.get('status', 'unknown'),
                'progress': upload_info.get('progress', 0),
                'timestamp': upload_info.get('timestamp'),
                'result': upload_info.get('result')
            })
        else:
            return jsonify({
                'success': False,
                'message': f"No upload status found for request ID: {request_id}"
            }), 404

# Add new API endpoint to trigger MEGA upload from client directly
@app.route('/api/trigger-mega-upload', methods=['POST'])
def api_trigger_mega_upload():
    """API endpoint to trigger client to upload its database to MEGA"""
    client_id = request.form.get('client_id', '')

    logger.info(f"[MEGA] ===== MEGA DIRECT UPLOAD REQUEST RECEIVED VIA API =====")
    logger.info(f"[MEGA] Direct upload request received via API, client_id: {client_id}")
    logger.info(f"[MEGA] Request source: {request.remote_addr}")
    logger.info(f"[MEGA] Request method: {request.method}")
    logger.debug(f"[MEGA] Request form data: {request.form}")

    if not client_id:
        logger.error("[MEGA] No client ID provided in direct upload request")
        return jsonify({
            'success': False,
            'message': "Client ID is required"
        }), 400

    # Check if client exists and is connected
    with clients_lock:
        if client_id not in clients:
            logger.error(f"[MEGA] Client {client_id} not found")
            return jsonify({
                'success': False,
                'message': f"Client {client_id} not found"
            }), 404

        client = clients[client_id]

        if not client.connected or not client.socket:
            logger.error(f"[MEGA] Client {client.display_name} is not connected")
            return jsonify({
                'success': False,
                'message': f"Client {client.display_name} is not connected"
            }), 400

    # Create a unique request ID
    request_id = str(uuid.uuid4())

    # Create MEGA upload request message
    try:
        # In this case, we don't need to specify a file_path
        # The client will use its currently connected database
        upload_request = NetworkMessage(
            NetworkMessage.TYPE_MEGA_UPLOAD_REQUEST,
            {
                'request_id': request_id,
                'timestamp': time.time(),
                'client_id': client_id,
                'direct_upload': True  # Indicate this is a direct upload
            },
            client_id
        )

        # Send request to client
        logger.info(f"[MEGA] Sending direct MEGA upload request to client {client.display_name}")
        if send_message(client.socket, upload_request):
            logger.info(f"[MEGA] Direct MEGA upload request sent successfully to client {client.display_name}")
            return jsonify({
                'success': True,
                'message': f"Direct MEGA upload request sent to client {client.display_name}",
                'request_id': request_id
            })
        else:
            logger.error(f"[MEGA] Failed to send direct MEGA upload request to client {client.display_name}")
            return jsonify({
                'success': False,
                'message': f"Failed to send direct MEGA upload request to client {client.display_name}"
            }), 500
    except Exception as e:
        logger.error(f"[MEGA] Error sending direct MEGA upload request: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f"Error sending direct MEGA upload request: {str(e)}"
        }), 500

# Add new API endpoint to trigger GDrive upload from client directly
@app.route('/api/trigger-gdrive-upload', methods=['POST'])
def api_trigger_gdrive_upload():
    """API endpoint to trigger client to upload its database to Google Drive"""
    client_id = request.form.get('client_id', '')

    logger.info(f"[GDRIVE] ===== GDRIVE DIRECT UPLOAD REQUEST RECEIVED VIA API =====")
    logger.info(f"[GDRIVE] Direct upload request received via API, client_id: {client_id}")
    logger.info(f"[GDRIVE] Request source: {request.remote_addr}")
    logger.info(f"[GDRIVE] Request method: {request.method}")
    logger.debug(f"[GDRIVE] Request form data: {request.form}")

    if not client_id:
        logger.error("[GDRIVE] No client ID provided in direct upload request")
        return jsonify({
            'success': False,
            'message': "Client ID is required"
        }), 400

    # Check if client exists and is connected
    with clients_lock:
        if client_id not in clients:
            logger.error(f"[GDRIVE] Client {client_id} not found")
            return jsonify({
                'success': False,
                'message': f"Client {client_id} not found"
            }), 404

        client = clients[client_id]

        if not client.connected or not client.socket:
            logger.error(f"[GDRIVE] Client {client.display_name} is not connected")
            return jsonify({
                'success': False,
                'message': f"Client {client.display_name} is not connected"
            }), 400

    # Create a unique request ID
    request_id = str(uuid.uuid4())

    # Create GDrive upload request message
    try:
        # Define new message type for GDrive uploads if not already defined
        if not hasattr(NetworkMessage, 'TYPE_GDRIVE_UPLOAD_REQUEST'):
            NetworkMessage.TYPE_GDRIVE_UPLOAD_REQUEST = 'gdrive_upload_request'

        # In this case, we don't need to specify a file_path
        # The client will use its currently connected database
        upload_request = NetworkMessage(
            NetworkMessage.TYPE_GDRIVE_UPLOAD_REQUEST,
            {
                'request_id': request_id,
                'timestamp': time.time(),
                'client_id': client_id,
                'direct_upload': True  # Indicate this is a direct upload
            },
            client_id
        )

        # Send request to client
        logger.info(f"[GDRIVE] Sending direct GDrive upload request to client {client.display_name}")
        if send_message(client.socket, upload_request):
            logger.info(f"[GDRIVE] Direct GDrive upload request sent successfully to client {client.display_name}")

            # Initialize gdrive_uploads attribute if it doesn't exist
            if not hasattr(client, 'gdrive_uploads'):
                client.gdrive_uploads = {}

            # Store initial status
            client.gdrive_uploads[request_id] = {
                'timestamp': time.time(),
                'status': 'pending',
                'progress': 0
            }

            return jsonify({
                'success': True,
                'message': f"Direct GDrive upload request sent to client {client.display_name}",
                'request_id': request_id
            })
        else:
            logger.error(f"[GDRIVE] Failed to send direct GDrive upload request to client {client.display_name}")
            return jsonify({
                'success': False,
                'message': f"Failed to send direct GDrive upload request to client {client.display_name}"
            }), 500
    except Exception as e:
        logger.error(f"[GDRIVE] Error sending direct GDrive upload request: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f"Error sending direct GDrive upload request: {str(e)}"
        }), 500

# Add new API endpoint to get GDrive upload status
@app.route('/api/backup/gdrive-status', methods=['GET'])
def api_gdrive_upload_status():
    """API endpoint to get the status of a GDrive upload request"""
    client_id = request.args.get('client_id')
    request_id = request.args.get('request_id')

    if not client_id:
        return jsonify({
            'success': False,
            'message': "Client ID is required"
        }), 400

    if not request_id:
        return jsonify({
            'success': False,
            'message': "Request ID is required"
        }), 400

    # Check if client exists
    with clients_lock:
        if client_id not in clients:
            return jsonify({
                'success': False,
                'message': f"Client {client_id} not found"
            }), 404

        client = clients[client_id]

        # Get upload status if available
        if hasattr(client, 'gdrive_uploads') and request_id in client.gdrive_uploads:
            upload_info = client.gdrive_uploads[request_id]
            return jsonify({
                'success': True,
                'status': upload_info.get('status', 'unknown'),
                'progress': upload_info.get('progress', 0),
                'timestamp': upload_info.get('timestamp'),
                'result': upload_info.get('result')
            })
        else:
            return jsonify({
                'success': False,
                'message': f"No upload status found for request ID: {request_id}"
            }), 404