#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Comprehensive Test Script for IFESS Header Truncation Fix

This script tests the following fixes:
1. Header truncation issue in web display
2. Enhanced database utilities with JSON result formatting
3. Query coordinator system for conflict-free query distribution
4. Configurable ISQL paths
5. Complete data integrity from database to web display

Usage:
    python test_header_fix.py
"""

import os
import sys
import json
import time
import requests
import subprocess
import tempfile
from datetime import datetime

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'server_web'))
sys.path.append(os.path.join(current_dir, 'staging_build'))

def test_enhanced_db_utils():
    """Test the enhanced database utilities"""
    print("=" * 60)
    print("Testing Enhanced Database Utilities")
    print("=" * 60)
    
    try:
        # Import enhanced db_utils
        sys.path.append(os.path.join(current_dir, 'server_web', 'common'))
        from db_utils import FirebirdConnector
        
        print("✓ Enhanced db_utils imported successfully")
        
        # Test configurable ISQL path detection
        connector = FirebirdConnector(
            db_path="test.fdb",
            username="SYSDBA", 
            password="masterkey"
        )
        
        print(f"✓ ISQL path detected: {connector.isql_path}")
        
        # Test enhanced parsing methods
        test_output = """
ID          SCANNERUSEREMPID    OCFIELDID       DEFOCID         JOBCODEID
=========== =================== =============== =============== ===============
1           EMP001              FIELD001        DEF001          JOB001
2           EMP002              FIELD002        DEF002          JOB002
"""
        
        result = connector._parse_enhanced_output(test_output, as_dict=True)
        
        if result and len(result) > 0:
            first_result = result[0]
            headers = first_result.get('headers', [])
            rows = first_result.get('rows', [])
            
            print(f"✓ Enhanced parsing successful:")
            print(f"  - Headers: {headers}")
            print(f"  - Rows: {len(rows)}")
            print(f"  - Data types: {first_result.get('data_types', {})}")
            print(f"  - Parsing method: {first_result.get('parsing_info', {}).get('method', 'unknown')}")
            
            # Verify no header truncation
            expected_headers = ['ID', 'SCANNERUSEREMPID', 'OCFIELDID', 'DEFOCID', 'JOBCODEID']
            if headers == expected_headers:
                print("✓ Headers preserved correctly - no truncation")
            else:
                print(f"✗ Header mismatch. Expected: {expected_headers}, Got: {headers}")
                return False
        else:
            print("✗ Enhanced parsing failed")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ Enhanced db_utils test failed: {e}")
        return False

def test_config_manager():
    """Test the configuration manager"""
    print("\n" + "=" * 60)
    print("Testing Configuration Manager")
    print("=" * 60)
    
    try:
        # Import config manager
        sys.path.append(os.path.join(current_dir, 'staging_build'))
        from config_manager import ConfigManager
        
        print("✓ ConfigManager imported successfully")
        
        # Create temporary config file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            test_config = {
                "server_address": "localhost",
                "server_port": 5555,
                "database": {
                    "path": "test.fdb",
                    "username": "SYSDBA",
                    "password": "masterkey"
                }
            }
            json.dump(test_config, f)
            temp_config_path = f.name
        
        try:
            # Test config manager initialization
            config_manager = ConfigManager(temp_config_path, current_dir)
            print("✓ ConfigManager initialized successfully")
            
            # Test configuration validation
            validation = config_manager.validate_config()
            print(f"✓ Configuration validation: {validation}")
            
            # Test ISQL path detection
            config = config_manager.get_config()
            if 'database' in config and 'isql_path' in config['database']:
                print(f"✓ ISQL path configured: {config['database']['isql_path']}")
            else:
                print("! ISQL path not configured (will use auto-detection)")
            
            return True
            
        finally:
            # Clean up temp file
            os.unlink(temp_config_path)
            
    except Exception as e:
        print(f"✗ ConfigManager test failed: {e}")
        return False

def test_query_coordinator():
    """Test the query coordinator system"""
    print("\n" + "=" * 60)
    print("Testing Query Coordinator System")
    print("=" * 60)
    
    try:
        # Import query coordinator
        sys.path.append(os.path.join(current_dir, 'server_web'))
        from query_coordinator import QueryCoordinator
        
        print("✓ QueryCoordinator imported successfully")
        
        # Initialize coordinator
        coordinator = QueryCoordinator()
        print("✓ QueryCoordinator initialized")
        
        # Test client registration
        coordinator.register_client("test_client_1", "Test Client 1")
        coordinator.register_client("test_client_2", "Test Client 2")
        print("✓ Test clients registered")
        
        # Test query submission
        query_id = coordinator.submit_query(
            "SELECT * FROM TEST_TABLE",
            target_clients=["test_client_1", "test_client_2"],
            timeout=60
        )
        
        if query_id:
            print(f"✓ Query submitted successfully: {query_id}")
            
            # Test query status
            status = coordinator.get_query_status(query_id)
            if status:
                print(f"✓ Query status retrieved: {status['status']}")
            else:
                print("✗ Failed to get query status")
                return False
        else:
            print("✗ Failed to submit query")
            return False
        
        # Test client status
        client_status = coordinator.get_client_status()
        print(f"✓ Client status retrieved: {len(client_status)} clients")
        
        # Stop coordinator
        coordinator.stop_coordinator()
        print("✓ QueryCoordinator stopped")
        
        return True
        
    except Exception as e:
        print(f"✗ QueryCoordinator test failed: {e}")
        return False

def test_css_header_fix():
    """Test that CSS header truncation has been fixed"""
    print("\n" + "=" * 60)
    print("Testing CSS Header Truncation Fix")
    print("=" * 60)
    
    try:
        # Read the CSS files to verify fixes
        css_files = [
            os.path.join(current_dir, 'server_web', 'static', 'css', 'style.css'),
            os.path.join(current_dir, 'server_web', 'templates', 'query.html')
        ]
        
        fixes_found = 0
        
        for css_file in css_files:
            if os.path.exists(css_file):
                with open(css_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for fixed table layout
                if 'table-layout: auto' in content:
                    print(f"✓ Found table-layout: auto in {os.path.basename(css_file)}")
                    fixes_found += 1
                
                # Check for removed header truncation
                if 'min-width: max-content' in content:
                    print(f"✓ Found min-width: max-content for headers in {os.path.basename(css_file)}")
                    fixes_found += 1
                
                # Check that overflow: hidden is not applied to headers
                if '.query-result-table th' in content and 'overflow: hidden' not in content.split('.query-result-table th')[1].split('}')[0]:
                    print(f"✓ Header overflow constraints removed in {os.path.basename(css_file)}")
                    fixes_found += 1
        
        if fixes_found >= 2:
            print("✓ CSS header truncation fixes verified")
            return True
        else:
            print(f"✗ Only {fixes_found} CSS fixes found, expected at least 2")
            return False
            
    except Exception as e:
        print(f"✗ CSS header fix test failed: {e}")
        return False

def test_javascript_logic():
    """Test that JavaScript table rendering logic is correct"""
    print("\n" + "=" * 60)
    print("Testing JavaScript Table Rendering Logic")
    print("=" * 60)
    
    try:
        query_html_path = os.path.join(current_dir, 'server_web', 'templates', 'query.html')
        
        if not os.path.exists(query_html_path):
            print("✗ query.html template not found")
            return False
        
        with open(query_html_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for correct row format handling
        if 'else if (typeof row === \'object\')' in content:
            print("✓ Correct row format handling found")
        else:
            print("✗ Row format handling logic not found")
            return False
        
        # Check for header mapping
        if 'result.headers.map(header => `<th>${header}</th>`).join(\'\')' in content:
            print("✓ Header mapping logic found")
        else:
            print("✗ Header mapping logic not found")
            return False
        
        # Check for complete header preservation
        if 'const headers = result.headers' in content:
            print("✓ Header preservation logic found")
        else:
            print("✗ Header preservation logic not found")
            return False
        
        print("✓ JavaScript table rendering logic verified")
        return True
        
    except Exception as e:
        print(f"✗ JavaScript logic test failed: {e}")
        return False

def test_server_integration():
    """Test that server properly integrates QueryCoordinator"""
    print("\n" + "=" * 60)
    print("Testing Server Integration")
    print("=" * 60)
    
    try:
        server_web_path = os.path.join(current_dir, 'server_web', 'server_web.py')
        
        if not os.path.exists(server_web_path):
            print("✗ server_web.py not found")
            return False
        
        with open(server_web_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for QueryCoordinator import
        if 'from query_coordinator import QueryCoordinator' in content:
            print("✓ QueryCoordinator import found")
        else:
            print("✗ QueryCoordinator import not found")
            return False
        
        # Check for coordinator initialization
        if 'query_coordinator = QueryCoordinator()' in content:
            print("✓ QueryCoordinator initialization found")
        else:
            print("✗ QueryCoordinator initialization not found")
            return False
        
        # Check for coordinator cleanup
        if 'query_coordinator.stop_coordinator()' in content:
            print("✓ QueryCoordinator cleanup found")
        else:
            print("✗ QueryCoordinator cleanup not found")
            return False
        
        print("✓ Server integration verified")
        return True
        
    except Exception as e:
        print(f"✗ Server integration test failed: {e}")
        return False

def test_client_enhancements():
    """Test that client uses enhanced components"""
    print("\n" + "=" * 60)
    print("Testing Client Enhancements")
    print("=" * 60)
    
    try:
        client_path = os.path.join(current_dir, 'staging_build', 'ifess_client_hidden.py')
        
        if not os.path.exists(client_path):
            print("✗ ifess_client_hidden.py not found")
            return False
        
        with open(client_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for enhanced imports
        if 'from config_manager import ConfigManager' in content:
            print("✓ ConfigManager import found in client")
        else:
            print("✗ ConfigManager import not found in client")
            return False
        
        # Check for enhanced database connector usage
        if '_initialize_enhanced_database_connector' in content:
            print("✓ Enhanced database connector initialization found")
        else:
            print("✗ Enhanced database connector initialization not found")
            return False
        
        # Check for enhanced result processing
        if 'Enhanced result processing with detailed logging' in content:
            print("✓ Enhanced result processing found")
        else:
            print("✗ Enhanced result processing not found")
            return False
        
        print("✓ Client enhancements verified")
        return True
        
    except Exception as e:
        print(f"✗ Client enhancements test failed: {e}")
        return False

def run_comprehensive_test():
    """Run all tests and provide summary"""
    print("IFESS Header Truncation Fix - Comprehensive Test Suite")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("Enhanced Database Utilities", test_enhanced_db_utils),
        ("Configuration Manager", test_config_manager),
        ("Query Coordinator System", test_query_coordinator),
        ("CSS Header Truncation Fix", test_css_header_fix),
        ("JavaScript Table Logic", test_javascript_logic),
        ("Server Integration", test_server_integration),
        ("Client Enhancements", test_client_enhancements)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        symbol = "✓" if result else "✗"
        print(f"{symbol} {test_name}: {status}")
        
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal Tests: {len(results)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success Rate: {(passed/len(results)*100):.1f}%")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED! The IFESS header truncation issue has been successfully fixed.")
        print("\nKey improvements verified:")
        print("• Header truncation eliminated in web display")
        print("• Enhanced database utilities with JSON result formatting")
        print("• Query coordinator for conflict-free distribution")
        print("• Configurable ISQL paths")
        print("• Complete data integrity preservation")
        print("• Robust error handling and isolation")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please review the issues above.")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return failed == 0

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1) 