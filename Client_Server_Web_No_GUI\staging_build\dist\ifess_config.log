2025-06-06 21:50:11,282 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-06-06 21:50:11,432 - IFESS-Config - INFO - Configuration loaded from OAuth config: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config_oauth_tokens.json
2025-06-06 21:50:11,432 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-06-06 21:50:11,432 - IFESS-Config - INFO - Loaded Google Drive token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-06-06 21:50:11,432 - IFESS-Config - INFO - Loaded scheduled upload settings: enabled=False, time=2:00, service=gdrive
2025-06-06 21:50:11,434 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config_oauth_tokens.json
2025-06-06 21:50:11,434 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-06-06 21:50:11,817 - IFESS-Config - INFO - token_downloader module imported successfully
2025-06-06 21:50:11,818 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-06-06 21:50:11,818 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-06 21:50:11,818 - token_downloader - INFO - Token expires in 59.4 minutes (threshold: 60.0 minutes)
2025-06-06 21:50:11,818 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-06-06 21:50:11,818 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-06 21:50:11,853 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\ptrj-backup-services-account.json
2025-06-06 21:50:11,855 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-06 21:50:11,857 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-06-06 21:50:11,857 - token_downloader - INFO - Using service account to list files
2025-06-06 21:50:12,981 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-06 21:50:12,981 - token_downloader - INFO - Found file: token_20250606_212734.json (Modified: 2025-06-06T14:27:34.678Z)
2025-06-06 21:50:12,981 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-06-06 21:50:12,982 - token_downloader - ERROR - Traceback (most recent call last):
  File "token_downloader.py", line 286, in process_token_files
    max_age = datetime.timedelta(days=MAX_TOKEN_AGE_DAYS)
              ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-06-06 21:50:12,982 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-06-06 21:50:12,982 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-06 21:50:12,983 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-06-06 21:50:32,515 - IFESS-Config - INFO - Testing database connection with:
2025-06-06 21:50:32,515 - IFESS-Config - INFO -   DB Path: D:/Gawean Rebinmas/Monitoring Database/Database Ifess/PTRJ_AB1_08042025/PTRJ_AB1.FDB
2025-06-06 21:50:32,515 - IFESS-Config - INFO -   Username: sysdba
2025-06-06 21:50:32,515 - IFESS-Config - INFO -   ISQL Path: C:/Program Files (x86)/Firebird/Firebird_1_5/bin/isql.exe
2025-06-06 21:50:32,516 - IFESS-Config - INFO -   Use localhost: True
2025-06-06 21:50:32,608 - IFESS-Config - INFO - test_connection result: True
2025-06-06 21:50:36,296 - IFESS-Config - INFO - Using token_downloader to get token from Google Drive
2025-06-06 21:50:36,297 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-06 21:50:36,298 - token_downloader - INFO - Token expires in 59.0 minutes (threshold: 60.0 minutes)
2025-06-06 21:50:36,298 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-06-06 21:50:36,298 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-06 21:50:36,298 - token_downloader - INFO - Using service account to list files
2025-06-06 21:50:36,654 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-06 21:50:36,654 - token_downloader - INFO - Found file: token_20250606_212734.json (Modified: 2025-06-06T14:27:34.678Z)
2025-06-06 21:50:36,654 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-06-06 21:50:36,655 - token_downloader - ERROR - Traceback (most recent call last):
  File "token_downloader.py", line 286, in process_token_files
    max_age = datetime.timedelta(days=MAX_TOKEN_AGE_DAYS)
              ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-06-06 21:50:36,655 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-06-06 21:50:36,655 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-06 21:50:36,659 - IFESS-Config - INFO - Building Google Drive API service...
2025-06-06 21:50:36,660 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-06 21:50:36,662 - IFESS-Config - INFO - Google Drive API service built successfully
2025-06-06 21:50:37,232 - IFESS-Config - INFO - Logged in as: PTRJ IFES (<EMAIL>)
2025-06-06 21:50:37,232 - IFESS-Config - INFO - Storage: 71.28 GB used of 100.00 GB total
2025-06-06 21:50:37,232 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-06-06 21:50:50,949 - IFESS-Config - INFO - Configuration also saved to OAuth config: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config_oauth_tokens.json
2025-06-06 21:50:50,949 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-06-06 21:50:50,949 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-06-06 21:50:57,758 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-06-06 21:50:57,889 - IFESS-Config - INFO - Configuration loaded from OAuth config: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config_oauth_tokens.json
2025-06-06 21:50:57,890 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-06-06 21:50:57,890 - IFESS-Config - INFO - Loaded Google Drive credentials file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-06-06 21:50:57,890 - IFESS-Config - INFO - Loaded scheduled upload settings: enabled=True, time=21:52, service=gdrive
2025-06-06 21:50:57,890 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config_oauth_tokens.json
2025-06-06 21:50:57,890 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-06-06 21:50:58,280 - IFESS-Config - INFO - token_downloader module imported successfully
2025-06-06 21:50:58,280 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-06-06 21:50:58,280 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-06 21:50:58,280 - token_downloader - INFO - Token expires in 58.6 minutes (threshold: 60.0 minutes)
2025-06-06 21:50:58,280 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-06-06 21:50:58,280 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-06 21:50:58,312 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\ptrj-backup-services-account.json
2025-06-06 21:50:58,314 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-06 21:50:58,315 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-06-06 21:50:58,315 - token_downloader - INFO - Using service account to list files
2025-06-06 21:50:59,286 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-06 21:50:59,286 - token_downloader - INFO - Found file: token_20250606_212734.json (Modified: 2025-06-06T14:27:34.678Z)
2025-06-06 21:50:59,286 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-06-06 21:50:59,287 - token_downloader - ERROR - Traceback (most recent call last):
  File "token_downloader.py", line 286, in process_token_files
    max_age = datetime.timedelta(days=MAX_TOKEN_AGE_DAYS)
              ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-06-06 21:50:59,287 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-06-06 21:50:59,287 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-06 21:50:59,287 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-06-06 21:52:51,331 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-06-06 21:52:51,468 - IFESS-Config - INFO - Configuration loaded from OAuth config: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config_oauth_tokens.json
2025-06-06 21:52:51,468 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-06-06 21:52:51,468 - IFESS-Config - INFO - Loaded Google Drive credentials file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-06-06 21:52:51,468 - IFESS-Config - INFO - Loaded scheduled upload settings: enabled=True, time=21:52, service=gdrive
2025-06-06 21:52:51,468 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config_oauth_tokens.json
2025-06-06 21:52:51,468 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-06-06 21:52:51,852 - IFESS-Config - INFO - token_downloader module imported successfully
2025-06-06 21:52:51,852 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-06-06 21:52:51,852 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-06 21:52:51,852 - token_downloader - INFO - Token expires in 56.7 minutes (threshold: 60.0 minutes)
2025-06-06 21:52:51,852 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-06-06 21:52:51,852 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-06 21:52:51,883 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\ptrj-backup-services-account.json
2025-06-06 21:52:51,885 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-06 21:52:51,886 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-06-06 21:52:51,886 - token_downloader - INFO - Using service account to list files
2025-06-06 21:52:52,849 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-06 21:52:52,849 - token_downloader - INFO - Found file: token_20250606_212734.json (Modified: 2025-06-06T14:27:34.678Z)
2025-06-06 21:52:52,849 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-06-06 21:52:52,850 - token_downloader - ERROR - Traceback (most recent call last):
  File "token_downloader.py", line 286, in process_token_files
    max_age = datetime.timedelta(days=MAX_TOKEN_AGE_DAYS)
              ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-06-06 21:52:52,850 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-06-06 21:52:52,850 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-06 21:52:52,850 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
