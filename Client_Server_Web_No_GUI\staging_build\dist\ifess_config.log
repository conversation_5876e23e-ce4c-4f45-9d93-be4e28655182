2025-06-07 09:51:20,581 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-06-07 09:51:20,738 - IFESS-Config - INFO - Configuration loaded from OAuth config: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config_oauth_tokens.json
2025-06-07 09:51:20,738 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-06-07 09:51:20,738 - IFESS-Config - INFO - Loaded Google Drive credentials file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-07 09:51:20,739 - IFESS-Config - INFO - Loaded scheduled upload settings: enabled=True, time=9:43, service=gdrive
2025-06-07 09:51:20,739 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config_oauth_tokens.json
2025-06-07 09:51:20,739 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-06-07 09:51:21,296 - IFESS-Config - INFO - token_downloader module imported successfully
2025-06-07 09:51:21,296 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-06-07 09:51:21,296 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-07 09:51:21,297 - token_downloader - ERROR - Token is already expired
2025-06-07 09:51:21,297 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-06-07 09:51:21,297 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-06-07 09:51:21,297 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/10)...
2025-06-07 09:51:21,297 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-07 09:51:21,335 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\ptrj-backup-services-account.json
2025-06-07 09:51:21,340 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-07 09:51:21,343 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-06-07 09:51:21,343 - token_downloader - INFO - Using service account to list files
2025-06-07 09:51:26,103 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-07 09:51:26,103 - token_downloader - INFO - Found file: token_20250607_093140.json (Modified: 2025-06-07T02:31:41.527Z)
2025-06-07 09:51:26,103 - token_downloader - INFO - Downloading token file: token_20250607_093140.json (Modified: 2025-06-07T02:31:41.527Z, ID: 11287jFJGN_zkLvVZAi7xVFnnLwDMMV3C)
2025-06-07 09:51:26,103 - token_downloader - INFO - Using service account to download token
2025-06-07 09:51:29,305 - token_downloader - INFO - Download progress: 100%
2025-06-07 09:51:29,305 - token_downloader - INFO - Processing token data from file: token_20250607_093140.json
2025-06-07 09:51:29,306 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-06-07 09:51:29,306 - token_downloader - INFO - Token expiry: 2025-06-07T03:31:37.861974Z
2025-06-07 09:51:29,335 - token_downloader - INFO - Successfully downloaded and validated new token
2025-06-07 09:51:29,335 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-07 09:51:29,337 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-06-07 09:51:52,303 - IFESS-Config - INFO - Testing database connection with:
2025-06-07 09:51:52,304 - IFESS-Config - INFO -   DB Path: D:/Gawean Rebinmas/Monitoring Database/Database Ifess/PTRJ_AB1_08042025/PTRJ_AB1.FDB
2025-06-07 09:51:52,304 - IFESS-Config - INFO -   Username: sysdba
2025-06-07 09:51:52,305 - IFESS-Config - INFO -   ISQL Path: C:/Program Files (x86)/Firebird/Firebird_1_5/bin/isql.exe
2025-06-07 09:51:52,306 - IFESS-Config - INFO -   Use localhost: True
2025-06-07 09:51:52,426 - IFESS-Config - INFO - test_connection result: True
2025-06-07 09:51:56,265 - IFESS-Config - INFO - Using token_downloader to get token from Google Drive
2025-06-07 09:51:56,265 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-07 09:51:56,266 - token_downloader - INFO - Token expires in 39.7 minutes (threshold: 60.0 minutes)
2025-06-07 09:51:56,266 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-06-07 09:51:56,266 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-07 09:51:56,266 - token_downloader - INFO - Using service account to list files
2025-06-07 09:51:56,646 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-07 09:51:56,646 - token_downloader - INFO - Found file: token_20250607_093140.json (Modified: 2025-06-07T02:31:41.527Z)
2025-06-07 09:51:56,646 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-06-07 09:51:56,648 - token_downloader - ERROR - Traceback (most recent call last):
  File "token_downloader.py", line 286, in process_token_files
    max_age = datetime.timedelta(days=MAX_TOKEN_AGE_DAYS)
              ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-06-07 09:51:56,648 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-06-07 09:51:56,648 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-07 09:51:56,651 - IFESS-Config - INFO - Building Google Drive API service...
2025-06-07 09:51:56,651 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-07 09:51:56,655 - IFESS-Config - INFO - Google Drive API service built successfully
2025-06-07 09:51:57,478 - IFESS-Config - INFO - Logged in as: PTRJ IFES (<EMAIL>)
2025-06-07 09:51:57,478 - IFESS-Config - INFO - Storage: 71.64 GB used of 100.00 GB total
2025-06-07 09:51:57,480 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-06-07 09:52:05,161 - IFESS-Config - INFO - Using OAuth config as primary configuration file
2025-06-07 09:52:05,161 - IFESS-Config - INFO - Configuration saved to primary file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config_oauth_tokens.json
2025-06-07 09:52:05,162 - IFESS-Config - INFO - Configuration also saved to secondary file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-06-07 09:52:05,162 - IFESS-Config - INFO - Configuration saved successfully
2025-06-07 09:52:05,162 - IFESS-Config - INFO - Primary config file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config_oauth_tokens.json
2025-06-07 09:52:05,162 - IFESS-Config - INFO - MEGA credentials saved for: <EMAIL>
2025-06-07 09:52:09,282 - IFESS-Config - INFO - Using OAuth config as primary configuration file
2025-06-07 09:52:09,282 - IFESS-Config - INFO - Configuration saved to primary file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config_oauth_tokens.json
2025-06-07 09:52:09,283 - IFESS-Config - INFO - Configuration also saved to secondary file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-06-07 09:52:09,283 - IFESS-Config - INFO - Configuration saved successfully
2025-06-07 09:52:09,283 - IFESS-Config - INFO - Primary config file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config_oauth_tokens.json
2025-06-07 09:52:09,283 - IFESS-Config - INFO - MEGA credentials saved for: <EMAIL>
2025-06-07 10:02:00,919 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-06-07 10:02:01,069 - IFESS-Config - INFO - Configuration loaded from OAuth config: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config_oauth_tokens.json
2025-06-07 10:02:01,069 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-06-07 10:02:01,069 - IFESS-Config - INFO - Loaded Google Drive credentials file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-06-07 10:02:01,069 - IFESS-Config - INFO - Loaded scheduled upload settings: enabled=True, time=9:59, service=gdrive
2025-06-07 10:02:01,069 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config_oauth_tokens.json
2025-06-07 10:02:01,070 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-06-07 10:02:01,466 - IFESS-Config - INFO - token_downloader module imported successfully
2025-06-07 10:02:01,466 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-06-07 10:02:01,466 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-07 10:02:01,467 - token_downloader - INFO - Token expires in 29.6 minutes (threshold: 60.0 minutes)
2025-06-07 10:02:01,467 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-06-07 10:02:01,467 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-07 10:02:01,499 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\ptrj-backup-services-account.json
2025-06-07 10:02:01,501 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-07 10:02:01,502 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-06-07 10:02:01,503 - token_downloader - INFO - Using service account to list files
2025-06-07 10:02:02,727 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-07 10:02:02,727 - token_downloader - INFO - Found file: token_20250607_093140.json (Modified: 2025-06-07T02:31:41.527Z)
2025-06-07 10:02:02,727 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-06-07 10:02:02,728 - token_downloader - ERROR - Traceback (most recent call last):
  File "token_downloader.py", line 286, in process_token_files
    max_age = datetime.timedelta(days=MAX_TOKEN_AGE_DAYS)
              ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-06-07 10:02:02,728 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-06-07 10:02:02,728 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-07 10:02:02,728 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-06-07 10:02:11,638 - IFESS-Config - INFO - Using OAuth config as primary configuration file
2025-06-07 10:02:11,639 - IFESS-Config - INFO - Configuration saved to primary file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config_oauth_tokens.json
2025-06-07 10:02:11,639 - IFESS-Config - INFO - Configuration also saved to secondary file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-06-07 10:02:11,639 - IFESS-Config - INFO - Configuration saved successfully
2025-06-07 10:02:11,639 - IFESS-Config - INFO - Primary config file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config_oauth_tokens.json
2025-06-07 10:02:11,639 - IFESS-Config - INFO - MEGA credentials saved for: <EMAIL>
2025-06-07 10:02:19,765 - IFESS-Config - INFO - Using OAuth config as primary configuration file
2025-06-07 10:02:19,766 - IFESS-Config - INFO - Configuration saved to primary file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config_oauth_tokens.json
2025-06-07 10:02:19,766 - IFESS-Config - INFO - Configuration also saved to secondary file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-06-07 10:02:19,767 - IFESS-Config - INFO - Configuration saved successfully
2025-06-07 10:02:19,767 - IFESS-Config - INFO - Primary config file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config_oauth_tokens.json
2025-06-07 10:02:19,767 - IFESS-Config - INFO - MEGA credentials saved for: <EMAIL>
