#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IFESS Hidden Client

This application runs completely hidden without any visible window or taskbar icon.
It connects to the server and executes queries on the local database as requested.
"""

import os
import sys
import json
import socket
import threading
import time
import uuid
import platform
import logging
import traceback
import ctypes
import tempfile
import subprocess
import datetime
import base64
import schedule
import zipfile
import shutil
from logging.handlers import RotatingFileHandler

# Ensure UTF-8 encoding for console output - handle PyInstaller compilation
try:
    # Check if stdout/stderr have encoding attribute and it's not None (PyInstaller fix)
    if hasattr(sys.stdout, 'encoding') and sys.stdout.encoding is not None:
        if sys.stdout.encoding.lower() != 'utf-8':
            import codecs
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    elif hasattr(sys.stdout, 'buffer'):  # Fallback for compiled executables
        import codecs
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
        
    if hasattr(sys.stderr, 'encoding') and sys.stderr.encoding is not None:
        if sys.stderr.encoding.lower() != 'utf-8':
            import codecs
            sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')
    elif hasattr(sys.stderr, 'buffer'):  # Fallback for compiled executables
        import codecs
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')
except Exception as e:
    # If encoding setup fails, continue without modification to avoid breaking startup
    try:
        print(f"Warning: Could not set UTF-8 encoding: {e}")
    except:
        # Even print might fail in some PyInstaller scenarios, so we ignore completely
        pass

# Constants
# Get the base directory - handle both running as script and as frozen executable
if getattr(sys, 'frozen', False):
    # Running as compiled executable
    BASE_DIR = os.path.dirname(sys.executable)
else:
    # Running as script
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# Define paths relative to base directory
CONFIG_FILE = os.path.join(BASE_DIR, "client_config.json")
OAUTH_CONFIG_FILE = os.path.join(BASE_DIR, "client_config_oauth_tokens.json")
DEFAULT_PORT = 5555  # Port untuk koneksi server-client (bukan port web interface)
LOG_FILE = os.path.join(BASE_DIR, "ifess_client_hidden.log")
MUTEX_NAME = "Global\\IFESS_Hidden_Client_Running"

# GUI Constants
TITLE = "IFESS Client Database Application"

# Helper functions
def format_file_size(size_bytes):
    """Format file size in human-readable format"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes/1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes/(1024*1024):.1f} MB"
    else:
        return f"{size_bytes/(1024*1024*1024):.1f} GB"

# Setup logging with UTF-8 encoding and rotating file handler
log_handler = RotatingFileHandler(
    LOG_FILE,
    maxBytes=5*1024*1024,  # 5 MB
    backupCount=3,
    encoding='utf-8'  # Ensure UTF-8 encoding for log files
)
log_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log_handler.setFormatter(log_formatter)

logger = logging.getLogger("IFESS-Hidden")
logger.setLevel(logging.INFO)
logger.addHandler(log_handler)

# Import common modules
try:
    from common.network import NetworkMessage, send_message, receive_message
    from common.db_utils import FirebirdConnector
    from config_manager import ConfigManager
    logger.info("Successfully imported core network, database, and config modules")
except ImportError:
    # Try relative import
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    try:
        from common.network import NetworkMessage, send_message, receive_message
        from common.db_utils import FirebirdConnector
        from config_manager import ConfigManager
        logger.info("Successfully imported core modules using relative import")
    except ImportError as e:
        logger.critical(f"Failed to import required modules: {e}")
        sys.exit(1)

# Try to import MEGA client (optional)
try:
    from mega_client import MegaClient
    logger.info("MEGA client imported successfully")
except ImportError:
    logger.warning("MEGA client not available, MEGA functionality will be disabled")
    MegaClient = None

# Define message types if not already defined in NetworkMessage
if not hasattr(NetworkMessage, 'TYPE_RESULT'):
    NetworkMessage.TYPE_RESULT = 'result'
if not hasattr(NetworkMessage, 'TYPE_ERROR'):
    NetworkMessage.TYPE_ERROR = 'error'
if not hasattr(NetworkMessage, 'TYPE_DB_REQUEST'):
    NetworkMessage.TYPE_DB_REQUEST = 'db_request'
if not hasattr(NetworkMessage, 'TYPE_DB_INFO'):
    NetworkMessage.TYPE_DB_INFO = 'db_info'
if not hasattr(NetworkMessage, 'TYPE_DB_CHUNK'):
    NetworkMessage.TYPE_DB_CHUNK = 'db_chunk'
if not hasattr(NetworkMessage, 'TYPE_DB_COMPLETE'):
    NetworkMessage.TYPE_DB_COMPLETE = 'db_complete'

# Add other message types that might be needed
if not hasattr(NetworkMessage, 'TYPE_REGISTER'):
    NetworkMessage.TYPE_REGISTER = 'register'
if not hasattr(NetworkMessage, 'TYPE_QUERY'):
    NetworkMessage.TYPE_QUERY = 'query'
if not hasattr(NetworkMessage, 'TYPE_PING'):
    NetworkMessage.TYPE_PING = 'ping'
if not hasattr(NetworkMessage, 'TYPE_PONG'):
    NetworkMessage.TYPE_PONG = 'pong'

# Add MEGA upload message types
if not hasattr(NetworkMessage, 'TYPE_MEGA_UPLOAD_REQUEST'):
    NetworkMessage.TYPE_MEGA_UPLOAD_REQUEST = 'mega_upload_request'
if not hasattr(NetworkMessage, 'TYPE_MEGA_UPLOAD_ACK'):
    NetworkMessage.TYPE_MEGA_UPLOAD_ACK = 'mega_upload_ack'
if not hasattr(NetworkMessage, 'TYPE_MEGA_UPLOAD_PROGRESS'):
    NetworkMessage.TYPE_MEGA_UPLOAD_PROGRESS = 'mega_upload_progress'
if not hasattr(NetworkMessage, 'TYPE_MEGA_UPLOAD_RESULT'):
    NetworkMessage.TYPE_MEGA_UPLOAD_RESULT = 'mega_upload_result'

def hide_console_window():
    """Hide the console window"""
    try:
        # Get console window handle
        hwnd = ctypes.windll.kernel32.GetConsoleWindow()

        # Hide window if it exists
        if hwnd != 0:
            ctypes.windll.user32.ShowWindow(hwnd, 0)
            logger.info("Console window hidden")
        else:
            logger.warning("No console window found to hide")
    except Exception as e:
        logger.error(f"Error hiding console window: {e}")

def create_mutex():
    """Create a mutex to ensure only one instance runs"""
    try:
        mutex = ctypes.windll.kernel32.CreateMutexW(None, 1, MUTEX_NAME)
        last_error = ctypes.windll.kernel32.GetLastError()

        if last_error == 183:  # ERROR_ALREADY_EXISTS
            logger.warning("Another instance is already running")
            return False

        logger.info("Created mutex, this is the only instance running")
        return True
    except Exception as e:
        logger.error(f"Error creating mutex: {e}")
        # Continue anyway
        return True

class ClientApp:
    """Background client that connects to server and executes queries on local database"""
    def __init__(self):
        self.socket = None
        self.server_address = None
        self.server_port = DEFAULT_PORT
        self.client_id = f"client_{uuid.uuid4().hex[:8]}"
        self.display_name = f"FDB-Client-{platform.node()}"
        self.connected = False
        self.running = True
        self.db_connector = None
        self.receive_thread = None

        # Initialize MEGA client if available
        if MegaClient is not None:
            try:
                self.mega_client = MegaClient(self.client_id)
                logger.info("MEGA client initialized successfully")
            except Exception as e:
                logger.warning(f"MEGA client initialization failed: {e}")
                self.mega_client = None
        else:
            logger.info("MEGA client not available - MEGA upload functionality disabled")
            self.mega_client = None

        # Auto-reconnect parameters
        self.reconnect_interval = 5  # seconds
        self.reconnect_thread = None
        self.is_connecting = False

        # Scheduled upload parameters
        self.scheduler_thread = None
        self.scheduler_running = False
        self.upload_in_progress = False

        # Initialize enhanced configuration manager with Unicode handling
        logger.info("Starting Firebird Client in hidden mode with enhanced configuration")
        try:
            self.config_manager = ConfigManager(OAUTH_CONFIG_FILE, BASE_DIR)
            config_validation = self.config_manager.validate_config()
            
            if not config_validation['valid']:
                logger.warning(f"Configuration issues detected: {config_validation['issues']}")
                logger.info("Attempting to auto-detect and fix configuration...")
                self.config_manager.setup_initial_config()
                config_validation = self.config_manager.validate_config()
                
            if not config_validation['valid']:
                logger.error(f"Failed to create valid configuration: {config_validation['issues']}")
                # Don't exit, try fallback
                logger.info("Falling back to old configuration system...")
                self.config_manager = None
            else:
                logger.info("Configuration validation successful")
            
        except UnicodeEncodeError as unicode_error:
            logger.error(f"Unicode encoding error in configuration manager: {unicode_error}")
            logger.info("Falling back to old configuration system...")
            self.config_manager = None
        except Exception as e:
            logger.error(f"Failed to initialize configuration manager: {e}")
            logger.info("Falling back to old configuration system...")
            self.config_manager = None
        
        # If enhanced config failed, use fallback
        if not hasattr(self, 'config_manager') or self.config_manager is None:
            logger.info("Using fallback configuration system...")
            config_valid = self.load_config()
            if not config_valid:
                self.create_default_config()
                config_valid = self.load_config()
            if not config_valid:
                logger.error("Failed to load or create valid configuration. Exiting.")
                sys.exit(1)

        # Initialize database connector with enhanced configuration
        self._initialize_enhanced_database_connector()

        # Setup scheduled uploads
        self.setup_scheduled_upload()

        # Automatically connect to server
        self.start_auto_reconnect()

    def _initialize_enhanced_database_connector(self):
        """Initialize database connector using enhanced configuration manager"""
        try:
            if hasattr(self, 'config_manager') and self.config_manager:
                # Use enhanced configuration manager
                self.db_connector = self.config_manager.create_firebird_connector()
                
                # Load connection settings from config manager
                config = self.config_manager.get_config()
                self.server_address = config.get('server_address', 'localhost')
                self.server_port = config.get('server_port', DEFAULT_PORT)
                self.reconnect_interval = config.get('reconnect_interval', 5)
                self.client_id = config.get('client_id', self.client_id)
                self.display_name = config.get('display_name', self.display_name)
                
                logger.info(f"Enhanced database connector initialized: {self.db_connector.db_path}")
                logger.info(f"Using ISQL path: {self.db_connector.isql_path}")
                
                # Test the connection with proper error handling for Unicode
                try:
                    test_result = self.db_connector.test_connection()
                    if test_result:
                        logger.info("Database connection test successful")
                    else:
                        logger.warning("Database connection test failed")
                except UnicodeEncodeError as unicode_error:
                    logger.warning(f"Database connection test had encoding issues, but may still work: {unicode_error}")
                except Exception as test_error:
                    logger.warning(f"Database connection test error: {test_error}")
                    
            else:
                # Fallback to old configuration method
                logger.warning("Enhanced config manager not available, using fallback configuration")
                self._initialize_fallback_database_connector()
                
        except UnicodeEncodeError as unicode_error:
            logger.error(f"Unicode encoding error in enhanced database connector: {unicode_error}")
            logger.info("Falling back to old configuration method")
            self._initialize_fallback_database_connector()
        except Exception as e:
            logger.error(f"Error initializing enhanced database connector: {e}")
            logger.info("Falling back to old configuration method")
            self._initialize_fallback_database_connector()

    def _initialize_fallback_database_connector(self):
        """Fallback method to initialize database connector using old configuration"""
        try:
            config_valid = self.load_config()
            if not config_valid:
                self.create_default_config()
                config_valid = self.load_config()
            if not config_valid:
                raise Exception("Failed to load valid configuration")
        except Exception as e:
            logger.error(f"Fallback database connector initialization failed: {e}")
            self.db_connector = None

    def load_config(self):
        """Load configuration from file

        Returns:
            bool: True if config is valid, False otherwise
        """
        try:
            config = self.get_config()

            # Check if config has required fields
            if not config.get('server_address'):
                logger.error("Config missing server address")
                return False

            # Load server config
            self.server_address = config.get('server_address')
            self.server_port = config.get('server_port', DEFAULT_PORT)
            self.reconnect_interval = config.get('reconnect_interval', 5)

            # Load client config
            if 'client_id' in config:
                self.client_id = config['client_id']
            if 'display_name' in config:
                self.display_name = config['display_name']

            # Load database config
            db_config = config.get('database', {})
            if not db_config or 'path' not in db_config:
                logger.error("Config missing database path")
                return False

            # Check if database file exists
            if not os.path.exists(db_config['path']):
                logger.error(f"Database file not found: {db_config['path']}")
                return False

            try:
                self.db_connector = FirebirdConnector(
                    db_path=db_config['path'],
                    username=db_config.get('username', 'SYSDBA'),
                    password=db_config.get('password', 'masterkey'),
                    isql_path=db_config.get('isql_path'),
                    use_localhost=db_config.get('use_localhost', True)
                )
                logger.info(f"Database connector initialized from config: {db_config['path']}")
                return True
            except Exception as e:
                logger.error(f"Error initializing database from config: {e}")
                return False
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            return False

    def get_config(self):
        """Get configuration from file, prioritizing OAuth config, or create default if not exists"""
        # First try to load OAuth config (contains Google Drive credentials)
        if os.path.exists(OAUTH_CONFIG_FILE):
            try:
                with open(OAUTH_CONFIG_FILE, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    logger.info(f"Configuration loaded from OAuth config: {OAUTH_CONFIG_FILE}")
                    return config
            except (json.JSONDecodeError, UnicodeDecodeError) as e:
                logger.error(f"Error parsing OAuth config file: {e}")
                # Fall back to regular config
        
        # Fall back to regular config file
        if os.path.exists(CONFIG_FILE):
            try:
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    logger.info(f"Configuration loaded from basic config: {CONFIG_FILE}")
                    return config
            except (json.JSONDecodeError, UnicodeDecodeError) as e:
                logger.error(f"Error parsing config file: {e}")
                # If the file is corrupted, create a new one
                return self.create_default_config()
        else:
            # Create default config file
            return self.create_default_config()

    def create_default_config(self):
        """Create default configuration file"""
        default_config = {
            "server_address": "localhost",
            "server_port": DEFAULT_PORT,
            "reconnect_interval": 5,
            "client_id": self.client_id,
            "display_name": self.display_name,
            "database": {
                "path": "C:/Firebird/database.fdb",
                "username": "SYSDBA",
                "password": "masterkey",
                "isql_path": "C:/Program Files/Firebird/Firebird_1_5/bin/isql.exe",
                "use_localhost": True
            }
        }

        # Log the default config
        logger.info(f"Creating default config with database path: {default_config['database']['path']}")

        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(CONFIG_FILE), exist_ok=True)

            # Write default config with UTF-8 encoding
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)

            logger.info(f"Created default config file at {CONFIG_FILE}")
            return default_config
        except Exception as e:
            logger.error(f"Error creating default config: {e}")
            # Return default config even if we couldn't save it
            return default_config

    def save_config(self):
        """Save configuration to file with multiple methods to ensure it works"""
        try:
            config = {
                'server_address': self.server_address,
                'server_port': self.server_port,
                'reconnect_interval': self.reconnect_interval,
                'client_id': self.client_id,
                'display_name': self.display_name,
                'database': {}
            }

            # Save database configuration if available
            if self.db_connector:
                config['database'] = {
                    'path': self.db_connector.db_path,
                    'username': self.db_connector.username,
                    'password': self.db_connector.password,
                    'isql_path': self.db_connector.isql_path,
                    'use_localhost': self.db_connector.use_localhost
                }

            # Ensure directory exists
            os.makedirs(os.path.dirname(CONFIG_FILE), exist_ok=True)

            # Method 1: Use json.dump with UTF-8 encoding
            try:
                with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                logger.info(f"Configuration saved to {CONFIG_FILE}")
            except Exception as e:
                logger.error(f"Error saving config with json.dump: {e}")

                # Method 2: Write to file using string
                try:
                    config_str = json.dumps(config, indent=2)
                    with open(CONFIG_FILE, 'w') as f:
                        f.write(config_str)
                    logger.info(f"Configuration saved to {CONFIG_FILE} using string write")
                except Exception as e2:
                    logger.error(f"Error saving config with string write: {e2}")

                    # Method 3: Write to temporary file then rename
                    try:
                        temp_file = f"{CONFIG_FILE}.tmp"
                        with open(temp_file, 'w') as f:
                            json.dump(config, f, indent=2)
                        os.replace(temp_file, CONFIG_FILE)
                        logger.info(f"Configuration saved to {CONFIG_FILE} using temp file")
                    except Exception as e3:
                        logger.error(f"Error saving config with temp file: {e3}")
                        raise e3
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")

    def start_auto_reconnect(self):
        """Start auto-reconnect thread"""
        if self.reconnect_thread and self.reconnect_thread.is_alive():
            return

        self.reconnect_thread = threading.Thread(target=self.auto_reconnect_loop)
        self.reconnect_thread.daemon = True
        self.reconnect_thread.start()
        logger.info("Auto-reconnect thread started")

    def auto_reconnect_loop(self):
        """Loop to try auto-reconnect to server"""
        while self.running:
            if not self.connected and not self.is_connecting:
                logger.info(f"Attempting to connect to {self.server_address}:{self.server_port}...")
                self.connect_to_server(self.server_address, self.server_port)

                # If connection failed, wait longer before next attempt
                if not self.connected:
                    logger.info(f"Connection failed, waiting {self.reconnect_interval} seconds before next attempt")
                    time.sleep(self.reconnect_interval)
                    continue

            # Wait interval before next attempt if needed
            if self.connected:
                # If connected, just sleep for a while to check status periodically
                time.sleep(10)
            else:
                # If not connected, wait the reconnect interval
                for i in range(self.reconnect_interval):
                    if not self.running or self.connected:
                        break
                    time.sleep(1)

    def connect_to_server(self, address, port):
        """Connect to server"""
        if self.connected:
            return

        if self.is_connecting:
            return

        self.is_connecting = True

        try:
            # Create socket
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(60.0)  # Increased timeout for better reliability

            # Add keep-alive mechanism
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)

            # Set keep-alive parameters if on Linux/Darwin systems
            if hasattr(socket, 'TCP_KEEPIDLE') and hasattr(socket, 'TCP_KEEPINTVL') and hasattr(socket, 'TCP_KEEPCNT'):
                self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPIDLE, 30)
                self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPINTVL, 5)
                self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPCNT, 5)

            # Set TCP_NODELAY to disable Nagle's algorithm for better responsiveness
            self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)

            # Set larger buffer sizes
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 256 * 1024)  # 256KB receive buffer
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 256 * 1024)  # 256KB send buffer

            self.socket.connect((address, port))

            # Update status
            self.server_address = address
            self.server_port = port
            self.connected = True

            # Register client to server
            self.register_to_server()

            # Start thread to receive messages
            self.receive_thread = threading.Thread(target=self.receive_messages)
            self.receive_thread.daemon = True
            self.receive_thread.start()

            logger.info(f"Connected to server: {address}:{port}")

            # Save configuration
            self.save_config()
        except Exception as e:
            logger.error(f"Error connecting to server: {e}")
            self.connected = False
            self.socket = None
        finally:
            self.is_connecting = False

    def register_to_server(self):
        """Register client to server"""
        if not self.connected or not self.socket:
            return

        try:
            # Create registration message
            registration = {
                'client_id': self.client_id,
                'display_name': self.display_name,
                'platform': platform.platform(),
                'python_version': platform.python_version(),
                'database': {
                    'path': self.db_connector.db_path if self.db_connector else None
                }
            }

            # Send registration message
            message = NetworkMessage(
                msg_type=NetworkMessage.TYPE_REGISTER,
                data=registration
            )
            send_message(self.socket, message)
            logger.info(f"Registered to server as {self.display_name} ({self.client_id})")
        except Exception as e:
            logger.error(f"Error registering to server: {e}")
            self.disconnect_from_server()

    def disconnect_from_server(self):
        """Disconnect from server"""
        if not self.connected:
            return

        try:
            self.connected = False
            if self.socket:
                self.socket.close()
                self.socket = None

            logger.info("Disconnected from server")
        except Exception as e:
            logger.error(f"Error disconnecting: {e}")

    def receive_messages(self):
        """Continuously receive messages from the server"""
        logger.info("Starting message receiving thread")

        while self.running and self.socket:
            try:
                message = receive_message(self.socket)

                if not message:
                    # No message received, server might have disconnected
                    logger.warning("No message received, server might have disconnected")
                    self.disconnect_from_server()
                    break

                # Log received message type for debugging
                if hasattr(message, 'msg_type'):
                    logger.info(f"Received message with type: {message.msg_type}")
                elif hasattr(message, 'type'):
                    logger.info(f"Received message with old 'type' attribute: {message.type}")
                else:
                    logger.error(f"Received message has no type attribute: {str(message)}")
                    logger.debug(f"Message attributes: {dir(message)}")
                    continue

                # Process message based on type - use getattr to safely access attribute
                msg_type = getattr(message, 'msg_type', getattr(message, 'type', None))
                if not msg_type:
                    logger.error(f"Cannot determine message type: {str(message)}")
                    continue

                if msg_type == NetworkMessage.TYPE_PING:
                    # Ping message, respond with pong
                    logger.debug("Received ping, sending pong")
                    self.send_pong()
                elif msg_type == NetworkMessage.TYPE_QUERY:
                    # Execute query and send result
                    query_data = message.data
                    logger.info(f"Received query request: {query_data}")

                    # Handle query in a separate thread to avoid blocking the receive thread
                    query_thread = threading.Thread(
                        target=self.execute_query,
                        args=(query_data,)
                    )
                    query_thread.daemon = True
                    query_thread.start()
                elif msg_type == NetworkMessage.TYPE_DB_REQUEST:
                    # Handle database file request
                    logger.info("Received database file request")
                    # Handle in a separate thread
                    db_thread = threading.Thread(
                        target=self.handle_db_file_request,
                        args=(message.data,)
                    )
                    db_thread.daemon = True
                    db_thread.start()
                elif msg_type == NetworkMessage.TYPE_MEGA_UPLOAD_REQUEST:
                        # Handle MEGA upload request
                    logger.info(f"[MEGA] ===== RECEIVED MEGA UPLOAD REQUEST =====")
                    logger.info(f"[MEGA] Request data: {message.data}")
                    # Handle in a separate thread
                    mega_thread = threading.Thread(
                        target=self.handle_mega_upload_request,
                        args=(message.data,)
                    )
                    mega_thread.daemon = True
                    mega_thread.start()
                    logger.info(f"[MEGA] Started MEGA upload thread")
                elif msg_type == 'gdrive_upload_request':
                    # Handle Google Drive upload request
                    logger.info(f"[GDRIVE] ===== RECEIVED GDRIVE UPLOAD REQUEST =====")
                    logger.info(f"[GDRIVE] Request data: {message.data}")
                    # Handle in a separate thread
                    gdrive_thread = threading.Thread(
                        target=self.handle_gdrive_upload_request,
                        args=(message.data,)
                    )
                    gdrive_thread.daemon = True
                    gdrive_thread.start()
                    logger.info(f"[GDRIVE] Started Google Drive upload thread")
                else:
                    # Unknown message type
                    logger.warning(f"Received unknown message type: {msg_type}")
            except Exception as e:
                logger.error(f"Error receiving message: {e}")
                logger.error(traceback.format_exc())
                if self.running:
                    # Only disconnect if still running
                    self.disconnect_from_server()
                    break

    def send_pong(self):
        """Send pong message to server"""
        if not self.connected or not self.socket:
            return

        try:
            # Create pong message
            message = NetworkMessage(
                msg_type=NetworkMessage.TYPE_PONG,
                data={
                    'client_id': self.client_id,
                    'timestamp': time.time()
                }
            )
            send_message(self.socket, message)
            logger.debug("Sent pong to server")
        except Exception as e:
            logger.error(f"Error sending pong: {e}")
            self.disconnect_from_server()

    def execute_query(self, query_data):
        """Execute query from server"""
        query = query_data.get('query', '')
        description = query_data.get('description', '')
        max_rows = query_data.get('max_rows', 1000)
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        logger.info(f"Received query execution request")
        logger.debug(f"Query: {query}")
        logger.debug(f"Description: {description}")
        logger.debug(f"Max rows: {max_rows}")

        if not query:
            logger.error("Query is empty")
            self.send_error_result("Query is empty", query_data)
            return

        if not self.db_connector:
            logger.error("Database not selected")
            self.send_error_result("Database not selected", query_data)
            return

        # Make sure database path is still valid
        if not os.path.exists(self.db_connector.db_path):
            logger.error(f"Database file not found: {self.db_connector.db_path}")
            self.send_error_result(f"Database file not found: {self.db_connector.db_path}", query_data)
            return

        logger.info(f"Executing query: {query[:100]}...")

        try:
            # Execute query
            logger.debug("Executing query via db_connector...")
            result = self.db_connector.execute_query(query)
            logger.debug("Query executed successfully")

            # Enhanced result processing with detailed logging
            logger.info(f"Query successful: {len(result)} result sets found")
            
            # Process each result set with enhanced validation
            for i, rs in enumerate(result):
                headers = rs.get('headers', [])
                rows = rs.get('rows', [])
                row_count = rs.get('row_count', len(rows))
                column_count = rs.get('column_count', len(headers))
                data_types = rs.get('data_types', {})
                parsing_info = rs.get('parsing_info', {})
                execution_info = rs.get('execution_info', {})
                
                logger.info(f"  Result set {i+1}: {row_count} rows, {column_count} columns")
                logger.info(f"  Parsing method: {parsing_info.get('method', 'unknown')}")
                
                if data_types:
                    logger.debug(f"  Column types: {data_types}")
                
                if len(rows) > 0:
                    logger.info(f"  Sample first row: {list(rows[0].values())[:3]}...")
                    logger.debug(f"  Sample row data: {str(rows[0])[:200]}...")

                # Enhanced validation with type preservation
                validated_rows = []
                for row_idx, row in enumerate(rows):
                    if not isinstance(row, dict):
                        # Convert to dict if it's not already
                        if isinstance(row, (list, tuple)):
                            row_dict = {}
                            for j, header in enumerate(headers):
                                if j < len(row):
                                    row_dict[header] = row[j]
                                else:
                                    row_dict[header] = None
                            validated_rows.append(row_dict)
                        else:
                            logger.warning(f"Skipping row {row_idx} with invalid type: {type(row)}")
                    else:
                        # Ensure all headers are present in the row
                        validated_row = {}
                        for header in headers:
                            validated_row[header] = row.get(header)
                        validated_rows.append(validated_row)

                # Update result set with validated data and enhanced metadata
                rs['rows'] = validated_rows
                rs['row_count'] = len(validated_rows)
                rs['validation_info'] = {
                    'original_rows': len(rows),
                    'validated_rows': len(validated_rows),
                    'validation_timestamp': datetime.datetime.now().isoformat()
                }
                
                logger.debug(f"Enhanced validation: {len(validated_rows)} rows processed with complete metadata")

            # Send result to server
            logger.debug("Sending result to server...")
            self.send_query_result(query, result, description)

            logger.info("Query executed successfully")
        except Exception as e:
            error_message = str(e)
            logger.error(f"Error executing query: {error_message}")
            logger.debug(traceback.format_exc())
            self.send_error_result(error_message, query_data)

    def send_query_result(self, query, result, description):
        """Send query result to server"""
        if not self.connected or not self.socket:
            logger.error("Cannot send result: not connected to server")
            return False

        try:
            # Format result data
            result_data = {
                'query': query,
                'timestamp': time.time(),
                'description': description,
                'status': 'success'
            }

            # Enhanced result data with complete metadata
            if result:
                # Use the first result set but include enhanced metadata
                first_result = result[0] if result else {'rows': [], 'headers': []}

                # Extract enhanced data from the first result set
                result_data['rows'] = first_result.get('rows', [])
                result_data['headers'] = first_result.get('headers', [])
                result_data['row_count'] = first_result.get('row_count', len(result_data['rows']))
                result_data['column_count'] = first_result.get('column_count', len(result_data['headers']))
                result_data['data_types'] = first_result.get('data_types', {})
                result_data['parsing_info'] = first_result.get('parsing_info', {})
                result_data['execution_info'] = first_result.get('execution_info', {})
                result_data['validation_info'] = first_result.get('validation_info', {})
                
                # Include all result sets if multiple exist
                if len(result) > 1:
                    result_data['all_result_sets'] = result
                    result_data['result_set_count'] = len(result)
                else:
                    result_data['result_set_count'] = 1

                # Enhanced logging with metadata
                logger.info(f"Sending enhanced result to server:")
                logger.info(f"  - {result_data['row_count']} rows, {result_data['column_count']} columns")
                logger.info(f"  - Parsing method: {result_data['parsing_info'].get('method', 'unknown')}")
                logger.info(f"  - Data types detected: {len(result_data['data_types'])} columns")
                
                if result_data['rows'] and len(result_data['rows']) > 0:
                    logger.info(f"  - First row sample: {str(result_data['rows'][0])[:200]}")
            else:
                result_data['rows'] = []
                result_data['headers'] = []
                result_data['row_count'] = 0
                result_data['column_count'] = 0
                result_data['data_types'] = {}
                result_data['parsing_info'] = {'method': 'no_data'}
                result_data['result_set_count'] = 0
                logger.info("No result data to send")

            # Create message
            message = NetworkMessage(
                msg_type='result',  # Use 'result' instead of NetworkMessage.TYPE_RESULT for better compatibility
                data=result_data,
                client_id=self.client_id
            )

            # Log the message being sent
            logger.info(f"Sending query result message with type: {message.msg_type}")
            logger.debug(f"Message data: {str(message.data)[:500]}...")

            # Send message with retries
            max_retries = 3
            success = False

            for retry in range(max_retries):
                try:
                    logger.debug(f"Sending result message (attempt {retry+1}/{max_retries})")
                    logger.debug(f"Socket state before sending result: {self.socket.getsockname() if self.socket else 'No socket'}")
                    send_success = send_message(self.socket, message)

                    if send_success:
                        logger.info(f"Query result sent to server ({len(result_data.get('rows', []))} rows)")
                        success = True
                        break
                    else:
                        logger.warning(f"Failed to send query result, attempt {retry+1}/{max_retries}")
                        if retry < max_retries - 1:
                            time.sleep(1.0)  # Wait before retrying
                except Exception as retry_error:
                    logger.error(f"Error sending query result on attempt {retry+1}: {retry_error}")
                    if retry < max_retries - 1:
                        time.sleep(1.0)  # Wait before retrying

            if not success:
                logger.error("Failed to send query result to server after all retries")

            return success
        except Exception as e:
            logger.error(f"Error sending query result: {e}")
            logger.debug(traceback.format_exc())
            self.disconnect_from_server()
            return False

    def send_error_result(self, error_message, query_data):
        """Send error result to server"""
        if not self.connected or not self.socket:
            logger.error("Cannot send error result: not connected to server")
            return

        try:
            # Create error data
            error_data = {
                'query': query_data.get('query', ''),
                'description': query_data.get('description', ''),
                'error': error_message,
                'timestamp': time.time(),
                'status': 'error',
                'rows': [],
                'headers': []
            }

            # Create error message - use 'result' type with error status for better compatibility
            message = NetworkMessage(
                msg_type='result',  # Use 'result' instead of 'error' for better compatibility
                data=error_data,
                client_id=self.client_id
            )

            # Log the message being sent
            logger.info(f"Sending error result message with type: {message.msg_type}")
            logger.debug(f"Error message data: {str(message.data)[:500]}...")

            # Send message with retries
            max_retries = 3
            success = False

            for retry in range(max_retries):
                try:
                    logger.debug(f"Sending error message (attempt {retry+1}/{max_retries})")
                    send_success = send_message(self.socket, message)

                    if send_success:
                        logger.info("Error result sent to server")
                        success = True
                        break
                    else:
                        logger.warning(f"Failed to send error result, attempt {retry+1}/{max_retries}")
                        if retry < max_retries - 1:
                            time.sleep(1.0)  # Wait before retrying
                except Exception as retry_error:
                    logger.error(f"Error sending error result on attempt {retry+1}: {retry_error}")
                    if retry < max_retries - 1:
                        time.sleep(1.0)  # Wait before retrying

            if not success:
                logger.error("Failed to send error result to server after all retries")

            return success
        except Exception as e:
            logger.error(f"Error sending error result: {e}")
            logger.debug(traceback.format_exc())
            self.disconnect_from_server()
            return False

    def handle_db_file_request(self, request_data):
        """Handle request to send database file to server"""
        logger.info("===== DATABASE IMPORT REQUEST RECEIVED =====")
        logger.info(f"Received database file request from server at {time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"Request data: {request_data}")
        logger.debug(f"Request data details: {json.dumps(request_data, indent=2) if isinstance(request_data, dict) else request_data}")
        logger.debug(f"[IMPORT-DEBUG] Request data type: {type(request_data)}")
        logger.debug(f"[IMPORT-DEBUG] Request data keys: {request_data.keys() if isinstance(request_data, dict) else 'Not a dictionary'}")

        # Print to console for immediate visibility
        print(f"\n[IMPORT] ===== PROCESSING DATABASE IMPORT REQUEST =====")
        print(f"[IMPORT] Processing time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"[IMPORT] Request data: {request_data}")

        # Log client connection details
        logger.info(f"Client connection details - ID: {self.client_id}, Connected: {self.connected}")
        logger.debug(f"Socket details - timeout: {self.socket.gettimeout() if self.socket else 'No socket'}")
        logger.debug(f"[IMPORT-DEBUG] Socket object ID: {id(self.socket) if self.socket else 'None'}")
        logger.debug(f"[IMPORT-DEBUG] Socket state: {'Connected' if self.connected else 'Disconnected'}")

        # Initialize import_in_progress attribute if it doesn't exist
        if not hasattr(self, '_backup_in_progress'):
            self._backup_in_progress = False
            logger.debug("[IMPORT] Initialized _backup_in_progress attribute")

        # Check if we're already processing an import request
        if self._backup_in_progress:
            logger.warning("[IMPORT] Already processing an import request. Ignoring new request.")
            logger.info("[IMPORT] Multiple import requests detected - rejecting duplicate request")
            error_data = {
                'error': "Already processing an import request",
                'timestamp': time.time()
            }
            error_message = NetworkMessage(
                msg_type=NetworkMessage.TYPE_ERROR,
                data=error_data,
                client_id=self.client_id
            )
            send_result = send_message(self.socket, error_message)
            logger.debug(f"[IMPORT] Sent error message to server: {send_result}")
            return

        # Mark that we're starting an import
        self._backup_in_progress = True
        logger.info("[IMPORT] Starting new import process - marked as in progress")
        logger.debug(f"[IMPORT-DEBUG] Client ID for this import: {self.client_id}")

        # Check database connection
        if not self.db_connector or not self.db_connector.db_path:
            logger.error("[IMPORT] Cannot send database file: No database connected")
            logger.info("[IMPORT] Database connection validation failed - no connection or path")
            error_data = {
                'error': "No database connected",
                'timestamp': time.time()
            }
            error_message = NetworkMessage(
                msg_type=NetworkMessage.TYPE_ERROR,
                data=error_data,
                client_id=self.client_id
            )
            send_result = send_message(self.socket, error_message)
            logger.debug(f"[IMPORT] Sent error message to server: {send_result}")
            self._backup_in_progress = False
            logger.info("[IMPORT] Import process aborted - marked as not in progress")
            return

        db_path = self.db_connector.db_path
        logger.info(f"[IMPORT] Database path: {db_path}")
        logger.debug(f"[IMPORT] Database connector details: {self.db_connector}")
        logger.debug(f"[IMPORT-DEBUG] Database connector type: {type(self.db_connector)}")

        # Check if file exists and get size
        if os.path.exists(db_path):
            file_size = os.path.getsize(db_path)
            logger.debug(f"[IMPORT-DEBUG] Database file size: {file_size} bytes ({format_file_size(file_size)})")
        else:
            logger.debug(f"[IMPORT-DEBUG] Database file not found: {db_path}")

        if not os.path.exists(db_path):
            logger.error(f"[IMPORT] Database file not found: {db_path}")
            logger.info("[IMPORT] Database file validation failed - file does not exist")
            error_data = {
                'error': f"Database file not found: {db_path}",
                'timestamp': time.time()
            }
            error_message = NetworkMessage(
                msg_type=NetworkMessage.TYPE_ERROR,
                data=error_data,
                client_id=self.client_id
            )
            send_result = send_message(self.socket, error_message)
            logger.debug(f"[IMPORT] Sent error message to server: {send_result}")
            self._backup_in_progress = False
            logger.info("[IMPORT] Import process aborted - marked as not in progress")
            return

        logger.info(f"[IMPORT] Database file validation successful: {db_path}")
        logger.debug(f"[IMPORT] File exists check passed. File permissions: {oct(os.stat(db_path).st_mode)}")

        try:
            # Get file size and name
            file_size = os.path.getsize(db_path)
            file_name = os.path.basename(db_path)

            # Add timestamp to filename
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            base_name, ext = os.path.splitext(file_name)
            timestamped_filename = f"{base_name}_{timestamp}{ext}"

            logger.info(f"[IMPORT] Starting import of database: {timestamped_filename}, size: {file_size} bytes ({format_file_size(file_size)})")
            logger.debug(f"[IMPORT] Import started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.debug(f"[IMPORT] Current working directory: {os.getcwd()}")

            # Set a much longer timeout during import to prevent socket timeouts
            if self.socket:
                old_timeout = self.socket.gettimeout()
                self.socket.settimeout(300.0)  # 5 minutes timeout during transfer to prevent timeouts
                logger.info(f"[IMPORT] Socket timeout increased to 300 seconds for database transfer")
                logger.debug(f"[IMPORT] Previous socket timeout was: {old_timeout} seconds")
            else:
                logger.error("[IMPORT] No socket available for import!")
                self._backup_in_progress = False
                return

            # Create database info message
            info_data = {
                'filename': timestamped_filename,
                'size': file_size,
                'db_info': {
                    'path': db_path,
                    'timestamp': datetime.datetime.now().isoformat()
                },
                'client_id': self.client_id,
                'timestamp': time.time()
            }

            logger.info(f"[IMPORT] Preparing database info message with filename: {file_name}")
            logger.debug(f"[IMPORT] Database info details: {json.dumps(info_data, indent=2)}")
            logger.debug(f"[IMPORT-DEBUG] Database info message size: {len(json.dumps(info_data))} bytes")

            # Ensure all message types are properly defined
            # These should match the server's message types
            NetworkMessage.TYPE_DB_REQUEST = 'db_request'
            NetworkMessage.TYPE_DB_INFO = 'db_info'
            NetworkMessage.TYPE_DB_CHUNK = 'db_chunk'
            NetworkMessage.TYPE_DB_COMPLETE = 'db_complete'
            NetworkMessage.TYPE_DB_ACK = 'db_ack'
            NetworkMessage.TYPE_DB_PROGRESS = 'db_progress'

            logger.debug(f"[IMPORT] Message types verified: DB_INFO={NetworkMessage.TYPE_DB_INFO}, DB_CHUNK={NetworkMessage.TYPE_DB_CHUNK}")
            logger.debug(f"[IMPORT] Creating NetworkMessage object with type={NetworkMessage.TYPE_DB_INFO}")
            logger.debug(f"[IMPORT-DEBUG] Socket object before sending DB_INFO: {self.socket != None}")
            logger.debug(f"[IMPORT-DEBUG] Socket state before sending DB_INFO: {self.socket.getsockname() if self.socket else 'No socket'}")

            info_message = NetworkMessage(
                msg_type=NetworkMessage.TYPE_DB_INFO,
                data=info_data,
                client_id=self.client_id
            )

            logger.info(f"[IMPORT] Sending database info message with type: {info_message.msg_type}")
            logger.debug(f"[IMPORT] NetworkMessage object created: {info_message}")
            logger.debug(f"[IMPORT-DEBUG] Full info message: {vars(info_message)}")

            # Retry sending the info message up to 5 times
            for retry in range(5):
                logger.debug(f"[IMPORT] Attempting to send DB info message (attempt {retry+1}/5)")
                logger.debug(f"[IMPORT-DEBUG] Socket state before send: {self.socket.getsockname() if self.socket else 'No socket'}")
                send_result = send_message(self.socket, info_message)
                logger.debug(f"[IMPORT-DEBUG] Send message result: {send_result}")

                if send_result:
                    logger.info(f"[IMPORT] Database info sent successfully on attempt {retry+1}")
                    logger.debug(f"[IMPORT] Send result: {send_result}")
                    # Wait a bit for server to process the info and prepare for chunks
                    logger.debug(f"[IMPORT] Waiting for server to process DB info message")
                    time.sleep(1.0)
                    logger.info("[IMPORT] Starting to send file chunks...")
                    logger.debug(f"[IMPORT-DEBUG] Socket state after sending DB_INFO: {self.socket.getsockname() if self.socket else 'No socket'}")
                    break
                else:
                    if retry < 4:  # Don't log after last attempt
                        logger.warning(f"[IMPORT] Failed to send database info, retrying ({retry+1}/5)...")
                        logger.debug(f"[IMPORT-DEBUG] Send result was: {send_result}")
                        time.sleep(1.0)  # Longer delay between retries
                    else:
                        logger.error("[IMPORT] Failed to send database info after 5 attempts. Aborting import.")
                        error_data = {
                            'error': "Failed to send database info after multiple attempts",
                            'timestamp': time.time()
                        }
                        error_message = NetworkMessage(
                            msg_type=NetworkMessage.TYPE_ERROR,
                            data=error_data,
                            client_id=self.client_id
                        )
                        send_message(self.socket, error_message)
                        self._backup_in_progress = False
                        return

            # Start sending file in chunks
            logger.info("[IMPORT] Preparing to send file in chunks")
            self.send_db_file_chunks(db_path, file_size)

            # Restore original timeout
            if self.socket and old_timeout:
                self.socket.settimeout(old_timeout)
                logger.debug(f"[IMPORT] Socket timeout restored to original value: {old_timeout}")

        except Exception as e:
            logger.error(f"[IMPORT] Error handling database file request: {e}")
            logger.debug(f"[IMPORT] Exception details: {traceback.format_exc()}")
            error_data = {
                'error': str(e),
                'timestamp': time.time()
            }
            error_message = NetworkMessage(
                msg_type=NetworkMessage.TYPE_ERROR,
                data=error_data,
                client_id=self.client_id
            )
            try:
                send_message(self.socket, error_message)
                logger.debug("[IMPORT] Sent error message to server")
            except Exception as send_error:
                logger.error(f"[IMPORT] Failed to send error message to server: {send_error}")
                logger.debug(f"[IMPORT] Error message send exception: {traceback.format_exc()}")

            # Make sure to mark import as not in progress
            self._backup_in_progress = False
            logger.info("[IMPORT] Import process marked as not in progress due to error")

    def send_db_file_chunks(self, file_path, file_size):
        """Send database file to server in chunks"""
        try:
            # Define chunk size (smaller chunks for better reliability)
            chunk_size = 16 * 1024  # 16KB chunks for better reliability with slow connections (reduced from 32KB)
            total_chunks = (file_size + chunk_size - 1) // chunk_size

            logger.info(f"[IMPORT] ===== STARTING DATABASE FILE TRANSFER =====")
            logger.info(f"[IMPORT] Starting to send database file in chunks. Total chunks: {total_chunks}")
            logger.debug(f"[IMPORT] File path: {file_path}, size: {file_size} bytes ({format_file_size(file_size)})")
            logger.debug(f"[IMPORT] Using chunk size: {chunk_size} bytes ({format_file_size(chunk_size)})")
            logger.debug(f"[IMPORT-DEBUG] Socket state at start of file send: {self.socket.getsockname() if self.socket else 'No socket'}")
            logger.debug(f"[IMPORT-DEBUG] Socket timeout: {self.socket.gettimeout() if self.socket else 'No socket'}")

            # Initialize variables for tracking and statistics
            start_time = time.time()
            bytes_sent = 0
            chunks_sent = 0
            last_progress_log = time.time()
            progress_interval = 2.0  # Log progress every 2 seconds
            failed_chunks = 0
            consecutive_failures = 0

            logger.debug(f"[IMPORT] Opening file for reading: {file_path}")
            logger.debug(f"[IMPORT-DEBUG] File accessible: {os.access(file_path, os.R_OK)}")
            logger.debug(f"[IMPORT-DEBUG] File permissions: {oct(os.stat(file_path).st_mode)}")

            # Setup retry parameters
            max_retries = 5  # Maximum number of retries for sending a chunk

            with open(file_path, 'rb') as f:
                logger.debug(f"[IMPORT] File opened successfully: {file_path}")
                logger.debug(f"[IMPORT-DEBUG] File object: {f}")

                chunk_number = 0
                offset = 0  # Track file offset for server to use when writing chunks

                while bytes_sent < file_size:
                    chunk_number += 1

                    try:
                        logger.debug(f"[IMPORT] Reading chunk {chunk_number}/{total_chunks}")
                        chunk = f.read(chunk_size)
                        logger.debug(f"[IMPORT-DEBUG] Chunk {chunk_number} size: {len(chunk)} bytes")
                    except Exception as read_error:
                        logger.error(f"[IMPORT] Error reading chunk {chunk_number}: {read_error}")
                        logger.debug(f"[IMPORT] Exception details: {traceback.format_exc()}")
                        logger.debug(f"[IMPORT-DEBUG] File position at error: {f.tell()}")
                        raise

                    if not chunk:
                        logger.warning(f"[IMPORT] Unexpected end of file at chunk {chunk_number}/{total_chunks}")
                        logger.debug(f"[IMPORT-DEBUG] File position at EOF: {f.tell()}")
                        logger.debug(f"[IMPORT-DEBUG] Expected file size: {file_size}, bytes read so far: {bytes_sent}")
                        break

                    # Calculate progress
                    chunk_size_bytes = len(chunk)
                    bytes_sent += chunk_size_bytes
                    progress = int((bytes_sent / file_size) * 100)

                    # Log progress periodically to avoid flooding the logs
                    current_time = time.time()
                    if current_time - last_progress_log >= progress_interval or chunk_number == 1 or bytes_sent >= file_size:
                        elapsed = current_time - start_time
                        speed = bytes_sent / elapsed if elapsed > 0 else 0
                        speed_kb = speed / 1024
                        remaining = (file_size - bytes_sent) / speed if speed > 0 else 0

                        logger.info(f"[IMPORT] Sent chunk {chunk_number}/{total_chunks} ({progress}%)")
                        logger.debug(f"[IMPORT] Progress details: {bytes_sent}/{file_size} bytes, speed: {speed_kb:.2f} KB/s, est. remaining: {remaining:.1f}s")
                        logger.debug(f"[IMPORT-DEBUG] Failed chunks so far: {failed_chunks}, consecutive failures: {consecutive_failures}")

                        last_progress_log = current_time

                    # Determine if this is the last chunk
                    is_last = bytes_sent >= file_size

                    # Create chunk message - include both 'chunk' and 'data' fields for compatibility
                    # with different server versions
                    encoded_chunk = base64.b64encode(chunk).decode('utf-8')
                    chunk_data = {
                        'chunk_number': chunk_number,
                        'total_chunks': total_chunks,
                        'chunk_size': chunk_size_bytes,
                        'size': chunk_size_bytes,  # For compatibility with older server versions
                        'offset': offset,  # Important for server to know where to write the chunk
                        'progress': progress,
                        'bytes_sent': bytes_sent,
                        'total_bytes': file_size,
                        'chunk': encoded_chunk,
                        'data': encoded_chunk,  # Include both for compatibility
                        'is_last': is_last,
                        'timestamp': time.time(),
                        'client_id': self.client_id,  # Add client_id to chunk data for better tracking
                        'filename': os.path.basename(file_path)  # Add filename for better identification
                    }

                    # Update offset for next chunk
                    offset += chunk_size_bytes

                    # Log the size of the message
                    logger.debug(f"[IMPORT] Chunk {chunk_number} message size before encoding: ~{len(json.dumps(chunk_data, ensure_ascii=False)) / 1024:.1f} KB")
                    logger.debug(f"[IMPORT-DEBUG] Chunk {chunk_number} base64 encoded size: {len(encoded_chunk)} bytes")

                    chunk_message = NetworkMessage(
                        msg_type=NetworkMessage.TYPE_DB_CHUNK,
                        data=chunk_data,
                        client_id=self.client_id
                    )

                    # Send chunk with retries
                    sent_success = False
                    for retry in range(max_retries):
                        try:
                            logger.debug(f"[IMPORT] Sending chunk {chunk_number} (attempt {retry+1}/{max_retries})")
                            logger.debug(f"[IMPORT-DEBUG] Socket state before sending chunk: {self.socket.getsockname() if self.socket else 'No socket'}")
                            if send_message(self.socket, chunk_message):
                                logger.debug(f"[IMPORT] Chunk {chunk_number} sent successfully")
                                sent_success = True
                                chunks_sent += 1
                                consecutive_failures = 0
                                break
                            else:
                                logger.warning(f"[IMPORT] Failed to send chunk {chunk_number}, attempt {retry+1}/{max_retries}")
                                logger.debug(f"[IMPORT-DEBUG] Socket state after failed send: {self.socket.getsockname() if self.socket else 'No socket'}")
                                if retry < max_retries - 1:
                                    backoff_time = 0.5 * (2 ** retry)  # Exponential backoff
                                    logger.debug(f"[IMPORT] Waiting {backoff_time:.1f}s before retry")
                                    time.sleep(backoff_time)
                        except Exception as chunk_error:
                            logger.error(f"[IMPORT] Error sending chunk {chunk_number}, attempt {retry+1}: {chunk_error}")
                            logger.debug(f"[IMPORT-DEBUG] Exception details: {traceback.format_exc()}")
                            logger.debug(f"[IMPORT-DEBUG] Socket state during exception: {self.socket.getsockname() if self.socket else 'No socket'}")
                            if retry < max_retries - 1:
                                backoff_time = 0.5 * (2 ** retry)
                                logger.debug(f"[IMPORT] Waiting {backoff_time:.1f}s before retry")
                                time.sleep(backoff_time)

                    if not sent_success:
                        logger.error(f"[IMPORT] Failed to send chunk {chunk_number} after {max_retries} attempts")
                        logger.warning(f"[IMPORT] Continuing to next chunk despite error")
                        failed_chunks += 1
                        consecutive_failures += 1

                        if consecutive_failures >= 3:
                            logger.error(f"[IMPORT] Too many consecutive failures ({consecutive_failures}). Aborting import.")
                            logger.debug(f"[IMPORT-DEBUG] Socket state after multiple failures: {self.socket.getsockname() if self.socket else 'No socket'}")
                            raise Exception(f"Too many consecutive failures ({consecutive_failures}) sending chunks. Aborting import.")

                # End of file reached
                logger.info(f"[IMPORT] Finished sending all chunks ({chunks_sent}/{total_chunks} chunks sent)")

                # Calculate statistics
                total_time = time.time() - start_time
                avg_speed = bytes_sent / total_time if total_time > 0 else 0
                avg_speed_kb = avg_speed / 1024

                logger.info(f"[IMPORT] Total time: {total_time:.2f} seconds")
                logger.info(f"[IMPORT] Average speed: {avg_speed_kb:.2f} KB/s")

                # Send completion message
                logger.info("[IMPORT] Sending completion message to server")

                complete_data = {
                    'file_path': file_path,
                    'filename': os.path.basename(file_path),
                    'file_size': file_size,
                    'chunks_sent': chunks_sent,
                    'total_chunks': total_chunks,
                    'bytes_sent': bytes_sent,
                    'elapsed_time': total_time,
                    'average_speed': avg_speed,
                    'average_speed_kb': avg_speed_kb,
                    'timestamp': time.time(),
                    'client_id': self.client_id,
                    'status': 'complete',
                    'failed_chunks': failed_chunks
                }

                complete_message = NetworkMessage(
                    msg_type=NetworkMessage.TYPE_DB_COMPLETE,
                    data=complete_data,
                    client_id=self.client_id
                )

                # Retry sending completion message with longer timeouts
                logger.debug("[IMPORT] Attempting to send completion message")
                completion_success = False

                for retry in range(max_retries):
                    try:
                        logger.debug(f"[IMPORT] Sending completion message (attempt {retry+1}/{max_retries})")
                        if send_message(self.socket, complete_message):
                            logger.info(f"[IMPORT] Database file transfer completed: {os.path.basename(file_path)}")
                            logger.debug("[IMPORT] Completion message sent successfully")
                            completion_success = True
                            break
                        else:
                            if retry < max_retries - 1:
                                backoff_time = 1.0 * (2 ** retry)  # Longer exponential backoff for completion
                                logger.warning(f"[IMPORT] Failed to send completion message, retrying ({retry+1}/{max_retries}) after {backoff_time:.1f}s")
                                time.sleep(backoff_time)  # Exponential backoff
                            else:
                                logger.error(f"[IMPORT] Failed to send completion message after {max_retries} attempts")
                    except Exception as e:
                        if retry < max_retries - 1:
                            backoff_time = 1.0 * (2 ** retry)
                            logger.warning(f"[IMPORT] Error sending completion message: {e}, retrying ({retry+1}/{max_retries}) after {backoff_time:.1f}s")
                            time.sleep(backoff_time)
                        else:
                            logger.error(f"[IMPORT] Error sending completion message after {max_retries} attempts: {e}")
                            logger.debug(f"[IMPORT] Exception details: {traceback.format_exc()}")

                if not completion_success:
                    logger.error("[IMPORT] Failed to confirm import completion with server. The server may not recognize the import as complete.")
                else:
                    logger.info(f"[IMPORT] ===== DATABASE IMPORT COMPLETED SUCCESSFULLY =====")
                    logger.info(f"[IMPORT] File: {os.path.basename(file_path)}, Size: {format_file_size(file_size)}")
                    logger.info(f"[IMPORT] Time: {total_time:.2f} seconds, Speed: {avg_speed_kb:.2f} KB/s")

                # Mark import as completed regardless of completion message success
                # The import itself was completed, even if the notification failed
                self._backup_in_progress = False
                logger.debug(f"[IMPORT] Import process marked as not in progress")

        except Exception as e:
            # Make sure to reset import flag on error
            self._backup_in_progress = False
            logger.error(f"[IMPORT] ===== DATABASE IMPORT FAILED =====")
            logger.error(f"[IMPORT] Error sending database file: {e}")
            logger.debug(f"[IMPORT] Error details: {traceback.format_exc()}")

            # Try to send error message to server
            try:
                error_data = {
                    'error': str(e),
                    'file_path': file_path,
                    'timestamp': time.time(),
                    'client_id': self.client_id
                }
                error_message = NetworkMessage(
                    msg_type=NetworkMessage.TYPE_ERROR,
                    data=error_data,
                    client_id=self.client_id
                )
                send_result = send_message(self.socket, error_message)
                logger.debug(f"[IMPORT] Sent error message to server: {send_result}")
            except Exception as send_error:
                logger.error(f"[IMPORT] Failed to send error message to server: {send_error}")
                logger.debug(f"[IMPORT] Error message send exception: {traceback.format_exc()}")

            # Make sure to mark import as not in progress
            self._backup_in_progress = False
            logger.info("[IMPORT] Import process marked as not in progress due to error")

    def handle_mega_upload_request(self, request_data):
        """Handle a request to upload a file to MEGA cloud storage"""
        try:
            logger.info(f"[MEGA] ===== HANDLING MEGA UPLOAD REQUEST =====")
            logger.info(f"[MEGA] Handling MEGA upload request: {request_data}")

            request_id = request_data.get('request_id')
            direct_upload = request_data.get('direct_upload', False)

            # Unified method will handle MEGA client initialization

            # Determine file to upload
            if direct_upload:
                # Use the currently connected database file
                if not self.db_connector or not self.db_connector.db_path:
                    logger.error("[MEGA] No database connected for direct upload")
                    self.send_mega_upload_result({
                        'success': False,
                        'error': 'No database connected',
                        'request_id': request_id
                    })
                    return

                file_path = self.db_connector.db_path
                logger.info(f"[MEGA] Using connected database for direct upload: {file_path}")
            else:
                # Use the file path specified in the request
                file_path = request_data.get('file_path')
                if not file_path:
                    logger.error("[MEGA] No file path provided in upload request")
                    self.send_mega_upload_result({
                        'success': False,
                        'error': 'No file path provided',
                        'request_id': request_id
                    })
                    return

            if not os.path.exists(file_path):
                logger.error(f"[MEGA] File not found: {file_path}")
                self.send_mega_upload_result({
                    'success': False,
                    'error': f'File not found: {file_path}',
                    'request_id': request_id
                })
                return

            # Get file info for logging
            file_size = os.path.getsize(file_path)
            file_size_formatted = format_file_size(file_size)
            logger.info(f"[MEGA] Preparing to upload file: {file_path} ({file_size_formatted})")

            # Send acknowledgment
            logger.info("[MEGA] Sending acknowledgment to server")
            self.send_mega_upload_ack({
                'file_path': file_path,
                'request_id': request_id,
                'timestamp': time.time()
            })

            # Update progress (0%)
            logger.info("[MEGA] Sending progress update (0%)")
            self.send_mega_upload_progress({
                'file_path': file_path,
                'request_id': request_id,
                'progress': 0,
                'status': 'starting',
                'timestamp': time.time()
            })

            # Perform unified upload to MEGA
            logger.info(f"[MEGA] Starting unified upload to MEGA: {file_path}")

            # Send progress update (10%)
            self.send_mega_upload_progress({
                'file_path': file_path,
                'request_id': request_id,
                'progress': 10,
                'status': 'uploading',
                'timestamp': time.time()
            })

            # Use unified upload method
            def progress_callback(progress):
                logger.info(f"[MEGA] Server-triggered upload progress: {progress}%")
                self.send_mega_upload_progress({
                    'file_path': file_path,
                    'request_id': request_id,
                    'progress': progress,
                    'status': 'uploading',
                    'timestamp': time.time()
                })

            result = self.unified_mega_upload(file_path, progress_callback, request_id)
            
            # Send final result
            self.send_mega_upload_result(result)
            logger.info(f"[MEGA] ===== MEGA UPLOAD COMPLETED =====")

        except Exception as e:
            logger.error(f"[MEGA] Error handling MEGA upload request: {e}")
            logger.error(traceback.format_exc())

            # Send error result
            self.send_mega_upload_result({
                'success': False,
                'error': str(e),
                'request_id': request_data.get('request_id', 'unknown')
            })

    def send_mega_upload_ack(self, data):
        """Send a MEGA upload acknowledgment message"""
        try:
            if not self.socket:
                logger.error("[MEGA] Cannot send acknowledgment, not connected")
                return False

            message = NetworkMessage(
                NetworkMessage.TYPE_MEGA_UPLOAD_ACK,
                data,
                self.client_id
            )

            return send_message(self.socket, message)
        except Exception as e:
            logger.error(f"[MEGA] Error sending acknowledgment: {e}")
            return False

    def send_mega_upload_progress(self, data):
        """Send a MEGA upload progress message"""
        try:
            if not self.socket:
                logger.error("[MEGA] Cannot send progress, not connected")
                return False

            message = NetworkMessage(
                NetworkMessage.TYPE_MEGA_UPLOAD_PROGRESS,
                data,
                self.client_id
            )

            return send_message(self.socket, message)
        except Exception as e:
            logger.error(f"[MEGA] Error sending progress: {e}")
            return False

    def send_mega_upload_result(self, data):
        """Send MEGA upload result to server"""
        if not self.connected or not self.socket:
            logger.error("[MEGA] Cannot send upload result: not connected to server")
            return

        try:
            # Create message with result data
            message = NetworkMessage(
                msg_type=NetworkMessage.TYPE_MEGA_UPLOAD_RESULT,
                data=data,
                client_id=self.client_id
            )

            # Send message
            if send_message(self.socket, message):
                logger.info(f"[MEGA] Upload result sent successfully: {data}")
            else:
                logger.error("[MEGA] Failed to send upload result")
        except Exception as e:
            logger.error(f"[MEGA] Error sending upload result: {e}")
            logger.error(traceback.format_exc())

    def handle_gdrive_upload_request(self, request_data):
        """Handle a request to upload a file to Google Drive cloud storage"""
        try:
            logger.info(f"[GDRIVE] ===== HANDLING GDRIVE UPLOAD REQUEST =====")
            logger.info(f"[GDRIVE] Handling Google Drive upload request: {request_data}")

            request_id = request_data.get('request_id')
            direct_upload = request_data.get('direct_upload', False)

            # Determine file to upload
            if direct_upload:
                # Use the currently connected database file
                if not self.db_connector or not self.db_connector.db_path:
                    logger.error("[GDRIVE] No database connected for direct upload")
                    self.send_gdrive_upload_result({
                        'success': False,
                        'error': 'No database connected',
                        'request_id': request_id
                    })
                    return

                file_path = self.db_connector.db_path
                logger.info(f"[GDRIVE] Using connected database for direct upload: {file_path}")
            else:
                # Use the file path specified in the request
                file_path = request_data.get('file_path')
                if not file_path:
                    logger.error("[GDRIVE] No file path provided in upload request")
                    self.send_gdrive_upload_result({
                        'success': False,
                        'error': 'No file path provided',
                        'request_id': request_id
                    })
                    return

            if not os.path.exists(file_path):
                logger.error(f"[GDRIVE] File not found: {file_path}")
                self.send_gdrive_upload_result({
                    'success': False,
                    'error': f'File not found: {file_path}',
                    'request_id': request_id
                })
                return

            # Get file info for logging
            file_size = os.path.getsize(file_path)
            file_size_formatted = format_file_size(file_size)
            logger.info(f"[GDRIVE] Preparing to upload file: {file_path} ({file_size_formatted})")

            # Send acknowledgment
            logger.info("[GDRIVE] Sending acknowledgment to server")
            self.send_gdrive_upload_ack({
                'file_path': file_path,
                'request_id': request_id,
                'timestamp': time.time()
            })

            # Update progress (0%)
            logger.info("[GDRIVE] Sending progress update (0%)")
            self.send_gdrive_upload_progress({
                'file_path': file_path,
                'request_id': request_id,
                'progress': 0,
                'status': 'starting',
                'timestamp': time.time()
            })

            # Perform unified upload to Google Drive
            logger.info(f"[GDRIVE] Starting unified upload to Google Drive: {file_path}")

            # Send progress update (10%)
            self.send_gdrive_upload_progress({
                'file_path': file_path,
                'request_id': request_id,
                'progress': 10,
                'status': 'uploading',
                'timestamp': time.time()
            })

            # Use unified upload method
            def progress_callback(progress):
                logger.info(f"[GDRIVE] Server-triggered upload progress: {progress}%")
                self.send_gdrive_upload_progress({
                    'file_path': file_path,
                    'request_id': request_id,
                    'progress': progress,
                    'status': 'uploading',
                    'timestamp': time.time()
                })

            result = self.unified_gdrive_upload(file_path, progress_callback, request_id)
            
            # Send final result
            self.send_gdrive_upload_result(result)

        except Exception as e:
            logger.error(f"[GDRIVE] Error handling Google Drive upload request: {e}")
            logger.error(traceback.format_exc())
            self.send_gdrive_upload_result({
                'success': False,
                'error': str(e),
                'request_id': request_data.get('request_id')
            })

    def send_gdrive_upload_ack(self, data):
        """Send Google Drive upload acknowledgment to server"""
        if not self.connected or not self.socket:
            logger.error("[GDRIVE] Cannot send upload acknowledgment: not connected to server")
            return

        try:
            # Create message with acknowledgment data
            message = NetworkMessage(
                msg_type='gdrive_upload_ack',
                data=data,
                client_id=self.client_id
            )

            # Send message
            if send_message(self.socket, message):
                logger.info(f"[GDRIVE] Upload acknowledgment sent successfully: {data}")
            else:
                logger.error("[GDRIVE] Failed to send upload acknowledgment")
        except Exception as e:
            logger.error(f"[GDRIVE] Error sending upload acknowledgment: {e}")
            logger.error(traceback.format_exc())

    def send_gdrive_upload_progress(self, data):
        """Send Google Drive upload progress to server"""
        if not self.connected or not self.socket:
            logger.error("[GDRIVE] Cannot send upload progress: not connected to server")
            return

        try:
            # Create message with progress data
            message = NetworkMessage(
                msg_type='gdrive_upload_progress',
                data=data,
                client_id=self.client_id
            )

            # Send message
            if send_message(self.socket, message):
                logger.debug(f"[GDRIVE] Upload progress sent successfully: {data}")
            else:
                logger.error("[GDRIVE] Failed to send upload progress")
        except Exception as e:
            logger.error(f"[GDRIVE] Error sending upload progress: {e}")
            logger.error(traceback.format_exc())

    def send_gdrive_upload_result(self, data):
        """Send Google Drive upload result to server"""
        if not self.connected or not self.socket:
            logger.error("[GDRIVE] Cannot send upload result: not connected to server")
            return

        try:
            # Create message with result data
            message = NetworkMessage(
                msg_type='gdrive_upload_result',
                data=data,
                client_id=self.client_id
            )

            # Send message
            if send_message(self.socket, message):
                logger.info(f"[GDRIVE] Upload result sent successfully: {data}")
            else:
                logger.error("[GDRIVE] Failed to send upload result")
        except Exception as e:
            logger.error(f"[GDRIVE] Error sending upload result: {e}")
            logger.error(traceback.format_exc())

    def unified_gdrive_upload(self, file_path, progress_callback=None, request_id=None):
        """Unified Google Drive upload method used by both server-triggered and scheduled uploads"""
        try:
            logger.info(f"[GDRIVE] Starting unified Google Drive upload: {file_path}")
            
            # Get credentials file from config
            config = self.get_config()
            gdrive_config = config.get('gdrive', {})
            
            # Try different credential file options
            credentials_file = None
            
            # First try credentials_file from config
            if 'credentials_file' in gdrive_config:
                cred_file = gdrive_config['credentials_file']
                if os.path.exists(cred_file):
                    credentials_file = cred_file
                    logger.info(f"[GDRIVE] Using credentials from config: {credentials_file}")
            
            # If not found, try token.json in current directory
            if not credentials_file:
                token_file = os.path.join(BASE_DIR, "token.json")
                if os.path.exists(token_file):
                    credentials_file = token_file
                    logger.info(f"[GDRIVE] Using token file: {credentials_file}")
            
            # If still not found, try to find client_secret.json + token.json combination
            if not credentials_file:
                client_secret = os.path.join(BASE_DIR, "client_secret.json")
                token_file = os.path.join(BASE_DIR, "token.json")
                if os.path.exists(client_secret) and os.path.exists(token_file):
                    credentials_file = token_file  # Use token file directly
                    logger.info(f"[GDRIVE] Using token file with client secret: {credentials_file}")
            
            if not credentials_file:
                logger.error("[GDRIVE] No valid credentials file found for Google Drive")
                return {
                    'success': False,
                    'error': 'No valid credentials file found for Google Drive',
                    'request_id': request_id
                }
            
            # Import and initialize Google Drive client
            try:
                from gdrive_client_simple import GDriveClient
                gdrive_client = GDriveClient(self.client_id, credentials_file)
                logger.info("[GDRIVE] Google Drive client initialized")
            except ImportError as e:
                logger.error(f"[GDRIVE] Failed to import Google Drive client: {e}")
                return {
                    'success': False,
                    'error': f'Failed to import Google Drive client: {str(e)}',
                    'request_id': request_id
                }
            except Exception as e:
                logger.error(f"[GDRIVE] Failed to initialize Google Drive client: {e}")
                return {
                    'success': False,
                    'error': f'Failed to initialize Google Drive client: {str(e)}',
                    'request_id': request_id
                }
            
            # Perform upload with unified Google Drive client (includes compression and cleanup)
            result = gdrive_client.upload_file(file_path, progress_callback)
            
            # Add request_id to result
            if request_id:
                result['request_id'] = request_id
            
            if result.get('success', False):
                logger.info(f"[GDRIVE] Unified Google Drive upload successful")
                logger.info(f"[GDRIVE] File ID: {result.get('file_id', 'N/A')}")
                logger.info(f"[GDRIVE] Duration: {result.get('upload_duration', 'N/A')}")
                logger.info(f"[GDRIVE] View Link: {result.get('web_view_link', 'N/A')}")
            else:
                logger.error(f"[GDRIVE] Unified Google Drive upload failed: {result.get('error', 'Unknown error')}")
                
            return result
                
        except Exception as e:
            logger.error(f"[GDRIVE] Error in unified Google Drive upload: {e}")
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': str(e),
                'request_id': request_id
            }

    def unified_mega_upload(self, file_path, progress_callback=None, request_id=None):
        """Unified MEGA upload method used by both server-triggered and scheduled uploads
        
        This method includes:
        1. Database compression to ZIP format 
        2. Upload to MEGA cloud storage
        3. Automatic cleanup of temporary ZIP file
        4. Consistent folder structure
        """
        compressed_file_path = None
        temp_dir = None
        upload_start_time = time.time()
        
        try:
            logger.info(f"[MEGA] Starting unified MEGA upload: {file_path}")
            
            # Initialize MEGA client if needed
            if not self.mega_client or not getattr(self.mega_client, 'logged_in', False):
                logger.warning("[MEGA] MEGA client not logged in, trying to initialize...")
                try:
                    from mega_client import MegaClient
                    self.mega_client = MegaClient(self.client_id)
                    if not getattr(self.mega_client, 'logged_in', False):
                        raise Exception("Failed to initialize MEGA client")
                    logger.info("[MEGA] MEGA client re-initialized successfully")
                except Exception as e:
                    logger.error(f"[MEGA] Failed to initialize MEGA client: {e}")
                    return {
                        'success': False,
                        'error': f'Failed to initialize MEGA client: {str(e)}',
                        'request_id': request_id
                    }
            
            # Validate file exists
            if not os.path.exists(file_path):
                logger.error(f"[MEGA] File not found: {file_path}")
                return {
                    'success': False,
                    'error': f'File not found: {file_path}',
                    'request_id': request_id
                }
            
            # Get original file size for logging
            original_size = os.path.getsize(file_path)
            logger.info(f"[MEGA] Original file size: {original_size / (1024*1024):.2f} MB")
            
            # Compress file to ZIP format (same as Google Drive implementation)
            logger.info("[MEGA] Compressing database file before upload...")
            compressed_file_path = self._compress_file_for_upload(file_path)
            temp_dir = os.path.dirname(compressed_file_path)
            
            # Get compressed file size
            compressed_size = os.path.getsize(compressed_file_path)
            compression_ratio = (1 - (compressed_size / original_size)) * 100 if original_size > 0 else 0
            logger.info(f"[MEGA] Compressed file size: {compressed_size / (1024*1024):.2f} MB")
            logger.info(f"[MEGA] Compression ratio: {compression_ratio:.2f}%")
            
            # Upload compressed file to MEGA
            if progress_callback:
                progress_callback(10)  # Compression complete
            
            result = self.mega_client.upload_file_to_mega(compressed_file_path)
            
            # Calculate upload duration
            upload_duration = time.time() - upload_start_time
            
            # Add metadata to result
            if request_id:
                result['request_id'] = request_id
            result['upload_duration'] = upload_duration
            result['original_file_size'] = original_size
            result['compressed_file_size'] = compressed_size
            result['compression_ratio'] = compression_ratio
            
            if result.get('success', False):
                logger.info(f"[MEGA] Unified MEGA upload successful")
                logger.info(f"[MEGA] File: {result.get('file_name', 'N/A')}")
                logger.info(f"[MEGA] Duration: {upload_duration:.2f} seconds")
                logger.info(f"[MEGA] Link: {result.get('link', 'N/A')}")
                logger.info(f"[MEGA] Original size: {original_size / (1024*1024):.2f} MB")
                logger.info(f"[MEGA] Compressed size: {compressed_size / (1024*1024):.2f} MB")
                if progress_callback:
                    progress_callback(100)  # Upload complete
            else:
                logger.error(f"[MEGA] Unified MEGA upload failed: {result.get('error', 'Unknown error')}")
                
            return result
                
        except Exception as e:
            upload_duration = time.time() - upload_start_time
            logger.error(f"[MEGA] Error in unified MEGA upload: {e}")
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': str(e),
                'request_id': request_id,
                'upload_duration': upload_duration
            }
        finally:
            # Always clean up temporary files
            if compressed_file_path and os.path.exists(compressed_file_path):
                try:
                    logger.info(f"[MEGA] Cleaning up temporary compressed file: {compressed_file_path}")
                    os.remove(compressed_file_path)
                except Exception as cleanup_error:
                    logger.warning(f"[MEGA] Error cleaning up compressed file: {cleanup_error}")

            if temp_dir and os.path.exists(temp_dir):
                try:
                    logger.info(f"[MEGA] Cleaning up temporary directory: {temp_dir}")
                    import shutil
                    shutil.rmtree(temp_dir)
                except Exception as cleanup_error:
                    logger.warning(f"[MEGA] Error cleaning up temporary directory: {cleanup_error}")

    def _compress_file_for_upload(self, file_path):
        """Compress a file using ZIP format for upload (unified compression method)
        
        Args:
            file_path: Path to the file to compress
            
        Returns:
            str: Path to the compressed file
        """
        import tempfile
        import zipfile
        
        logger.info(f"[UNIFIED] Compressing file: {file_path}")

        try:
            # Create a temporary directory for the compressed file
            temp_dir = tempfile.mkdtemp(prefix="ifess_upload_")
            logger.info(f"[UNIFIED] Created temporary directory: {temp_dir}")

            # Get the original file name and create a zip file name
            original_filename = os.path.basename(file_path)
            zip_filename = f"{os.path.splitext(original_filename)[0]}.zip"
            zip_path = os.path.join(temp_dir, zip_filename)

            logger.info(f"[UNIFIED] Creating ZIP file: {zip_path}")

            # Create the ZIP file
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Add the file to the ZIP with its original name
                logger.info(f"[UNIFIED] Adding {original_filename} to ZIP")
                zipf.write(file_path, arcname=original_filename)

            # Check if the ZIP file was created successfully
            if not os.path.exists(zip_path):
                raise Exception(f"Failed to create ZIP file: {zip_path}")

            # Get file sizes for logging
            original_size = os.path.getsize(file_path)
            compressed_size = os.path.getsize(zip_path)
            compression_ratio = (1 - (compressed_size / original_size)) * 100 if original_size > 0 else 0

            logger.info(f"[UNIFIED] Compression complete:")
            logger.info(f"[UNIFIED]   Original size: {original_size / (1024*1024):.2f} MB")
            logger.info(f"[UNIFIED]   Compressed size: {compressed_size / (1024*1024):.2f} MB")
            logger.info(f"[UNIFIED]   Compression ratio: {compression_ratio:.2f}%")

            return zip_path

        except Exception as e:
            logger.error(f"[UNIFIED] Error compressing file: {e}")
            logger.error(traceback.format_exc())
            raise

    def setup_scheduled_upload(self):
        """Setup scheduled upload functionality"""
        try:
            # Get configuration - ensure we get a dictionary
            config = self.get_config()

            # Validate that config is a dictionary
            if not isinstance(config, dict):
                logger.error(f"[SCHEDULED] Invalid configuration type: {type(config)}. Expected dict.")
                return

            schedule_config = config.get('scheduled_upload', {})

            # Validate schedule_config is a dictionary
            if not isinstance(schedule_config, dict):
                logger.error(f"[SCHEDULED] Invalid scheduled_upload config type: {type(schedule_config)}. Expected dict.")
                return

            if not schedule_config.get('enabled', False):
                logger.info("[SCHEDULED] Scheduled upload is disabled")
                return

            # Get scheduled time with validation
            hour = schedule_config.get('hour', 2)
            minute = schedule_config.get('minute', 0)
            service = schedule_config.get('service', 'gdrive')

            # Validate time values
            try:
                hour = int(hour)
                minute = int(minute)
                if not (0 <= hour <= 23):
                    logger.error(f"[SCHEDULED] Invalid hour value: {hour}. Using default 2.")
                    hour = 2
                if not (0 <= minute <= 59):
                    logger.error(f"[SCHEDULED] Invalid minute value: {minute}. Using default 0.")
                    minute = 0
            except (ValueError, TypeError) as e:
                logger.error(f"[SCHEDULED] Error parsing time values: {e}. Using defaults.")
                hour, minute = 2, 0

            # Format time string
            time_str = f"{hour:02d}:{minute:02d}"

            logger.info(f"[SCHEDULED] Setting up scheduled upload for {time_str} using {service}")

            # Clear any existing scheduled jobs
            schedule.clear()

            # Schedule the upload
            schedule.every().day.at(time_str).do(self.perform_scheduled_upload)

            # Start scheduler thread
            if not self.scheduler_running:
                self.scheduler_running = True
                self.scheduler_thread = threading.Thread(target=self.run_scheduler, daemon=True)
                self.scheduler_thread.start()
                logger.info("[SCHEDULED] Scheduler thread started")
            else:
                logger.info("[SCHEDULED] Scheduler already running")

        except Exception as e:
            logger.error(f"[SCHEDULED] Error setting up scheduled upload: {e}")
            logger.error(traceback.format_exc())
    
    def run_scheduler(self):
        """Run the scheduler in a background thread"""
        logger.info("[SCHEDULED] Scheduler thread started")
        while self.scheduler_running:  # Only check scheduler_running, not self.running - scheduler should be independent
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            except Exception as e:
                logger.error(f"[SCHEDULED] Error in scheduler thread: {e}")
                logger.error(traceback.format_exc())
                time.sleep(60)  # Continue running even if there's an error
        logger.info("[SCHEDULED] Scheduler thread stopped")
    
    def perform_scheduled_upload(self):
        """Perform scheduled upload"""
        if self.upload_in_progress:
            logger.warning("[SCHEDULED] Upload already in progress, skipping scheduled upload")
            return
            
        if not self.db_connector or not self.db_connector.db_path:
            logger.error("[SCHEDULED] No database configured for scheduled upload")
            return
            
        try:
            self.upload_in_progress = True
            logger.info("[SCHEDULED] Starting scheduled database upload")
            
            # Get configuration
            config = self.get_config()
            schedule_config = config.get('scheduled_upload', {})
            service = schedule_config.get('service', 'gdrive')
            
            db_path = self.db_connector.db_path
            if not os.path.exists(db_path):
                logger.error(f"[SCHEDULED] Database file not found: {db_path}")
                return
                
            logger.info(f"[SCHEDULED] Uploading database file: {db_path}")
            logger.info(f"[SCHEDULED] Using service: {service}")
            
            success = False
            
            if service == 'gdrive':
                success = self.perform_scheduled_gdrive_upload(db_path)
            elif service == 'mega':
                success = self.perform_scheduled_mega_upload(db_path)
            else:
                logger.error(f"[SCHEDULED] Unknown upload service: {service}")
                return
                
            if success:
                logger.info("[SCHEDULED] Scheduled upload completed successfully")
            else:
                logger.error("[SCHEDULED] Scheduled upload failed")
                
        except Exception as e:
            logger.error(f"[SCHEDULED] Error during scheduled upload: {e}")
            logger.error(traceback.format_exc())
        finally:
            self.upload_in_progress = False
    
    def perform_scheduled_gdrive_upload(self, db_path):
        """Perform scheduled Google Drive upload using unified upload method"""
        try:
            logger.info("[SCHEDULED] Starting Google Drive upload")
            
            # Use unified upload method which includes compression, upload, and cleanup
            def progress_callback(progress):
                logger.info(f"[SCHEDULED] Google Drive upload progress: {progress}%")
            
            result = self.unified_gdrive_upload(db_path, progress_callback)
            
            if result.get('success', False):
                logger.info(f"[SCHEDULED] Google Drive upload successful")
                logger.info(f"[SCHEDULED] File ID: {result.get('file_id', 'N/A')}")
                logger.info(f"[SCHEDULED] Duration: {result.get('upload_duration', 'N/A')}")
                logger.info(f"[SCHEDULED] View Link: {result.get('web_view_link', 'N/A')}")
                return True
            else:
                logger.error(f"[SCHEDULED] Google Drive upload failed: {result.get('error', 'Unknown error')}")
                return False
                
        except Exception as e:
            logger.error(f"[SCHEDULED] Error in Google Drive upload: {e}")
            logger.error(traceback.format_exc())
            return False
    
    def perform_scheduled_mega_upload(self, db_path):
        """Perform scheduled MEGA upload using unified upload method"""
        try:
            logger.info("[SCHEDULED] Starting MEGA upload")
            
            # Use unified upload method which includes compression, upload, and cleanup
            def progress_callback(progress):
                logger.info(f"[SCHEDULED] MEGA upload progress: {progress}%")
            
            result = self.unified_mega_upload(db_path, progress_callback)
            
            if result.get('success', False):
                logger.info(f"[SCHEDULED] MEGA upload successful")
                logger.info(f"[SCHEDULED] File: {result.get('file_name', 'N/A')}")
                logger.info(f"[SCHEDULED] Duration: {result.get('duration', 'N/A')}")
                logger.info(f"[SCHEDULED] Link: {result.get('link', 'N/A')}")
                return True
            else:
                logger.error(f"[SCHEDULED] MEGA upload failed: {result.get('error', 'Unknown error')}")
                return False
                
        except Exception as e:
            logger.error(f"[SCHEDULED] Error in MEGA upload: {e}")
            logger.error(traceback.format_exc())
            return False

    def run(self):
        """Run the client application"""
        logger.info("Client running in hidden mode")

        # Log connection status
        if self.connected:
            logger.info(f"Connected to server: {self.server_address}:{self.server_port}")
        else:
            logger.info(f"Not connected to server, auto-reconnect is {self.reconnect_thread is not None and self.reconnect_thread.is_alive()}")

        # Log database status
        if self.db_connector:
            logger.info(f"Database configured: {self.db_connector.db_path}")
        else:
            logger.warning("No database configured")

        try:
            # Keep the main thread alive
            counter = 0
            while self.running:
                # Periodically check connection status
                counter += 1
                if counter % 60 == 0:  # Every 60 seconds
                    if self.connected:
                        logger.info(f"Client is running and connected to server: {self.server_address}:{self.server_port}")
                    else:
                        logger.info(f"Client is running but not connected to server")

                    # Check if reconnect thread is alive
                    if not self.connected and not self.is_connecting and (self.reconnect_thread is None or not self.reconnect_thread.is_alive()):
                        logger.warning("Auto-reconnect thread appears to be dead, restarting...")
                        self.start_auto_reconnect()

                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt, shutting down...")
        except Exception as e:
            logger.error(f"Error in main thread: {e}")
            logger.error(traceback.format_exc())

            # Keep the application running even if there's an error
            logger.info("Attempting to continue running despite error...")
            try:
                while self.running:
                    time.sleep(5)
            except:
                pass
        finally:
            logger.info("Shutting down client...")
            self.running = False
            
            # Stop scheduler
            if self.scheduler_running:
                logger.info("[SCHEDULED] Stopping scheduler...")
                self.scheduler_running = False
                schedule.clear()
                if self.scheduler_thread and self.scheduler_thread.is_alive():
                    self.scheduler_thread.join(timeout=5)
                logger.info("[SCHEDULED] Scheduler stopped")
            
            if self.connected and self.socket:
                self.disconnect_from_server()
            logger.info("Client stopped")

def main():
    """Main function"""
    try:
        # Additional PyInstaller safety checks
        if getattr(sys, 'frozen', False):
            # Running as compiled executable - additional safety measures
            try:
                # Ensure we have a proper working directory
                if not os.path.exists(BASE_DIR):
                    os.makedirs(BASE_DIR, exist_ok=True)
                os.chdir(BASE_DIR)
            except Exception as e:
                # Log to a fallback location if possible
                try:
                    with open('ifess_startup_error.log', 'a') as f:
                        f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - PyInstaller directory error: {e}\n")
                except:
                    pass
        
        # Create log directory if it doesn't exist
        log_dir = os.path.dirname(LOG_FILE)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)

        # Parse command line arguments
        debug_mode = False
        force_mode = False

        for arg in sys.argv[1:]:
            if arg == '--debug':
                debug_mode = True
            elif arg == '--force':
                force_mode = True

        # Log startup information
        logger.info("===== IFESS HIDDEN CLIENT STARTING =====")
        logger.info(f"Start time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"Python version: {sys.version}")
        logger.info(f"Running from: {os.path.abspath(__file__)}")
        logger.info(f"Current directory: {os.getcwd()}")
        logger.info(f"Log file: {os.path.abspath(LOG_FILE)}")
        logger.info(f"Debug mode: {debug_mode}")
        logger.info(f"Force mode: {force_mode}")

        # Hide console window only if not in debug mode
        if not debug_mode:
            logger.info("Hiding console window...")
            hide_console_window()

            # Redirect stdout and stderr to log file to prevent any console output
            logger.info("Redirecting stdout and stderr to devnull...")
            sys.stdout = open(os.devnull, 'w')
            sys.stderr = open(os.devnull, 'w')
        else:
            logger.info("Running in debug mode, console window will remain visible")

            # Set up console handler for logger
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.DEBUG)
            console_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)

        # Create mutex to ensure only one instance runs
        if not force_mode and not create_mutex():
            logger.warning("Another instance is already running, exiting")
            time.sleep(2)  # Give time for log to be written
            return

        logger.info("Starting IFESS Hidden Client")
        logger.info(f"Config file path: {os.path.abspath(CONFIG_FILE)}")
        logger.info(f"Server port: {DEFAULT_PORT}")

        # Check if config file exists
        if not os.path.exists(CONFIG_FILE):
            logger.warning(f"Config file not found: {os.path.abspath(CONFIG_FILE)}")
            logger.info("Creating default config file...")

            # Create default config
            client_id = f"client_{uuid.uuid4().hex[:8]}"
            display_name = f"FDB-Client-{platform.node()}"

            default_config = {
                "server_address": "localhost",
                "server_port": DEFAULT_PORT,
                "reconnect_interval": 5,
                "client_id": client_id,
                "display_name": display_name,
                "database": {
                    "path": "",
                    "username": "SYSDBA",
                    "password": "masterkey",
                    "isql_path": "",
                    "use_localhost": True
                }
            }

            try:
                with open(CONFIG_FILE, 'w') as f:
                    json.dump(default_config, f, indent=2)
                logger.info(f"Default config file created: {os.path.abspath(CONFIG_FILE)}")
            except Exception as e:
                logger.error(f"Failed to create default config file: {e}")
                logger.error(traceback.format_exc())

        # Initialize and run the application
        try:
            logger.info("Initializing ClientApp...")
            app = ClientApp()
            logger.info("ClientApp initialized, starting main loop...")
            app.run()
        except Exception as e:
            logger.critical(f"Critical error in ClientApp: {e}")
            logger.critical(traceback.format_exc())
            # Keep the application running for a while to allow logs to be written
            logger.info("Waiting 10 seconds before exiting due to critical error...")
            time.sleep(10)
    except Exception as e:
        logger.critical(f"Critical error in main: {e}")
        logger.critical(traceback.format_exc())

        # Keep the application running for a while to allow logs to be written
        logger.info("Waiting 10 seconds before exiting due to critical error...")
        time.sleep(10)

if __name__ == "__main__":
    main()
