# Augment Rules - IFESS Client System
# Dynamic learning journal for project-specific patterns and preferences
# Last Updated: 2025-01-27

## Coding Patterns

### File Organization Preferences
- **Timestamp**: 2025-01-25
- **Pattern**: User prefers staging_build/ directory for active development
- **Rationale**: Main development happens in staging_build/, with dist/ for final builds
- **Application**: Always check staging_build/ first when looking for current implementations

### Error Handling Approach
- **Timestamp**: 2025-01-25  
- **Pattern**: Comprehensive fallback chains for external dependencies
- **Example**: MEGA client with 6 fallback variants, GDrive client with multiple auth methods
- **Application**: Always implement graceful fallbacks for cloud service integrations

### Threading Architecture
- **Timestamp**: 2025-01-25
- **Pattern**: Daemon threads for background operations to prevent hanging on exit
- **Implementation**: `thread.daemon = True` for upload operations and background tasks
- **Application**: Use daemon threads for all non-critical background operations

### Configuration Management
- **Timestamp**: 2025-01-24
- **Pattern**: Multiple configuration files for different authentication methods
- **Example**: client_config.json (standard) + client_config_oauth_tokens.json (OAuth)
- **Application**: Maintain backward compatibility while adding new configuration options

## User Preferences

### Development Workflow
- **Timestamp**: 2025-01-27
- **Preference**: User wants component synchronization across three client variants
- **Details**: Changes in ifess_client_hidden should be reflected in ifess_gui and ifess_debug
- **Approach**: Implement shared modules to reduce code duplication

### Debug and Troubleshooting
- **Timestamp**: 2025-01-27
- **Preference**: User wants visible debug mode for hidden client troubleshooting
- **Requirement**: `ifess_client_hidden --debug` should show terminal with real-time output
- **Implementation**: Add command-line argument parsing with console window management

### Progress Tracking
- **Timestamp**: 2025-01-25
- **Preference**: User values detailed progress tracking for upload operations
- **Implementation**: Real-time progress callbacks with percentage updates (0% → 10% → real-time → 100%)
- **Application**: Always implement progress tracking for long-running operations

### Authentication Strategy
- **Timestamp**: 2025-01-24
- **Preference**: Token-based authentication preferred over client secrets
- **Rationale**: Existing token.json file contains valid OAuth credentials
- **Application**: Use token.json for Google Drive authentication instead of client_secrets.json

## Technical Decisions

### Connection Management
- **Timestamp**: 2025-01-23
- **Decision**: Exponential backoff with jitter for reconnection attempts
- **Specification**: 5s → 10s → 20s → 40s → 80s → 160s → 300s (max) with ±20% jitter
- **Rationale**: Prevents connection storms and improves server stability
- **Status**: Classes implemented, integration pending

### Cloud Storage Architecture
- **Timestamp**: 2025-01-25
- **Decision**: Multiple cloud storage providers with fallback mechanisms
- **Implementation**: MEGA (primary) + Google Drive (secondary) with robust error handling
- **Pattern**: Always implement multiple fallback options for critical external services

### Message Protocol Enhancement
- **Timestamp**: 2025-01-25
- **Decision**: Extend existing NetworkMessage protocol instead of creating new one
- **Added Types**: gdrive_upload_request, gdrive_upload_ack, gdrive_upload_progress, gdrive_upload_result
- **Rationale**: Maintains compatibility while adding new functionality

### Folder Structure Convention
- **Timestamp**: 2025-01-24
- **Decision**: Standardized Google Drive folder structure
- **Format**: `Backup_PTRJ/Auto_Backup_App/Ifess_Database/{client_id}`
- **Application**: Use this structure for all Google Drive upload operations

## Known Issues and Workarounds

### MEGA Client Python 3.10+ Compatibility
- **Timestamp**: 2025-01-23
- **Issue**: asyncio.coroutine deprecated in Python 3.10+
- **Workaround**: Multiple client variants with compatibility patches
- **Status**: Resolved with fallback chain implementation
- **Prevention**: Always test cloud clients with target Python version

### Component Feature Inconsistency
- **Timestamp**: 2025-01-27
- **Issue**: Google Drive integration only exists in hidden client
- **Impact**: GUI and debug clients lack upload functionality
- **Resolution**: Implement shared module architecture
- **Timeline**: Current development priority

### Debug Information Isolation
- **Timestamp**: 2025-01-27
- **Issue**: Debug client cannot access hidden client's internal debug data
- **Workaround**: File-based log monitoring (current)
- **Planned Solution**: Inter-process communication for real-time debug streaming
- **Architecture**: Named pipes or TCP sockets for Windows compatibility

## Tool Configurations

### PyInstaller Build Patterns
- **Timestamp**: 2025-01-25
- **Pattern**: Separate .spec files for each client variant
- **Files**: ifess_client_hidden.spec, ifess_client_debug.spec, ifess_config_gui.spec
- **Application**: Maintain separate build configurations for different deployment scenarios

### Batch File Conventions
- **Timestamp**: 2025-01-25
- **Pattern**: Descriptive batch file names with clear functionality
- **Examples**: build_client_hidden.bat, build_config_gui_only.bat, run_server.py
- **Application**: Use clear, descriptive names for all automation scripts

### Log File Management
- **Timestamp**: 2025-01-25
- **Configuration**: 5MB rotating log files with 3 backups
- **Pattern**: Separate log files for different components
- **Application**: Maintain consistent logging configuration across all components

## Evolution of Decisions

### Authentication Method Evolution
- **2025-01-20**: Started with client_secrets.json approach
- **2025-01-24**: Migrated to token.json for simplified authentication
- **Lesson**: Token-based auth is more reliable for automated systems
- **Application**: Prefer token-based authentication for future integrations

### Connection Strategy Evolution  
- **2025-01-20**: Simple fixed-interval reconnection (5 seconds)
- **2025-01-23**: Designed exponential backoff with jitter
- **2025-01-27**: Integration pending due to component synchronization priority
- **Lesson**: Connection resilience is critical but requires careful integration
- **Application**: Always implement progressive backoff for network operations

### Component Architecture Evolution
- **2025-01-20**: Three separate client implementations with duplicated code
- **2025-01-27**: Moving toward shared module architecture
- **Lesson**: Code duplication leads to maintenance issues and feature inconsistency
- **Application**: Extract common functionality to shared modules early in development

## User Communication Patterns

### Progress Updates
- **Preference**: User appreciates detailed progress tracking with specific percentages
- **Format**: "✅ COMPLETED", "🔄 IN PROGRESS", "⏳ PENDING", "⚠️ NEEDS ATTENTION"
- **Application**: Use consistent status indicators in all documentation

### Technical Documentation
- **Preference**: User values cross-references between documentation files
- **Pattern**: Include "Cross-References" section linking to related files
- **Application**: Always include references to related Knowledge Vault files

### Implementation Details
- **Preference**: User wants to see actual code snippets in documentation
- **Format**: Include relevant code blocks with explanatory comments
- **Application**: Provide concrete examples rather than abstract descriptions

## Project-Specific Conventions

### Client ID Usage
- **Pattern**: Use descriptive client IDs like "client_coba" for testing
- **Application**: Client ID becomes part of folder structure in cloud storage
- **Consideration**: Client ID should be unique and descriptive for identification

### Database Path Conventions
- **Pattern**: Use absolute paths for database files (D:/path/to/database.FDB)
- **Rationale**: Ensures consistent database access across different execution contexts
- **Application**: Always use absolute paths in configuration files

### Service Architecture
- **Pattern**: Hidden client runs as background service, GUI clients are on-demand
- **Implementation**: Mutex prevents multiple instances of hidden client
- **Application**: Maintain single-instance pattern for service components

## Recent Enhancements (2025-01-27)

### Connection Resilience Patterns
- **Timestamp**: 2025-01-27
- **Pattern**: Exponential backoff with jitter for connection retry logic
- **Implementation**: ConnectionManager class with progressive intervals (5s → 300s max, ±20% jitter)
- **Application**: Use for all network retry scenarios to prevent server overload

### Query Execution Reliability
- **Timestamp**: 2025-01-27
- **Pattern**: Multi-layer error handling with request ID tracking
- **Implementation**: 3-attempt retry with exponential backoff, comprehensive error isolation
- **Application**: Apply structured error handling to all critical operations

### Scheduled Operations Architecture
- **Timestamp**: 2025-01-27
- **Pattern**: Independent background scheduler with daemon threads
- **Implementation**: `schedule` library with configurable timing, dual cloud provider fallback
- **Application**: Use for all automated background tasks requiring high reliability

### Enhanced Logging Strategy
- **Timestamp**: 2025-01-27
- **Pattern**: Structured logging with operation correlation IDs
- **Example**: `[QUERY-{request_id}]`, `[SCHEDULER]`, `[CONNECTION]` prefixes
- **Application**: Implement consistent logging patterns for all major operations

This file captures the evolving patterns and preferences discovered during IFESS client development. It should be updated whenever new patterns emerge or decisions change.
