
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), wheel.vendored.packaging._manylinux (delayed, optional)
missing module named posix - imported by posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), os (conditional, optional), _pyrepl.unix_console (delayed, optional)
missing module named resource - imported by posix (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named _typeshed - imported by pkg_resources (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional)
missing module named jnius - imported by platformdirs.android (delayed, conditional, optional)
missing module named android - imported by platformdirs.android (delayed, conditional, optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), http.server (delayed, optional), netrc (delayed, conditional), getpass (delayed, optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level), _pyrepl.curses (optional)
missing module named termios - imported by tty (top-level), _pyrepl.pager (delayed, optional), getpass (optional), _pyrepl.unix_console (top-level), _pyrepl.fancy_termios (top-level), _pyrepl.unix_eventqueue (top-level)
missing module named fcntl - imported by subprocess (optional), _pyrepl.unix_console (top-level), firebirdsql.stream (optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), rlcompleter (optional), pdb (delayed, optional), site (delayed, optional), sqlite3.__main__ (delayed, conditional, optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named backports.zoneinfo - imported by backports (delayed, optional), firebirdsql.tz_utils (delayed, optional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional)
missing module named 'collections.abc' - imported by traceback (top-level), typing (top-level), inspect (top-level), logging (top-level), importlib.resources.readers (top-level), selectors (top-level), tracemalloc (top-level), schedule (top-level), pytz.lazy (optional), http.client (top-level), asyncio.base_events (top-level), asyncio.coroutines (top-level), googleapiclient.discovery (top-level), google.auth.jwt (optional), cachetools (top-level), urllib3._collections (optional), requests.compat (top-level), typing_extensions (top-level), socks (optional), setuptools (top-level), setuptools._vendor.jaraco.functools (top-level), setuptools._vendor.more_itertools.more (top-level), setuptools._vendor.more_itertools.recipes (top-level), setuptools._distutils.spawn (top-level), setuptools._reqs (top-level), setuptools.discovery (top-level), setuptools.dist (top-level), setuptools._distutils.core (top-level), setuptools._distutils.cmd (top-level), setuptools._distutils.dist (top-level), setuptools._distutils.fancy_getopt (top-level), configparser (top-level), setuptools.config.setupcfg (top-level), setuptools.config.expand (top-level), setuptools.config.pyprojecttoml (top-level), setuptools.config._apply_pyprojecttoml (top-level), tomllib._parser (top-level), setuptools._vendor.tomli._parser (top-level), pkg_resources (top-level), platformdirs.windows (conditional), setuptools.command.egg_info (top-level), setuptools.glob (top-level), setuptools.command._requirestxt (top-level), setuptools.command.bdist_wheel (top-level), wheel.cli.convert (top-level), wheel.cli.tags (top-level), _pyrepl.types (top-level), _pyrepl.readline (top-level), google.auth.pluggable (optional), google.auth.identity_pool (optional), pyparsing.core (top-level), pyparsing.results (top-level), markupsafe (top-level), uritemplate.variable (top-level), sqlite3.dbapi2 (top-level), firebirdsql.utils (optional), firebirdsql.aio.context (top-level), blinker.base (top-level), blinker._utilities (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named monotonic - imported by tenacity._utils (optional)
missing module named StringIO - imported by urllib3.packages.six (conditional), Crypto.Util.py3compat (conditional), six (conditional)
missing module named tornado - imported by tenacity (optional), tenacity.tornadoweb (top-level)
missing module named 'setuptools._distutils.msvc9compiler' - imported by cffi._shimmed_dist_utils (conditional, optional)
missing module named collections.Callable - imported by collections (optional), socks (optional), cffi.api (optional)
missing module named _dummy_thread - imported by cffi.lock (conditional, optional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional)
missing module named cStringIO - imported by cffi.ffiplatform (optional)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named imp - imported by cffi.verifier (conditional), cffi._imp_emulation (optional), Crypto.Util._raw_api (conditional)
missing module named jwt - imported by oauthlib.common (delayed), oauthlib.oauth2.rfc6749.clients.service_application (delayed)
runtime module named urllib3.packages.six.moves - imported by http.client (top-level), urllib3.util.response (top-level), urllib3.connectionpool (top-level), urllib3.packages.six.moves.urllib (top-level), urllib3.util.queue (top-level)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed)
missing module named bcrypt - imported by cryptography.hazmat.primitives.serialization.ssh (optional)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named 'OpenSSL.SSL' - imported by urllib3.contrib.pyopenssl (top-level)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level), google.auth.transport._mtls_helper (delayed), google.auth.transport.requests (delayed, optional), google.auth.identity_pool (delayed)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named 'jwt.algorithms' - imported by oauthlib.oauth1.rfc5849.signature (delayed, conditional)
missing module named 'requests.packages.urllib3' - imported by google.auth.transport.requests (top-level)
missing module named Queue - imported by urllib3.util.queue (conditional), fdb.ibase (conditional)
missing module named 'urllib3.packages.six.moves.urllib.parse' - imported by urllib3.request (top-level), urllib3.poolmanager (top-level)
missing module named collections.MutableMapping - imported by collections (optional), urllib3._collections (optional)
missing module named collections.Mapping - imported by collections (optional), pytz.lazy (optional), google.auth.jwt (optional), urllib3._collections (optional), google.auth.pluggable (optional), google.auth.identity_pool (optional), firebirdsql.utils (optional)
missing module named win_inet_pton - imported by socks (conditional, optional)
missing module named 'google.appengine' - imported by google.auth.app_engine (optional), googleapiclient.discovery_cache.appengine_memcache (top-level)
missing module named 'pyu2f.model' - imported by google.oauth2.challenges (delayed, optional)
missing module named 'pyu2f.errors' - imported by google.oauth2.challenges (delayed, optional)
missing module named pyu2f - imported by google.oauth2.challenges (delayed, optional)
missing module named 'oauth2client.locked_file' - imported by googleapiclient.discovery_cache.file_cache (optional)
missing module named 'oauth2client.contrib' - imported by googleapiclient.discovery_cache.file_cache (optional)
missing module named 'oauth2client.client' - imported by googleapiclient._auth (optional)
missing module named oauth2client - imported by googleapiclient._auth (optional)
missing module named ca_certs_locater - imported by httplib2.certs (optional)
missing module named railroad - imported by pyparsing.diagram (top-level)
missing module named pyparsing.Word - imported by pyparsing (delayed), pyparsing.unicode (delayed)
missing module named crypt - imported by firebirdsql.wireprotocol (delayed, optional)
missing module named passlib - imported by firebirdsql.wireprotocol (delayed, optional)
missing module named urlparse - imported by firebirdsql.utils (optional)
missing module named __builtin__ - imported by fdb.fbcore (optional)
missing module named _winreg - imported by fdb.ibase (delayed, conditional)
missing module named urllib3_secure_extra - imported by urllib3 (optional)
missing module named mega_client - imported by D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ifess_client_hidden.py (delayed, conditional, optional)
missing module named sets - imported by pytz.tzinfo (optional)
missing module named UserDict - imported by pytz.lazy (optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
