# PyInstaller runtime hook to handle encoding issues
# This file will be executed at the beginning of the compiled executable

import sys
import os

def setup_encoding_safety():
    """Setup encoding safety for PyInstaller executables"""
    try:
        # Ensure stdout and stderr have safe encoding handling
        if hasattr(sys.stdout, 'encoding') and sys.stdout.encoding is None:
            # Set a fallback encoding
            if hasattr(sys.stdout, 'buffer'):
                import codecs
                sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
                
        if hasattr(sys.stderr, 'encoding') and sys.stderr.encoding is None:
            # Set a fallback encoding  
            if hasattr(sys.stderr, 'buffer'):
                import codecs
                sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')
                
    except Exception:
        # Silently ignore any errors during encoding setup
        # This prevents the application from failing to start
        pass

# Execute the encoding safety setup
setup_encoding_safety() 