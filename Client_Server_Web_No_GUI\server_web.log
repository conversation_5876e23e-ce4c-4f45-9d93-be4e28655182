2025-06-07 10:51:19,996 - server_web - INFO - Loaded predefined query: Employee_Overtime with 0 variables
2025-06-07 10:51:19,996 - server_web - INFO - Loaded predefined query: Find_Diff_Job_Date_Overtime with 2 variables
2025-06-07 10:51:19,997 - server_web - INFO - Loaded predefined query: Find_Helper_Hasnt_Veh_No with 0 variables
2025-06-07 10:51:19,997 - server_web - INFO - Loaded predefined query: General_Work_Has_YY with 0 variables
2025-06-07 10:51:19,997 - server_web - INFO - Loaded predefined query: test_dynamic_variables with 3 variables
2025-06-07 10:51:20,007 - server_web - INFO - Loaded predefined query: Employee_Overtime with 0 variables
2025-06-07 10:51:20,008 - server_web - INFO - Loaded predefined query: Find_Diff_Job_Date_Overtime with 2 variables
2025-06-07 10:51:20,018 - server_web - INFO - Loaded predefined query: Find_Helper_Hasnt_Veh_No with 0 variables
2025-06-07 10:51:20,018 - server_web - INFO - Loaded predefined query: General_Work_Has_YY with 0 variables
2025-06-07 10:51:20,018 - server_web - INFO - Loaded predefined query: test_dynamic_variables with 3 variables
2025-06-07 10:51:20,025 - server_web - INFO - Direct database access module loaded successfully
2025-06-07 10:51:20,026 - server_web - INFO - [DIRECT-DB] Using backup directory: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\server_web\static\backups
2025-06-07 10:51:20,028 - server_web - INFO - Direct database access module loaded successfully
2025-06-07 10:51:20,029 - server_web - INFO - Server running on 0.0.0.0:5555
2025-06-07 10:51:20,029 - server_web - INFO - Starting connection acceptance thread
2025-06-07 10:51:20,029 - server_web - INFO - Starting heartbeat thread
2025-06-07 10:51:20,030 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:51:20
2025-06-07 10:51:20,030 - server_web - INFO - Started backup monitor thread
2025-06-07 10:51:20,030 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 10:51:20,044 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-06-07 10:51:20,044 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-07 10:51:25,031 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:51:25
2025-06-07 10:51:25,032 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 10:51:30,033 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:51:30
2025-06-07 10:51:30,034 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 10:51:34,630 - server_web - INFO - New connection from ('127.0.0.1', 57628)
2025-06-07 10:51:34,642 - server_web - INFO - Generated stable client ID: client_fdb-client-monitoring_127.0.0.1 for FDB-Client-Monitoring from 127.0.0.1
2025-06-07 10:51:34,644 - server_web - INFO - New client registered: FDB-Client-Monitoring (client_fdb-client-monitoring_127.0.0.1) from ('127.0.0.1', 57628)
2025-06-07 10:51:35,035 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:51:35
2025-06-07 10:51:35,035 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:51:40,036 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:51:40
2025-06-07 10:51:40,036 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:51:45,037 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:51:45
2025-06-07 10:51:45,037 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:51:50,038 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:51:50
2025-06-07 10:51:50,039 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:51:55,039 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:51:55
2025-06-07 10:51:55,039 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:52:00,040 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:52:00
2025-06-07 10:52:00,041 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:52:05,043 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:52:05
2025-06-07 10:52:05,044 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:52:05,044 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.4 seconds
2025-06-07 10:52:05,044 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 10:52:05,046 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 10:52:07,358 - server_web - DEBUG - No message received from FDB-Client-Monitoring, may have disconnected
2025-06-07 10:52:07,359 - server_web - INFO - Client FDB-Client-Monitoring marked as disconnected
2025-06-07 10:52:08,664 - server_web - INFO - New connection from ('127.0.0.1', 57660)
2025-06-07 10:52:08,713 - server_web - INFO - Generated stable client ID: client_fdb-client-monitoring_127.0.0.1 for FDB-Client-Monitoring from 127.0.0.1
2025-06-07 10:52:08,714 - server_web - INFO - Found existing client with ID client_fdb-client-monitoring_127.0.0.1
2025-06-07 10:52:08,714 - server_web - INFO - Updating existing client client_fdb-client-monitoring_127.0.0.1 with new connection
2025-06-07 10:52:08,714 - server_web - INFO - Reconnected client: FDB-Client-Monitoring (client_fdb-client-monitoring_127.0.0.1) from ('127.0.0.1', 57660)
2025-06-07 10:52:10,049 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:52:10
2025-06-07 10:52:10,049 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:52:15,050 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:52:15
2025-06-07 10:52:15,051 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:52:20,052 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:52:20
2025-06-07 10:52:20,052 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:52:23,120 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 10:52:23] "GET / HTTP/1.1" 200 -
2025-06-07 10:52:23,694 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 10:52:23] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-07 10:52:23,884 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 10:52:23] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-06-07 10:52:24,797 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 10:52:24] "GET / HTTP/1.1" 200 -
2025-06-07 10:52:25,053 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:52:25
2025-06-07 10:52:25,053 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:52:25,110 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 10:52:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-07 10:52:25,154 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 10:52:25] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-06-07 10:52:26,711 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 10:52:26] "GET /backup HTTP/1.1" 200 -
2025-06-07 10:52:26,799 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 10:52:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-07 10:52:26,931 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 10:52:26] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-06-07 10:52:27,974 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 10:52:27] "GET /query HTTP/1.1" 200 -
2025-06-07 10:52:28,004 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 10:52:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-07 10:52:28,207 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 10:52:28] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-06-07 10:52:28,525 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 10:52:28] "GET /api/query/get/Find_Diff_Job_Date_Overtime HTTP/1.1" 200 -
2025-06-07 10:52:28,784 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 10:52:28] "GET /api/query/get/test_dynamic_variables HTTP/1.1" 200 -
2025-06-07 10:52:30,054 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:52:30
2025-06-07 10:52:30,055 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:52:32,055 - server_web - INFO - Starting query execution query_1691c2ebe0e2_1749268352: # Gunakan query yang lebih sederhana untuk testing awal:
SELECT FIRST 5 a.ID, a.SCANNERUSEREMPID, a....
2025-06-07 10:52:32,056 - server_web - INFO - Connected clients: 1
2025-06-07 10:52:32,056 - server_web - INFO - Target clients: ['client_fdb-client-monitoring_127.0.0.1']
2025-06-07 10:52:32,059 - server_web - INFO - Query query_1691c2ebe0e2_1749268352 sent to client FDB-Client-Monitoring
2025-06-07 10:52:32,059 - server_web - INFO - Query query_1691c2ebe0e2_1749268352 sent to 1 clients, waiting for completion...
2025-06-07 10:52:32,060 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 10:52:32] "POST /api/query/send HTTP/1.1" 200 -
2025-06-07 10:52:32,451 - server_web - INFO -    Message data keys: ['query', 'description', 'error', 'timestamp', 'status', 'rows', 'headers']
2025-06-07 10:52:32,452 - server_web - INFO -    Query ID from result: None
2025-06-07 10:52:32,453 - server_web - INFO -    Active queries: []
2025-06-07 10:52:32,459 - server_web - INFO -    Legacy result: 0 rows, 0 headers
2025-06-07 10:52:34,375 - server_web - INFO - Getting query results for client_id: all, query_id: query_1691c2ebe0e2_1749268352
2025-06-07 10:52:35,056 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:52:35
2025-06-07 10:52:35,056 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:52:40,057 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:52:40
2025-06-07 10:52:40,057 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:52:40,058 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 31.3 seconds
2025-06-07 10:52:40,058 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 10:52:40,059 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 10:52:45,059 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:52:45
2025-06-07 10:52:45,060 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:52:50,060 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:52:50
2025-06-07 10:52:50,061 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:52:55,062 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:52:55
2025-06-07 10:52:55,062 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:52:56,072 - server_web - INFO - Getting query results for client_id: all, query_id: query_1691c2ebe0e2_1749268352
2025-06-07 10:52:58,379 - server_web - INFO - Getting query results for client_id: all, query_id: query_1691c2ebe0e2_1749268352
2025-06-07 10:53:00,063 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:53:00
2025-06-07 10:53:00,063 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:53:00,776 - server_web - INFO - Getting query results for client_id: all, query_id: query_1691c2ebe0e2_1749268352
2025-06-07 10:53:02,779 - server_web - INFO - Getting query results for client_id: all, query_id: query_1691c2ebe0e2_1749268352
2025-06-07 10:53:04,774 - server_web - INFO - Getting query results for client_id: all, query_id: query_1691c2ebe0e2_1749268352
2025-06-07 10:53:05,064 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:53:05
2025-06-07 10:53:05,064 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:53:10,065 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:53:10
2025-06-07 10:53:10,065 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:53:10,066 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 10:53:10,066 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 10:53:10,067 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 10:53:15,068 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:53:15
2025-06-07 10:53:15,068 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:53:20,069 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:53:20
2025-06-07 10:53:20,070 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:53:25,070 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:53:25
2025-06-07 10:53:25,071 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:53:30,072 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:53:30
2025-06-07 10:53:30,072 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:53:35,073 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:53:35
2025-06-07 10:53:35,075 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:53:40,076 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:53:40
2025-06-07 10:53:40,076 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:53:40,077 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 10:53:40,077 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 10:53:40,078 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 10:53:45,078 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:53:45
2025-06-07 10:53:45,079 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:53:50,079 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:53:50
2025-06-07 10:53:50,080 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:53:55,080 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:53:55
2025-06-07 10:53:55,081 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:54:00,082 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:54:00
2025-06-07 10:54:00,082 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:54:05,083 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:54:05
2025-06-07 10:54:05,084 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:54:10,084 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:54:10
2025-06-07 10:54:10,085 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:54:10,085 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 10:54:10,085 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 10:54:10,086 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 10:54:15,087 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:54:15
2025-06-07 10:54:15,087 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:54:20,088 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:54:20
2025-06-07 10:54:20,088 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:54:25,089 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:54:25
2025-06-07 10:54:25,090 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:54:30,091 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:54:30
2025-06-07 10:54:30,092 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:54:35,093 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:54:35
2025-06-07 10:54:35,093 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:54:40,094 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:54:40
2025-06-07 10:54:40,094 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:54:40,095 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 10:54:40,095 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 10:54:40,096 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 10:54:45,097 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:54:45
2025-06-07 10:54:45,097 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:54:50,098 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:54:50
2025-06-07 10:54:50,098 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:54:55,099 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:54:55
2025-06-07 10:54:55,100 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:55:00,100 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:55:00
2025-06-07 10:55:00,100 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:55:05,101 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:55:05
2025-06-07 10:55:05,102 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:55:10,102 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:55:10
2025-06-07 10:55:10,103 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:55:10,103 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 10:55:10,103 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 10:55:10,103 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 10:55:15,104 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:55:15
2025-06-07 10:55:15,104 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:55:20,105 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:55:20
2025-06-07 10:55:20,105 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:55:25,106 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:55:25
2025-06-07 10:55:25,106 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:55:30,106 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:55:30
2025-06-07 10:55:30,107 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:55:35,107 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:55:35
2025-06-07 10:55:35,108 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:55:40,108 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:55:40
2025-06-07 10:55:40,109 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:55:40,109 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 10:55:40,109 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 10:55:40,110 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 10:55:45,111 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:55:45
2025-06-07 10:55:45,111 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:55:50,111 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:55:50
2025-06-07 10:55:50,112 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:55:55,112 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:55:55
2025-06-07 10:55:55,113 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:56:00,114 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:56:00
2025-06-07 10:56:00,114 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:56:05,115 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:56:05
2025-06-07 10:56:05,115 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:56:10,116 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:56:10
2025-06-07 10:56:10,117 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:56:10,118 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 10:56:10,119 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 10:56:10,120 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 10:56:15,121 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:56:15
2025-06-07 10:56:15,121 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:56:20,122 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:56:20
2025-06-07 10:56:20,123 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:56:25,124 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:56:25
2025-06-07 10:56:25,124 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:56:30,127 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:56:30
2025-06-07 10:56:30,128 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:56:35,128 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:56:35
2025-06-07 10:56:35,129 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:56:40,130 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:56:40
2025-06-07 10:56:40,130 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:56:40,131 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 10:56:40,131 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 10:56:40,132 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 10:56:45,133 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:56:45
2025-06-07 10:56:45,134 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:56:50,134 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:56:50
2025-06-07 10:56:50,135 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:56:55,135 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:56:55
2025-06-07 10:56:55,136 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:57:00,136 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:57:00
2025-06-07 10:57:00,137 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:57:05,138 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:57:05
2025-06-07 10:57:05,138 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:57:10,140 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:57:10
2025-06-07 10:57:10,140 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:57:10,140 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 10:57:10,140 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 10:57:10,141 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 10:57:15,142 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:57:15
2025-06-07 10:57:15,143 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:57:20,143 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:57:20
2025-06-07 10:57:20,144 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:57:25,144 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:57:25
2025-06-07 10:57:25,145 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:57:30,146 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:57:30
2025-06-07 10:57:30,147 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:57:35,147 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:57:35
2025-06-07 10:57:35,147 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:57:40,148 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:57:40
2025-06-07 10:57:40,148 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:57:40,148 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 10:57:40,148 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 10:57:40,149 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 10:57:45,150 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:57:45
2025-06-07 10:57:45,150 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:57:50,151 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:57:50
2025-06-07 10:57:50,152 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:57:55,152 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:57:55
2025-06-07 10:57:55,153 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:58:00,153 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:58:00
2025-06-07 10:58:00,154 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:58:05,155 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:58:05
2025-06-07 10:58:05,155 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:58:10,156 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:58:10
2025-06-07 10:58:10,156 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:58:10,157 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 10:58:10,157 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 10:58:10,158 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 10:58:15,158 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:58:15
2025-06-07 10:58:15,159 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:58:20,159 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:58:20
2025-06-07 10:58:20,159 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:58:25,160 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:58:25
2025-06-07 10:58:25,161 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:58:30,162 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:58:30
2025-06-07 10:58:30,162 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:58:35,163 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:58:35
2025-06-07 10:58:35,163 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:58:40,164 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:58:40
2025-06-07 10:58:40,164 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:58:40,165 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 10:58:40,165 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 10:58:40,166 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 10:58:45,166 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:58:45
2025-06-07 10:58:45,167 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:58:50,167 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:58:50
2025-06-07 10:58:50,168 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:58:55,169 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:58:55
2025-06-07 10:58:55,170 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:59:00,171 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:59:00
2025-06-07 10:59:00,171 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:59:05,172 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:59:05
2025-06-07 10:59:05,173 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:59:10,173 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:59:10
2025-06-07 10:59:10,174 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:59:10,174 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 10:59:10,174 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 10:59:10,175 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 10:59:15,176 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:59:15
2025-06-07 10:59:15,176 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:59:20,177 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:59:20
2025-06-07 10:59:20,178 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:59:25,178 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:59:25
2025-06-07 10:59:25,179 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:59:30,179 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:59:30
2025-06-07 10:59:30,180 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:59:35,181 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:59:35
2025-06-07 10:59:35,181 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:59:40,182 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:59:40
2025-06-07 10:59:40,183 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:59:40,183 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 10:59:40,184 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 10:59:40,186 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 10:59:45,187 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:59:45
2025-06-07 10:59:45,187 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:59:50,188 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:59:50
2025-06-07 10:59:50,188 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 10:59:55,189 - server_web - DEBUG - Heartbeat check at 2025-06-07 10:59:55
2025-06-07 10:59:55,189 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:00:00,190 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:00:00
2025-06-07 11:00:00,191 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:00:05,192 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:00:05
2025-06-07 11:00:05,193 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:00:10,193 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:00:10
2025-06-07 11:00:10,194 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:00:10,194 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:00:10,195 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:00:10,196 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:00:15,196 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:00:15
2025-06-07 11:00:15,197 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:00:20,197 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:00:20
2025-06-07 11:00:20,198 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:00:25,198 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:00:25
2025-06-07 11:00:25,199 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:00:30,199 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:00:30
2025-06-07 11:00:30,200 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:00:35,201 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:00:35
2025-06-07 11:00:35,201 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:00:40,202 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:00:40
2025-06-07 11:00:40,202 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:00:40,203 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:00:40,203 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:00:40,204 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:00:45,204 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:00:45
2025-06-07 11:00:45,205 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:00:50,205 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:00:50
2025-06-07 11:00:50,206 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:00:55,207 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:00:55
2025-06-07 11:00:55,208 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:01:00,208 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:01:00
2025-06-07 11:01:00,209 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:01:05,210 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:01:05
2025-06-07 11:01:05,210 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:01:10,211 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:01:10
2025-06-07 11:01:10,212 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:01:10,212 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:01:10,212 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:01:10,213 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:01:15,214 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:01:15
2025-06-07 11:01:15,215 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:01:20,215 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:01:20
2025-06-07 11:01:20,216 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:01:25,217 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:01:25
2025-06-07 11:01:25,217 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:01:30,218 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:01:30
2025-06-07 11:01:30,219 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:01:35,220 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:01:35
2025-06-07 11:01:35,220 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:01:40,222 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:01:40
2025-06-07 11:01:40,223 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:01:40,223 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:01:40,224 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:01:40,225 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:01:45,226 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:01:45
2025-06-07 11:01:45,226 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:01:50,227 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:01:50
2025-06-07 11:01:50,227 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:01:55,227 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:01:55
2025-06-07 11:01:55,228 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:02:00,229 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:02:00
2025-06-07 11:02:00,229 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:02:05,230 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:02:05
2025-06-07 11:02:05,231 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:02:10,231 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:02:10
2025-06-07 11:02:10,232 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:02:10,232 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:02:10,232 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:02:10,233 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:02:15,234 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:02:15
2025-06-07 11:02:15,234 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:02:20,235 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:02:20
2025-06-07 11:02:20,236 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:02:25,236 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:02:25
2025-06-07 11:02:25,237 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:02:30,237 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:02:30
2025-06-07 11:02:30,237 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:02:35,238 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:02:35
2025-06-07 11:02:35,239 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:02:40,239 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:02:40
2025-06-07 11:02:40,240 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:02:40,240 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:02:40,240 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:02:40,241 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:02:45,242 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:02:45
2025-06-07 11:02:45,242 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:02:50,242 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:02:50
2025-06-07 11:02:50,243 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:02:55,244 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:02:55
2025-06-07 11:02:55,244 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:03:00,245 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:03:00
2025-06-07 11:03:00,246 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:03:05,246 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:03:05
2025-06-07 11:03:05,247 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:03:10,247 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:03:10
2025-06-07 11:03:10,247 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:03:10,248 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:03:10,248 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:03:10,248 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:03:15,249 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:03:15
2025-06-07 11:03:15,250 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:03:20,250 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:03:20
2025-06-07 11:03:20,251 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:03:25,251 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:03:25
2025-06-07 11:03:25,252 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:03:30,253 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:03:30
2025-06-07 11:03:30,253 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:03:35,254 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:03:35
2025-06-07 11:03:35,254 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:03:40,255 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:03:40
2025-06-07 11:03:40,256 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:03:40,257 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:03:40,258 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:03:40,260 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:03:45,261 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:03:45
2025-06-07 11:03:45,261 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:03:50,262 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:03:50
2025-06-07 11:03:50,263 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:03:55,264 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:03:55
2025-06-07 11:03:55,265 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:04:00,265 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:04:00
2025-06-07 11:04:00,266 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:04:05,267 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:04:05
2025-06-07 11:04:05,268 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:04:10,268 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:04:10
2025-06-07 11:04:10,269 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:04:10,269 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:04:10,269 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:04:10,270 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:04:15,271 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:04:15
2025-06-07 11:04:15,271 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:04:20,272 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:04:20
2025-06-07 11:04:20,273 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:04:25,274 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:04:25
2025-06-07 11:04:25,275 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:04:30,276 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:04:30
2025-06-07 11:04:30,276 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:04:35,277 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:04:35
2025-06-07 11:04:35,278 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:04:40,279 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:04:40
2025-06-07 11:04:40,280 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:04:40,280 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:04:40,281 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:04:40,281 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:04:45,282 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:04:45
2025-06-07 11:04:45,283 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:04:50,283 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:04:50
2025-06-07 11:04:50,284 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:04:55,285 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:04:55
2025-06-07 11:04:55,285 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:05:00,286 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:05:00
2025-06-07 11:05:00,287 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:05:05,287 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:05:05
2025-06-07 11:05:05,288 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:05:10,289 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:05:10
2025-06-07 11:05:10,289 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:05:10,290 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:05:10,290 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:05:10,290 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:05:15,291 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:05:15
2025-06-07 11:05:15,292 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:05:20,294 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:05:20
2025-06-07 11:05:20,294 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:05:25,295 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:05:25
2025-06-07 11:05:25,296 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:05:30,298 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:05:30
2025-06-07 11:05:30,298 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:05:35,299 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:05:35
2025-06-07 11:05:35,300 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:05:40,300 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:05:40
2025-06-07 11:05:40,301 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:05:40,301 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:05:40,301 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:05:40,302 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:05:45,303 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:05:45
2025-06-07 11:05:45,304 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:05:50,304 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:05:50
2025-06-07 11:05:50,305 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:05:55,306 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:05:55
2025-06-07 11:05:55,306 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:06:00,315 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:06:00
2025-06-07 11:06:00,316 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:06:05,316 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:06:05
2025-06-07 11:06:05,317 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:06:10,317 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:06:10
2025-06-07 11:06:10,318 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:06:10,318 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:06:10,318 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:06:10,319 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:06:15,319 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:06:15
2025-06-07 11:06:15,320 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:06:20,320 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:06:20
2025-06-07 11:06:20,321 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:06:25,322 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:06:25
2025-06-07 11:06:25,322 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:06:30,323 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:06:30
2025-06-07 11:06:30,324 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:06:35,324 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:06:35
2025-06-07 11:06:35,325 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:06:40,326 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:06:40
2025-06-07 11:06:40,328 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:06:40,329 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:06:40,329 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:06:40,336 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:06:45,338 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:06:45
2025-06-07 11:06:45,339 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:06:50,341 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:06:50
2025-06-07 11:06:50,342 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:06:55,343 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:06:55
2025-06-07 11:06:55,345 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:07:00,346 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:07:00
2025-06-07 11:07:00,349 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:07:05,349 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:07:05
2025-06-07 11:07:05,350 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:07:10,350 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:07:10
2025-06-07 11:07:10,351 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:07:10,352 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:07:10,352 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:07:10,354 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:07:15,355 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:07:15
2025-06-07 11:07:15,355 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:07:20,356 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:07:20
2025-06-07 11:07:20,357 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:07:25,358 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:07:25
2025-06-07 11:07:25,359 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:07:30,360 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:07:30
2025-06-07 11:07:30,361 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:07:35,362 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:07:35
2025-06-07 11:07:35,365 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:07:40,367 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:07:40
2025-06-07 11:07:40,368 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:07:40,369 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:07:40,370 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:07:40,372 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:07:45,374 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:07:45
2025-06-07 11:07:45,375 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:07:50,375 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:07:50
2025-06-07 11:07:50,376 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:07:55,376 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:07:55
2025-06-07 11:07:55,377 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:08:00,378 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:08:00
2025-06-07 11:08:00,382 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:08:05,385 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:08:05
2025-06-07 11:08:05,387 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:08:10,388 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:08:10
2025-06-07 11:08:10,388 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:08:15,389 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:08:15
2025-06-07 11:08:15,389 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:08:15,390 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 35.0 seconds
2025-06-07 11:08:15,390 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:08:15,402 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:08:20,403 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:08:20
2025-06-07 11:08:20,404 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:08:25,405 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:08:25
2025-06-07 11:08:25,406 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:08:30,407 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:08:30
2025-06-07 11:08:30,408 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:08:35,409 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:08:35
2025-06-07 11:08:35,409 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:08:40,410 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:08:40
2025-06-07 11:08:40,411 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:08:45,411 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:08:45
2025-06-07 11:08:45,413 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:08:45,413 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:08:45,414 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:08:45,420 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:08:50,421 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:08:50
2025-06-07 11:08:50,422 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:08:55,424 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:08:55
2025-06-07 11:08:55,424 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:09:00,425 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:09:00
2025-06-07 11:09:00,426 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:09:05,427 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:09:05
2025-06-07 11:09:05,428 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:09:10,429 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:09:10
2025-06-07 11:09:10,430 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:09:15,431 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:09:15
2025-06-07 11:09:15,433 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:09:15,433 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:09:15,434 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:09:15,446 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:09:20,448 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:09:20
2025-06-07 11:09:20,449 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:09:25,450 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:09:25
2025-06-07 11:09:25,451 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:09:30,452 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:09:30
2025-06-07 11:09:30,452 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:09:35,456 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:09:35
2025-06-07 11:09:35,457 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:09:40,458 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:09:40
2025-06-07 11:09:40,459 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:09:45,460 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:09:45
2025-06-07 11:09:45,461 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:09:45,461 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:09:45,462 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:09:45,463 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:09:50,464 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:09:50
2025-06-07 11:09:50,464 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:09:55,465 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:09:55
2025-06-07 11:09:55,467 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:10:00,468 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:10:00
2025-06-07 11:10:00,470 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:10:05,472 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:10:05
2025-06-07 11:10:05,472 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:10:10,473 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:10:10
2025-06-07 11:10:10,474 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:10:15,475 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:10:15
2025-06-07 11:10:15,477 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:10:15,483 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:10:15,484 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:10:15,486 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:10:20,488 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:10:20
2025-06-07 11:10:20,490 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:10:25,491 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:10:25
2025-06-07 11:10:25,492 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:10:30,493 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:10:30
2025-06-07 11:10:30,495 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:10:35,497 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:10:35
2025-06-07 11:10:35,497 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:10:40,499 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:10:40
2025-06-07 11:10:40,500 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:10:45,501 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:10:45
2025-06-07 11:10:45,502 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:10:45,503 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:10:45,503 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:10:45,505 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:10:50,506 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:10:50
2025-06-07 11:10:50,507 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:10:55,508 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:10:55
2025-06-07 11:10:55,511 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:11:00,512 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:11:00
2025-06-07 11:11:00,514 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:11:05,516 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:11:05
2025-06-07 11:11:05,517 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:11:10,518 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:11:10
2025-06-07 11:11:10,520 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:11:15,521 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:11:15
2025-06-07 11:11:15,522 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:11:15,523 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:11:15,524 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:11:15,527 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:11:20,529 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:11:20
2025-06-07 11:11:20,529 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:11:25,530 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:11:25
2025-06-07 11:11:25,531 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:11:30,532 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:11:30
2025-06-07 11:11:30,535 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:11:35,537 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:11:35
2025-06-07 11:11:35,539 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:11:40,540 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:11:40
2025-06-07 11:11:40,542 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:11:45,544 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:11:45
2025-06-07 11:11:45,546 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:11:45,547 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:11:45,548 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:11:45,550 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:11:50,551 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:11:50
2025-06-07 11:11:50,551 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:11:55,552 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:11:55
2025-06-07 11:11:55,553 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:12:00,554 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:12:00
2025-06-07 11:12:00,557 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:12:05,558 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:12:05
2025-06-07 11:12:05,559 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:12:10,561 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:12:10
2025-06-07 11:12:10,562 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:12:15,563 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:12:15
2025-06-07 11:12:15,563 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:12:15,564 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:12:15,564 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:12:15,571 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:12:20,572 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:12:20
2025-06-07 11:12:20,577 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:12:25,579 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:12:25
2025-06-07 11:12:25,579 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:12:30,580 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:12:30
2025-06-07 11:12:30,581 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:12:35,581 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:12:35
2025-06-07 11:12:35,582 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:12:40,583 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:12:40
2025-06-07 11:12:40,584 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:12:45,585 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:12:45
2025-06-07 11:12:45,585 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:12:45,586 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:12:45,586 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:12:45,588 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:12:50,589 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:12:50
2025-06-07 11:12:50,591 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:12:55,592 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:12:55
2025-06-07 11:12:55,595 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:13:00,596 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:13:00
2025-06-07 11:13:00,597 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:13:05,598 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:13:05
2025-06-07 11:13:05,599 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:13:10,602 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:13:10
2025-06-07 11:13:10,603 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:13:15,605 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:13:15
2025-06-07 11:13:15,605 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:13:15,606 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:13:15,606 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:13:15,608 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:13:20,609 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:13:20
2025-06-07 11:13:20,609 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:13:25,610 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:13:25
2025-06-07 11:13:25,610 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:13:30,611 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:13:30
2025-06-07 11:13:30,611 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:13:35,612 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:13:35
2025-06-07 11:13:35,613 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:13:40,614 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:13:40
2025-06-07 11:13:40,615 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:13:45,616 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:13:45
2025-06-07 11:13:45,617 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:13:45,617 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:13:45,618 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:13:45,620 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:13:50,621 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:13:50
2025-06-07 11:13:50,621 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:13:55,622 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:13:55
2025-06-07 11:13:55,623 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:14:00,624 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:14:00
2025-06-07 11:14:00,624 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:14:05,625 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:14:05
2025-06-07 11:14:05,625 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:14:10,626 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:14:10
2025-06-07 11:14:10,626 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:14:15,628 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:14:15
2025-06-07 11:14:15,628 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:14:15,628 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:14:15,629 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:14:15,629 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:14:20,630 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:14:20
2025-06-07 11:14:20,631 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:14:25,632 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:14:25
2025-06-07 11:14:25,633 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:14:30,634 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:14:30
2025-06-07 11:14:30,635 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:14:35,636 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:14:35
2025-06-07 11:14:35,638 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:14:40,639 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:14:40
2025-06-07 11:14:40,640 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:14:45,641 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:14:45
2025-06-07 11:14:45,642 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:14:45,643 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:14:45,644 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:14:45,648 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:14:50,651 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:14:50
2025-06-07 11:14:50,652 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:14:55,652 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:14:55
2025-06-07 11:14:55,652 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:15:00,653 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:15:00
2025-06-07 11:15:00,654 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:15:05,655 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:15:05
2025-06-07 11:15:05,655 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:15:10,656 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:15:10
2025-06-07 11:15:10,657 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:15:15,658 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:15:15
2025-06-07 11:15:15,659 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:15:15,659 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:15:15,660 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:15:15,661 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:15:20,662 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:15:20
2025-06-07 11:15:20,664 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:15:25,665 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:15:25
2025-06-07 11:15:25,666 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:15:30,668 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:15:30
2025-06-07 11:15:30,669 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:15:35,669 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:15:35
2025-06-07 11:15:35,670 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:15:40,671 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:15:40
2025-06-07 11:15:40,672 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:15:45,672 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:15:45
2025-06-07 11:15:45,673 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:15:45,674 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:15:45,674 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:15:45,675 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:15:50,677 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:15:50
2025-06-07 11:15:50,678 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:15:55,679 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:15:55
2025-06-07 11:15:55,680 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:16:00,681 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:16:00
2025-06-07 11:16:00,682 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:16:05,683 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:16:05
2025-06-07 11:16:05,686 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:16:10,687 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:16:10
2025-06-07 11:16:10,688 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:16:15,689 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:16:15
2025-06-07 11:16:15,690 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:16:15,691 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:16:15,691 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:16:15,693 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:16:20,695 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:16:20
2025-06-07 11:16:20,698 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:16:25,698 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:16:25
2025-06-07 11:16:25,699 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:16:30,700 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:16:30
2025-06-07 11:16:30,701 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:16:35,702 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:16:35
2025-06-07 11:16:35,702 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:16:40,703 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:16:40
2025-06-07 11:16:40,704 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:16:45,706 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:16:45
2025-06-07 11:16:45,707 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:16:45,707 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:16:45,707 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:16:45,709 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:16:50,710 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:16:50
2025-06-07 11:16:50,711 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:16:55,712 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:16:55
2025-06-07 11:16:55,713 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:17:00,714 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:17:00
2025-06-07 11:17:00,715 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:17:05,716 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:17:05
2025-06-07 11:17:05,717 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:17:10,717 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:17:10
2025-06-07 11:17:10,719 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:17:15,720 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:17:15
2025-06-07 11:17:15,721 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:17:15,722 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:17:15,723 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:17:15,725 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:17:20,726 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:17:20
2025-06-07 11:17:20,728 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:17:25,730 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:17:25
2025-06-07 11:17:25,730 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:17:30,731 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:17:30
2025-06-07 11:17:30,731 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:17:35,732 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:17:35
2025-06-07 11:17:35,733 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:17:40,734 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:17:40
2025-06-07 11:17:40,734 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:17:45,735 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:17:45
2025-06-07 11:17:45,736 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:17:45,736 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:17:45,736 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:17:45,739 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:17:50,740 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:17:50
2025-06-07 11:17:50,741 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:17:55,742 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:17:55
2025-06-07 11:17:55,743 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:18:00,743 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:18:00
2025-06-07 11:18:00,745 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:18:05,746 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:18:05
2025-06-07 11:18:05,746 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:18:10,747 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:18:10
2025-06-07 11:18:10,747 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:18:15,748 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:18:15
2025-06-07 11:18:15,748 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:18:15,749 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:18:15,749 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:18:15,749 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:18:20,750 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:18:20
2025-06-07 11:18:20,750 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:18:25,751 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:18:25
2025-06-07 11:18:25,751 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:18:30,752 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:18:30
2025-06-07 11:18:30,755 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:18:35,756 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:18:35
2025-06-07 11:18:35,757 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:18:40,758 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:18:40
2025-06-07 11:18:40,758 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:18:45,760 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:18:45
2025-06-07 11:18:45,761 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:18:45,761 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:18:45,761 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:18:45,762 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:18:50,763 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:18:50
2025-06-07 11:18:50,764 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:18:55,765 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:18:55
2025-06-07 11:18:55,766 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:19:00,766 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:19:00
2025-06-07 11:19:00,767 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:19:05,768 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:19:05
2025-06-07 11:19:05,769 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:19:10,770 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:19:10
2025-06-07 11:19:10,771 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:19:15,772 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:19:15
2025-06-07 11:19:15,772 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:19:15,773 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:19:15,773 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:19:15,774 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:19:20,774 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:19:20
2025-06-07 11:19:20,775 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:19:25,776 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:19:25
2025-06-07 11:19:25,776 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:19:30,777 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:19:30
2025-06-07 11:19:30,777 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:19:35,778 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:19:35
2025-06-07 11:19:35,778 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:19:40,779 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:19:40
2025-06-07 11:19:40,780 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:19:45,781 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:19:45
2025-06-07 11:19:45,781 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:19:45,782 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:19:45,782 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:19:45,784 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:19:50,784 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:19:50
2025-06-07 11:19:50,786 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:19:55,787 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:19:55
2025-06-07 11:19:55,788 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:20:00,789 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:20:00
2025-06-07 11:20:00,790 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:20:05,790 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:20:05
2025-06-07 11:20:05,791 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:20:10,792 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:20:10
2025-06-07 11:20:10,793 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:20:15,795 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:20:15
2025-06-07 11:20:15,796 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:20:15,796 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:20:15,797 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:20:15,798 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:20:20,799 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:20:20
2025-06-07 11:20:20,800 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:20:25,801 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:20:25
2025-06-07 11:20:25,802 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:20:30,802 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:20:30
2025-06-07 11:20:30,803 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:20:35,804 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:20:35
2025-06-07 11:20:35,805 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:20:40,806 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:20:40
2025-06-07 11:20:40,807 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:20:45,807 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:20:45
2025-06-07 11:20:45,808 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:20:45,808 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:20:45,808 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:20:45,808 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:20:50,809 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:20:50
2025-06-07 11:20:50,810 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:20:55,811 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:20:55
2025-06-07 11:20:55,812 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:21:00,813 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:21:00
2025-06-07 11:21:00,814 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:21:05,815 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:21:05
2025-06-07 11:21:05,816 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:21:10,817 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:21:10
2025-06-07 11:21:10,818 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:21:15,818 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:21:15
2025-06-07 11:21:15,819 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:21:15,819 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:21:15,820 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:21:15,820 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:21:20,821 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:21:20
2025-06-07 11:21:20,821 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:21:25,822 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:21:25
2025-06-07 11:21:25,822 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:21:30,823 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:21:30
2025-06-07 11:21:30,824 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:21:35,824 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:21:35
2025-06-07 11:21:35,825 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:21:40,826 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:21:40
2025-06-07 11:21:40,826 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:21:45,827 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:21:45
2025-06-07 11:21:45,827 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:21:45,828 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:21:45,828 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:21:45,828 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:21:50,829 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:21:50
2025-06-07 11:21:50,829 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:21:55,830 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:21:55
2025-06-07 11:21:55,830 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:22:00,832 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:22:00
2025-06-07 11:22:00,832 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:22:05,833 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:22:05
2025-06-07 11:22:05,834 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:22:10,834 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:22:10
2025-06-07 11:22:10,835 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:22:15,836 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:22:15
2025-06-07 11:22:15,837 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:22:15,838 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:22:15,838 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:22:15,840 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:22:20,841 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:22:20
2025-06-07 11:22:20,841 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:22:25,842 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:22:25
2025-06-07 11:22:25,843 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:22:30,844 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:22:30
2025-06-07 11:22:30,845 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:22:35,845 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:22:35
2025-06-07 11:22:35,846 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:22:40,846 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:22:40
2025-06-07 11:22:40,847 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:22:45,849 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:22:45
2025-06-07 11:22:45,849 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:22:45,849 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:22:45,849 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:22:45,850 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:22:50,851 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:22:50
2025-06-07 11:22:50,852 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:22:55,853 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:22:55
2025-06-07 11:22:55,853 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:23:00,854 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:23:00
2025-06-07 11:23:00,855 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:23:05,855 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:23:05
2025-06-07 11:23:05,856 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:23:10,856 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:23:10
2025-06-07 11:23:10,857 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:23:15,857 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:23:15
2025-06-07 11:23:15,858 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:23:15,858 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:23:15,858 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:23:15,859 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:23:17,862 - server_web - DEBUG - No message received from FDB-Client-Monitoring, may have disconnected
2025-06-07 11:23:17,862 - server_web - INFO - Client FDB-Client-Monitoring marked as disconnected
2025-06-07 11:23:18,351 - server_web - INFO - Server stopped
2025-06-07 11:23:18,361 - server_web - INFO - Connection acceptance thread stopped
2025-06-07 11:55:12,838 - server_web - INFO - Loaded predefined query: Employee_Overtime with 0 variables
2025-06-07 11:55:12,839 - server_web - INFO - Loaded predefined query: Find_Diff_Job_Date_Overtime with 2 variables
2025-06-07 11:55:12,840 - server_web - INFO - Loaded predefined query: Find_Helper_Hasnt_Veh_No with 0 variables
2025-06-07 11:55:12,840 - server_web - INFO - Loaded predefined query: General_Work_Has_YY with 0 variables
2025-06-07 11:55:12,840 - server_web - INFO - Loaded predefined query: test_dynamic_variables with 3 variables
2025-06-07 11:55:12,848 - server_web - INFO - [DIRECT-DB] Using backup directory: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\server_web\static\backups
2025-06-07 11:55:12,852 - server_web - INFO - Direct database access module loaded successfully
2025-06-07 11:55:12,854 - server_web - INFO - Server running on 0.0.0.0:5555
2025-06-07 11:55:12,855 - server_web - INFO - Starting connection acceptance thread
2025-06-07 11:55:12,855 - server_web - INFO - Starting heartbeat thread
2025-06-07 11:55:12,855 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:55:12
2025-06-07 11:55:12,855 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:55:14,867 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-06-07 11:55:14,868 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-07 11:55:17,856 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:55:17
2025-06-07 11:55:17,856 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:55:22,857 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:55:22
2025-06-07 11:55:22,858 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:55:27,859 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:55:27
2025-06-07 11:55:27,860 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:55:32,861 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:55:32
2025-06-07 11:55:32,863 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:55:37,864 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:55:37
2025-06-07 11:55:37,865 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:55:42,865 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:55:42
2025-06-07 11:55:42,866 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:55:47,867 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:55:47
2025-06-07 11:55:47,867 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:55:52,868 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:55:52
2025-06-07 11:55:52,869 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:55:57,870 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:55:57
2025-06-07 11:55:57,871 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:56:02,873 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:56:02
2025-06-07 11:56:02,874 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:56:07,875 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:56:07
2025-06-07 11:56:07,876 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:56:12,877 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:56:12
2025-06-07 11:56:12,877 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:56:17,878 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:56:17
2025-06-07 11:56:17,878 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:56:22,879 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:56:22
2025-06-07 11:56:22,879 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:56:27,880 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:56:27
2025-06-07 11:56:27,881 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:56:32,881 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:56:32
2025-06-07 11:56:32,883 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:56:37,884 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:56:37
2025-06-07 11:56:37,884 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:56:42,885 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:56:42
2025-06-07 11:56:42,885 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:56:47,886 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:56:47
2025-06-07 11:56:47,886 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:56:52,887 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:56:52
2025-06-07 11:56:52,888 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:56:57,889 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:56:57
2025-06-07 11:56:57,890 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:57:02,891 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:57:02
2025-06-07 11:57:02,892 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:57:07,893 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:57:07
2025-06-07 11:57:07,894 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:57:12,894 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:57:12
2025-06-07 11:57:12,895 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:57:17,896 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:57:17
2025-06-07 11:57:17,896 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:57:22,897 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:57:22
2025-06-07 11:57:22,898 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:57:27,899 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:57:27
2025-06-07 11:57:27,899 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:57:32,900 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:57:32
2025-06-07 11:57:32,901 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:57:37,901 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:57:37
2025-06-07 11:57:37,902 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:57:42,904 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:57:42
2025-06-07 11:57:42,905 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:57:45,878 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 11:57:45] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 11:57:47,906 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:57:47
2025-06-07 11:57:47,906 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:57:52,907 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:57:52
2025-06-07 11:57:52,907 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:57:55,546 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 11:57:55] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 11:57:57,908 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:57:57
2025-06-07 11:57:57,910 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 11:57:58,616 - server_web - INFO - New connection from ('127.0.0.1', 50086)
2025-06-07 11:57:58,620 - server_web - INFO - Generated stable client ID: client_fdb-client-monitoring_127.0.0.1 for FDB-Client-Monitoring from 127.0.0.1
2025-06-07 11:57:58,623 - server_web - INFO - New client registered: FDB-Client-Monitoring (client_fdb-client-monitoring_127.0.0.1) from ('127.0.0.1', 50086)
2025-06-07 11:58:02,911 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:58:02
2025-06-07 11:58:02,911 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:58:05,859 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 11:58:05] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 11:58:07,912 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:58:07
2025-06-07 11:58:07,913 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:58:12,914 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:58:12
2025-06-07 11:58:12,915 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:58:15,544 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 11:58:15] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 11:58:17,916 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:58:17
2025-06-07 11:58:17,917 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:58:22,918 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:58:22
2025-06-07 11:58:22,918 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:58:25,862 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 11:58:25] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 11:58:27,919 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:58:27
2025-06-07 11:58:27,921 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:58:30,998 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 11:58:30] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 11:58:32,922 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:58:32
2025-06-07 11:58:32,923 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:58:32,923 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 34.3 seconds
2025-06-07 11:58:32,924 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:58:32,925 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:58:33,037 - server_web - INFO - Starting query execution query_c0c6403db8af_1749272313: SELECT FIRST 1 'Test' as MESSAGE FROM RDB$DATABASE...
2025-06-07 11:58:33,038 - server_web - INFO - Connected clients: 1
2025-06-07 11:58:33,039 - server_web - INFO - Target clients: ['client_fdb-client-monitoring_127.0.0.1']
2025-06-07 11:58:33,072 - server_web - INFO - Query query_c0c6403db8af_1749272313 sent to 1 clients, waiting for completion...
2025-06-07 11:58:33,073 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 11:58:33] "POST /api/query/send HTTP/1.1" 200 -
2025-06-07 11:58:33,937 - server_web - INFO -    Message data keys: ['query', 'description', 'error', 'timestamp', 'status', 'rows', 'headers']
2025-06-07 11:58:33,938 - server_web - INFO -    Query ID from result: None
2025-06-07 11:58:33,939 - server_web - INFO -    Active queries: ['query_c0c6403db8af_1749272313']
2025-06-07 11:58:33,947 - server_web - INFO -    Legacy result: 0 rows, 0 headers
2025-06-07 11:58:33,948 - server_web - INFO -    Attempting to add to any active query for legacy support
2025-06-07 11:58:33,949 - server_web - INFO -    Adding legacy result to query query_c0c6403db8af_1749272313
2025-06-07 11:58:33,963 - server_web - INFO -    - Total clients: 1
2025-06-07 11:58:33,964 - server_web - INFO -    - Successful: 1
2025-06-07 11:58:33,964 - server_web - INFO -    - Failed: 0
2025-06-07 11:58:33,964 - server_web - INFO -    - Success rate: 100.0%
2025-06-07 11:58:35,122 - server_web - INFO - Getting query results for client_id: all, query_id: query_c0c6403db8af_1749272313
2025-06-07 11:58:35,549 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 11:58:35] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 11:58:37,926 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:58:37
2025-06-07 11:58:37,927 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:58:42,929 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:58:42
2025-06-07 11:58:42,931 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:58:44,175 - server_web - INFO - Getting query results for client_id: all, query_id: query_c0c6403db8af_1749272313
2025-06-07 11:58:45,857 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 11:58:45] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 11:58:47,932 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:58:47
2025-06-07 11:58:47,933 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:58:52,934 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:58:52
2025-06-07 11:58:52,935 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:58:53,230 - server_web - INFO - Getting query results for client_id: all, query_id: query_c0c6403db8af_1749272313
2025-06-07 11:58:55,554 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 11:58:55] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 11:58:57,941 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:58:57
2025-06-07 11:58:57,942 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:59:02,300 - server_web - INFO - Getting query results for client_id: all, query_id: query_c0c6403db8af_1749272313
2025-06-07 11:59:02,944 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:59:02
2025-06-07 11:59:02,945 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:59:02,946 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:59:02,946 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:59:02,949 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:59:06,789 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 11:59:06] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 11:59:07,952 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:59:07
2025-06-07 11:59:07,954 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:59:11,339 - server_web - INFO - Getting query results for client_id: all, query_id: query_c0c6403db8af_1749272313
2025-06-07 11:59:12,956 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:59:12
2025-06-07 11:59:12,957 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:59:16,468 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 11:59:16] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 11:59:17,959 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:59:17
2025-06-07 11:59:17,961 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:59:20,388 - server_web - INFO - Getting query results for client_id: all, query_id: query_c0c6403db8af_1749272313
2025-06-07 11:59:22,963 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:59:22
2025-06-07 11:59:22,964 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:59:26,791 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 11:59:26] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 11:59:27,966 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:59:27
2025-06-07 11:59:27,967 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:59:29,429 - server_web - INFO - Getting query results for client_id: all, query_id: query_c0c6403db8af_1749272313
2025-06-07 11:59:32,968 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:59:32
2025-06-07 11:59:32,970 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:59:32,971 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 11:59:32,971 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 11:59:32,974 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 11:59:36,474 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 11:59:36] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 11:59:37,975 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:59:37
2025-06-07 11:59:37,976 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:59:38,462 - server_web - INFO - Getting query results for client_id: all, query_id: query_c0c6403db8af_1749272313
2025-06-07 11:59:42,977 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:59:42
2025-06-07 11:59:42,978 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:59:46,795 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 11:59:46] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 11:59:47,492 - server_web - INFO - Getting query results for client_id: all, query_id: query_c0c6403db8af_1749272313
2025-06-07 11:59:47,980 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:59:47
2025-06-07 11:59:47,980 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:59:52,981 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:59:52
2025-06-07 11:59:52,982 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 11:59:56,480 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 11:59:56] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 11:59:56,533 - server_web - INFO - Getting query results for client_id: all, query_id: query_c0c6403db8af_1749272313
2025-06-07 11:59:57,984 - server_web - DEBUG - Heartbeat check at 2025-06-07 11:59:57
2025-06-07 11:59:57,985 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:00:02,986 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:00:02
2025-06-07 12:00:02,987 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:00:02,987 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 12:00:02,987 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 12:00:02,988 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 12:00:07,988 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:00:07
2025-06-07 12:00:07,989 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:00:12,990 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:00:12
2025-06-07 12:00:12,991 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:00:17,993 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:00:17
2025-06-07 12:00:17,994 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:00:22,995 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:00:22
2025-06-07 12:00:22,995 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:00:27,996 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:00:27
2025-06-07 12:00:27,997 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:00:32,998 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:00:32
2025-06-07 12:00:32,998 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:00:32,999 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 12:00:32,999 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 12:00:33,002 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 12:00:36,560 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:00:36] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 12:00:38,004 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:00:38
2025-06-07 12:00:38,005 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:00:38,592 - server_web - INFO - Starting query execution query_14c4c3534520_1749272438: SELECT FIRST 1 'Test' FROM RDB$DATABASE...
2025-06-07 12:00:38,594 - server_web - INFO - Connected clients: 1
2025-06-07 12:00:38,594 - server_web - INFO - Target clients: ['client_fdb-client-monitoring_127.0.0.1']
2025-06-07 12:00:43,006 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:00:43
2025-06-07 12:01:39,846 - server_web - INFO - Loaded predefined query: Employee_Overtime with 0 variables
2025-06-07 12:01:39,847 - server_web - INFO - Loaded predefined query: Find_Diff_Job_Date_Overtime with 2 variables
2025-06-07 12:01:39,847 - server_web - INFO - Loaded predefined query: Find_Helper_Hasnt_Veh_No with 0 variables
2025-06-07 12:01:39,847 - server_web - INFO - Loaded predefined query: General_Work_Has_YY with 0 variables
2025-06-07 12:01:39,847 - server_web - INFO - Loaded predefined query: test_dynamic_variables with 3 variables
2025-06-07 12:01:39,853 - server_web - INFO - [DIRECT-DB] Using backup directory: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\server_web\static\backups
2025-06-07 12:01:39,855 - server_web - INFO - Direct database access module loaded successfully
2025-06-07 12:01:39,857 - server_web - INFO - Server running on 0.0.0.0:5555
2025-06-07 12:01:39,857 - server_web - INFO - Starting connection acceptance thread
2025-06-07 12:01:39,857 - server_web - INFO - Starting heartbeat thread
2025-06-07 12:01:39,858 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:01:39
2025-06-07 12:01:39,858 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:01:41,872 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-06-07 12:01:41,872 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-07 12:01:43,798 - server_web - INFO - New connection from ('127.0.0.1', 51131)
2025-06-07 12:01:43,801 - server_web - INFO - Generated stable client ID: client_fdb-client-monitoring_127.0.0.1 for FDB-Client-Monitoring from 127.0.0.1
2025-06-07 12:01:43,801 - server_web - INFO - New client registered: FDB-Client-Monitoring (client_fdb-client-monitoring_127.0.0.1) from ('127.0.0.1', 51131)
2025-06-07 12:01:44,859 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:01:44
2025-06-07 12:01:44,860 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:01:49,860 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:01:49
2025-06-07 12:01:49,862 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:01:54,863 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:01:54
2025-06-07 12:01:54,864 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:01:58,798 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:01:58] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 12:01:59,865 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:01:59
2025-06-07 12:01:59,866 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:02:04,867 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:02:04
2025-06-07 12:02:04,868 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:02:06,859 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:02:06] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 12:02:08,907 - server_web - INFO - Starting query execution query_bb7e8ad752e1_1749272528: SELECT FIRST 1 CURRENT_TIMESTAMP FROM RDB$DATABASE...
2025-06-07 12:02:08,908 - server_web - INFO - Connected clients: 1
2025-06-07 12:02:08,909 - server_web - INFO - Target clients: ['client_fdb-client-monitoring_127.0.0.1']
2025-06-07 12:02:08,959 - server_web - INFO - Query query_bb7e8ad752e1_1749272528 sent to 1 clients, waiting for completion...
2025-06-07 12:02:08,961 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:02:08] "POST /api/query/send HTTP/1.1" 200 -
2025-06-07 12:02:09,194 - server_web - INFO -    Message data keys: ['query', 'description', 'error', 'timestamp', 'status', 'rows', 'headers']
2025-06-07 12:02:09,195 - server_web - INFO -    Query ID from result: None
2025-06-07 12:02:09,196 - server_web - INFO -    Active queries: ['query_bb7e8ad752e1_1749272528']
2025-06-07 12:02:09,202 - server_web - INFO -    Legacy result: 0 rows, 0 headers
2025-06-07 12:02:09,203 - server_web - INFO -    Attempting to add to any active query for legacy support
2025-06-07 12:02:09,203 - server_web - INFO -    Adding legacy result to query query_bb7e8ad752e1_1749272528
2025-06-07 12:02:09,215 - server_web - INFO -    - Total clients: 1
2025-06-07 12:02:09,215 - server_web - INFO -    - Successful: 1
2025-06-07 12:02:09,216 - server_web - INFO -    - Failed: 0
2025-06-07 12:02:09,216 - server_web - INFO -    - Success rate: 100.0%
2025-06-07 12:02:09,870 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:02:09
2025-06-07 12:02:09,871 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:02:10,999 - server_web - INFO - Getting query results for client_id: all, query_id: query_bb7e8ad752e1_1749272528
2025-06-07 12:02:14,872 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:02:14
2025-06-07 12:02:14,874 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:02:14,875 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 31.1 seconds
2025-06-07 12:02:14,876 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 12:02:14,881 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 12:02:19,884 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:02:19
2025-06-07 12:02:19,886 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:02:20,029 - server_web - INFO - Getting query results for client_id: all, query_id: query_bb7e8ad752e1_1749272528
2025-06-07 12:02:24,887 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:02:24
2025-06-07 12:02:24,889 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:02:29,063 - server_web - INFO - Getting query results for client_id: all, query_id: query_bb7e8ad752e1_1749272528
2025-06-07 12:02:29,891 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:02:29
2025-06-07 12:02:29,892 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:02:34,894 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:02:34
2025-06-07 12:02:34,895 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:02:38,117 - server_web - INFO - Getting query results for client_id: all, query_id: query_bb7e8ad752e1_1749272528
2025-06-07 12:02:39,897 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:02:39
2025-06-07 12:02:39,899 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:02:44,901 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:02:44
2025-06-07 12:02:44,902 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:02:44,903 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 12:02:44,904 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 12:02:44,907 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 12:02:47,176 - server_web - INFO - Getting query results for client_id: all, query_id: query_bb7e8ad752e1_1749272528
2025-06-07 12:02:49,909 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:02:49
2025-06-07 12:02:49,910 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:02:54,918 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:02:54
2025-06-07 12:02:54,929 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:02:56,235 - server_web - INFO - Getting query results for client_id: all, query_id: query_bb7e8ad752e1_1749272528
2025-06-07 12:02:58,477 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:02:58] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 12:02:59,940 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:02:59
2025-06-07 12:02:59,942 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:03:04,943 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:03:04
2025-06-07 12:03:04,944 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:03:05,266 - server_web - INFO - Getting query results for client_id: all, query_id: query_bb7e8ad752e1_1749272528
2025-06-07 12:03:09,945 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:03:09
2025-06-07 12:03:09,945 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:03:14,299 - server_web - INFO - Getting query results for client_id: all, query_id: query_bb7e8ad752e1_1749272528
2025-06-07 12:03:14,947 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:03:14
2025-06-07 12:03:14,948 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:03:14,949 - server_web - DEBUG - Client FDB-Client-Monitoring silent for 30.0 seconds
2025-06-07 12:03:14,949 - server_web - DEBUG - Sending ping to FDB-Client-Monitoring (attempt 1/10)
2025-06-07 12:03:14,953 - server_web - DEBUG - Sent heartbeat to client FDB-Client-Monitoring successfully
2025-06-07 12:03:19,955 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:03:19
2025-06-07 12:03:19,956 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:03:21,328 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:03:21] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 12:03:23,341 - server_web - INFO - Getting query results for client_id: all, query_id: query_bb7e8ad752e1_1749272528
2025-06-07 12:03:24,957 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:03:24
2025-06-07 12:03:24,959 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:03:25,559 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:03:25] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 12:03:29,765 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:03:29] "GET /query HTTP/1.1" 200 -
2025-06-07 12:03:29,960 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:03:29
2025-06-07 12:03:29,962 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:03:30,903 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:03:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-07 12:03:30,954 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:03:30] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-06-07 12:03:31,332 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:03:31] "GET /api/query/get/Find_Diff_Job_Date_Overtime HTTP/1.1" 200 -
2025-06-07 12:03:31,604 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:03:31] "GET /api/query/get/test_dynamic_variables HTTP/1.1" 200 -
2025-06-07 12:03:32,380 - server_web - INFO - Getting query results for client_id: all, query_id: query_bb7e8ad752e1_1749272528
2025-06-07 12:03:34,964 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:03:34
2025-06-07 12:03:34,965 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:03:39,965 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:03:39
2025-06-07 12:03:39,966 - server_web - DEBUG - Clients status: 1 connected, 0 disconnected, 1 total
2025-06-07 12:03:43,590 - server_web - INFO - Starting query execution query_1d29eb440682_1749272623: SELECT 
  o.EMPID AS KaryawanID,
  o.ID AS OvertimeID,
  o.INPDATE AS TanggalOvertime,
  o.HOURS AS ...
2025-06-07 12:03:43,591 - server_web - INFO - Connected clients: 1
2025-06-07 12:03:43,592 - server_web - INFO - Target clients: ['client_fdb-client-monitoring_127.0.0.1']
2025-06-07 12:03:44,967 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:03:44
2025-06-07 12:09:09,357 - server_web - INFO - Loaded predefined query: Employee_Overtime with 0 variables
2025-06-07 12:09:09,357 - server_web - INFO - Loaded predefined query: Find_Diff_Job_Date_Overtime with 2 variables
2025-06-07 12:09:09,358 - server_web - INFO - Loaded predefined query: Find_Helper_Hasnt_Veh_No with 0 variables
2025-06-07 12:09:09,358 - server_web - INFO - Loaded predefined query: General_Work_Has_YY with 0 variables
2025-06-07 12:09:09,359 - server_web - INFO - Loaded predefined query: test_dynamic_variables with 3 variables
2025-06-07 12:09:09,365 - server_web - INFO - [DIRECT-DB] Using backup directory: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\server_web\static\backups
2025-06-07 12:09:09,367 - server_web - INFO - Direct database access module loaded successfully
2025-06-07 12:09:09,369 - server_web - INFO - Server running on 0.0.0.0:5555
2025-06-07 12:09:09,370 - server_web - INFO - Starting connection acceptance thread
2025-06-07 12:09:09,370 - server_web - INFO - Starting heartbeat thread
2025-06-07 12:09:09,370 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:09:09
2025-06-07 12:09:09,371 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:09:11,409 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-06-07 12:09:11,410 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-07 12:09:14,372 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:09:14
2025-06-07 12:09:14,373 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:09:19,374 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:09:19
2025-06-07 12:09:19,374 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:09:21,571 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:09:21] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 12:09:23,610 - server_web - INFO - Starting query execution query_74716831e84a_1749272963: SELECT FIRST 1 CURRENT_TIMESTAMP FROM RDB$DATABASE...
2025-06-07 12:09:23,612 - server_web - WARNING - No clients connected
2025-06-07 12:09:23,613 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:09:23] "POST /api/query/send HTTP/1.1" 200 -
2025-06-07 12:09:24,375 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:09:24
2025-06-07 12:09:24,376 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:09:25,672 - server_web - INFO - Getting query results for client_id: all, query_id: latest
2025-06-07 12:09:25,673 - server_web - INFO - Legacy mode - getting results from 0 connected clients
2025-06-07 12:09:25,674 - server_web - INFO - Legacy mode returning 0 results with 0 total rows and 0 unique headers
2025-06-07 12:09:25,675 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:09:25] "GET /api/query/results HTTP/1.1" 200 -
2025-06-07 12:09:29,378 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:09:29
2025-06-07 12:09:29,380 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:09:29,714 - server_web - INFO - Getting query results for client_id: all, query_id: latest
2025-06-07 12:09:29,716 - server_web - INFO - Legacy mode - getting results from 0 connected clients
2025-06-07 12:09:29,716 - server_web - INFO - Legacy mode returning 0 results with 0 total rows and 0 unique headers
2025-06-07 12:09:29,717 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:09:29] "GET /api/query/results HTTP/1.1" 200 -
2025-06-07 12:09:33,760 - server_web - INFO - Getting query results for client_id: all, query_id: latest
2025-06-07 12:09:33,761 - server_web - INFO - Legacy mode - getting results from 0 connected clients
2025-06-07 12:09:33,762 - server_web - INFO - Legacy mode returning 0 results with 0 total rows and 0 unique headers
2025-06-07 12:09:33,763 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:09:33] "GET /api/query/results HTTP/1.1" 200 -
2025-06-07 12:09:34,382 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:09:34
2025-06-07 12:09:34,383 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:09:37,814 - server_web - INFO - Getting query results for client_id: all, query_id: latest
2025-06-07 12:09:37,816 - server_web - INFO - Legacy mode - getting results from 0 connected clients
2025-06-07 12:09:37,817 - server_web - INFO - Legacy mode returning 0 results with 0 total rows and 0 unique headers
2025-06-07 12:09:37,818 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:09:37] "GET /api/query/results HTTP/1.1" 200 -
2025-06-07 12:09:39,385 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:09:39
2025-06-07 12:09:39,386 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:09:41,889 - server_web - INFO - Getting query results for client_id: all, query_id: latest
2025-06-07 12:09:41,890 - server_web - INFO - Legacy mode - getting results from 0 connected clients
2025-06-07 12:09:41,891 - server_web - INFO - Legacy mode returning 0 results with 0 total rows and 0 unique headers
2025-06-07 12:09:41,892 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:09:41] "GET /api/query/results HTTP/1.1" 200 -
2025-06-07 12:09:44,387 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:09:44
2025-06-07 12:09:44,389 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:09:45,941 - server_web - INFO - Getting query results for client_id: all, query_id: latest
2025-06-07 12:09:45,942 - server_web - INFO - Legacy mode - getting results from 0 connected clients
2025-06-07 12:09:45,943 - server_web - INFO - Legacy mode returning 0 results with 0 total rows and 0 unique headers
2025-06-07 12:09:45,944 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:09:45] "GET /api/query/results HTTP/1.1" 200 -
2025-06-07 12:09:49,389 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:09:49
2025-06-07 12:09:49,390 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:09:49,988 - server_web - INFO - Getting query results for client_id: all, query_id: latest
2025-06-07 12:09:49,988 - server_web - INFO - Legacy mode - getting results from 0 connected clients
2025-06-07 12:09:49,989 - server_web - INFO - Legacy mode returning 0 results with 0 total rows and 0 unique headers
2025-06-07 12:09:49,989 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:09:49] "GET /api/query/results HTTP/1.1" 200 -
2025-06-07 12:09:54,046 - server_web - INFO - Getting query results for client_id: all, query_id: latest
2025-06-07 12:09:54,046 - server_web - INFO - Legacy mode - getting results from 0 connected clients
2025-06-07 12:09:54,047 - server_web - INFO - Legacy mode returning 0 results with 0 total rows and 0 unique headers
2025-06-07 12:09:54,047 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:09:54] "GET /api/query/results HTTP/1.1" 200 -
2025-06-07 12:09:54,391 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:09:54
2025-06-07 12:09:54,391 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:09:58,065 - server_web - INFO - Getting query results for client_id: all, query_id: latest
2025-06-07 12:09:58,065 - server_web - INFO - Legacy mode - getting results from 0 connected clients
2025-06-07 12:09:58,066 - server_web - INFO - Legacy mode returning 0 results with 0 total rows and 0 unique headers
2025-06-07 12:09:58,067 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:09:58] "GET /api/query/results HTTP/1.1" 200 -
2025-06-07 12:09:59,392 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:09:59
2025-06-07 12:09:59,393 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:10:02,116 - server_web - INFO - Getting query results for client_id: all, query_id: latest
2025-06-07 12:10:02,117 - server_web - INFO - Legacy mode - getting results from 0 connected clients
2025-06-07 12:10:02,117 - server_web - INFO - Legacy mode returning 0 results with 0 total rows and 0 unique headers
2025-06-07 12:10:02,117 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:10:02] "GET /api/query/results HTTP/1.1" 200 -
2025-06-07 12:10:04,395 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:10:04
2025-06-07 12:10:04,396 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:10:04,715 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:10:04] "GET /api/clients HTTP/1.1" 200 -
2025-06-07 12:10:06,744 - server_web - INFO - Starting query execution query_4727615afea9_1749273006: SELECT FIRST 1 CURRENT_TIMESTAMP FROM RDB$DATABASE...
2025-06-07 12:10:06,744 - server_web - WARNING - No clients connected
2025-06-07 12:10:06,745 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:10:06] "POST /api/query/send HTTP/1.1" 200 -
2025-06-07 12:10:08,781 - server_web - INFO - Getting query results for client_id: all, query_id: latest
2025-06-07 12:10:08,782 - server_web - INFO - Legacy mode - getting results from 0 connected clients
2025-06-07 12:10:08,782 - server_web - INFO - Legacy mode returning 0 results with 0 total rows and 0 unique headers
2025-06-07 12:10:08,784 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:10:08] "GET /api/query/results HTTP/1.1" 200 -
2025-06-07 12:10:09,397 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:10:09
2025-06-07 12:10:09,398 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:10:12,846 - server_web - INFO - Getting query results for client_id: all, query_id: latest
2025-06-07 12:10:12,848 - server_web - INFO - Legacy mode - getting results from 0 connected clients
2025-06-07 12:10:12,848 - server_web - INFO - Legacy mode returning 0 results with 0 total rows and 0 unique headers
2025-06-07 12:10:12,850 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:10:12] "GET /api/query/results HTTP/1.1" 200 -
2025-06-07 12:10:14,399 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:10:14
2025-06-07 12:10:14,400 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:10:16,873 - server_web - INFO - Getting query results for client_id: all, query_id: latest
2025-06-07 12:10:16,873 - server_web - INFO - Legacy mode - getting results from 0 connected clients
2025-06-07 12:10:16,873 - server_web - INFO - Legacy mode returning 0 results with 0 total rows and 0 unique headers
2025-06-07 12:10:16,874 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:10:16] "GET /api/query/results HTTP/1.1" 200 -
2025-06-07 12:10:19,401 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:10:19
2025-06-07 12:10:19,402 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:10:20,907 - server_web - INFO - Getting query results for client_id: all, query_id: latest
2025-06-07 12:10:20,908 - server_web - INFO - Legacy mode - getting results from 0 connected clients
2025-06-07 12:10:20,909 - server_web - INFO - Legacy mode returning 0 results with 0 total rows and 0 unique headers
2025-06-07 12:10:20,910 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:10:20] "GET /api/query/results HTTP/1.1" 200 -
2025-06-07 12:10:24,403 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:10:24
2025-06-07 12:10:24,405 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:10:24,951 - server_web - INFO - Getting query results for client_id: all, query_id: latest
2025-06-07 12:10:24,952 - server_web - INFO - Legacy mode - getting results from 0 connected clients
2025-06-07 12:10:24,952 - server_web - INFO - Legacy mode returning 0 results with 0 total rows and 0 unique headers
2025-06-07 12:10:24,953 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:10:24] "GET /api/query/results HTTP/1.1" 200 -
2025-06-07 12:10:28,993 - server_web - INFO - Getting query results for client_id: all, query_id: latest
2025-06-07 12:10:28,994 - server_web - INFO - Legacy mode - getting results from 0 connected clients
2025-06-07 12:10:28,994 - server_web - INFO - Legacy mode returning 0 results with 0 total rows and 0 unique headers
2025-06-07 12:10:28,995 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:10:28] "GET /api/query/results HTTP/1.1" 200 -
2025-06-07 12:10:29,407 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:10:29
2025-06-07 12:10:29,408 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:10:33,021 - server_web - INFO - Getting query results for client_id: all, query_id: latest
2025-06-07 12:10:33,022 - server_web - INFO - Legacy mode - getting results from 0 connected clients
2025-06-07 12:10:33,022 - server_web - INFO - Legacy mode returning 0 results with 0 total rows and 0 unique headers
2025-06-07 12:10:33,023 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:10:33] "GET /api/query/results HTTP/1.1" 200 -
2025-06-07 12:10:34,411 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:10:34
2025-06-07 12:10:34,411 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:10:37,057 - server_web - INFO - Getting query results for client_id: all, query_id: latest
2025-06-07 12:10:37,057 - server_web - INFO - Legacy mode - getting results from 0 connected clients
2025-06-07 12:10:37,057 - server_web - INFO - Legacy mode returning 0 results with 0 total rows and 0 unique headers
2025-06-07 12:10:37,058 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:10:37] "GET /api/query/results HTTP/1.1" 200 -
2025-06-07 12:10:39,412 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:10:39
2025-06-07 12:10:39,414 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:10:41,092 - server_web - INFO - Getting query results for client_id: all, query_id: latest
2025-06-07 12:10:41,093 - server_web - INFO - Legacy mode - getting results from 0 connected clients
2025-06-07 12:10:41,094 - server_web - INFO - Legacy mode returning 0 results with 0 total rows and 0 unique headers
2025-06-07 12:10:41,095 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:10:41] "GET /api/query/results HTTP/1.1" 200 -
2025-06-07 12:10:44,415 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:10:44
2025-06-07 12:10:44,416 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:10:45,120 - server_web - INFO - Getting query results for client_id: all, query_id: latest
2025-06-07 12:10:45,121 - server_web - INFO - Legacy mode - getting results from 0 connected clients
2025-06-07 12:10:45,121 - server_web - INFO - Legacy mode returning 0 results with 0 total rows and 0 unique headers
2025-06-07 12:10:45,121 - werkzeug - INFO - 127.0.0.1 - - [07/Jun/2025 12:10:45] "GET /api/query/results HTTP/1.1" 200 -
2025-06-07 12:10:49,417 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:10:49
2025-06-07 12:10:49,418 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:10:54,419 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:10:54
2025-06-07 12:10:54,420 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:10:59,421 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:10:59
2025-06-07 12:10:59,422 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:11:04,423 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:11:04
2025-06-07 12:11:04,423 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:11:09,424 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:11:09
2025-06-07 12:11:09,425 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:11:14,426 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:11:14
2025-06-07 12:11:14,427 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:11:19,428 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:11:19
2025-06-07 12:11:19,429 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:11:24,429 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:11:24
2025-06-07 12:11:24,430 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:11:29,431 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:11:29
2025-06-07 12:11:29,433 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:11:34,434 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:11:34
2025-06-07 12:11:34,434 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:11:39,452 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:11:39
2025-06-07 12:11:39,455 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:11:44,457 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:11:44
2025-06-07 12:11:44,457 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:11:49,458 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:11:49
2025-06-07 12:11:49,458 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:11:54,460 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:11:54
2025-06-07 12:11:54,461 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:11:59,462 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:11:59
2025-06-07 12:11:59,463 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:12:04,463 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:12:04
2025-06-07 12:12:04,464 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:12:09,465 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:12:09
2025-06-07 12:12:09,467 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:12:14,467 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:12:14
2025-06-07 12:12:14,468 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:12:19,469 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:12:19
2025-06-07 12:12:19,470 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:12:24,472 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:12:24
2025-06-07 12:12:24,474 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:12:29,476 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:12:29
2025-06-07 12:12:29,478 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:12:34,480 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:12:34
2025-06-07 12:12:34,480 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:12:39,481 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:12:39
2025-06-07 12:12:39,482 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:12:44,483 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:12:44
2025-06-07 12:12:44,485 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:12:49,486 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:12:49
2025-06-07 12:12:49,486 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:12:54,487 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:12:54
2025-06-07 12:12:54,487 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:12:59,488 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:12:59
2025-06-07 12:12:59,488 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:13:04,489 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:13:04
2025-06-07 12:13:04,490 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:13:09,490 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:13:09
2025-06-07 12:13:09,491 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:13:14,492 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:13:14
2025-06-07 12:13:14,493 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:13:19,493 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:13:19
2025-06-07 12:13:19,494 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:13:24,494 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:13:24
2025-06-07 12:13:24,495 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:13:29,495 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:13:29
2025-06-07 12:13:29,496 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:13:34,497 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:13:34
2025-06-07 12:13:34,498 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:13:39,499 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:13:39
2025-06-07 12:13:39,499 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:13:44,500 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:13:44
2025-06-07 12:13:44,501 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
2025-06-07 12:13:49,502 - server_web - DEBUG - Heartbeat check at 2025-06-07 12:13:49
2025-06-07 12:13:49,503 - server_web - DEBUG - Clients status: 0 connected, 0 disconnected, 0 total
