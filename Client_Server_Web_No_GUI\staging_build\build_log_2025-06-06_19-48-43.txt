IFESS Client Suite - Comprehensive Build Log 
Build started: 06/06/2025 19:48:43,19 
================================================ 
 
Current directory: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build 
[1/7] Performing pre-build checks... 
Python 3.13.3
PyInstaller command line access verified 
WARNING: UPX not found - compression will be disabled 
Checking source files... 
Found: ifess_client_hidden.py 
Found: ifess_config_gui.py 
Pre-build checks completed successfully 
[2/7] Cleaning build environment... 
Removing old dist directory... 
Removing old build directory... 
Build environment cleaned 
[3/7] Checking and installing dependencies... 
Installing/updating Python dependencies... 
Installing: google-api-python-client 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: google-api-python-client in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (2.171.0)
Requirement already satisfied: httplib2<1.0.0,>=0.19.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-python-client) (0.22.0)
Requirement already satisfied: google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-python-client) (2.40.3)
Requirement already satisfied: google-auth-httplib2<1.0.0,>=0.2.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-python-client) (0.2.0)
Requirement already satisfied: google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-python-client) (2.24.2)
Requirement already satisfied: uritemplate<5,>=3.0.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-python-client) (4.1.1)
Requirement already satisfied: googleapis-common-protos<2.0.0,>=1.56.2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (1.70.0)
Requirement already satisfied: protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<7.0.0,>=3.19.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (5.29.5)
Requirement already satisfied: proto-plus<2.0.0,>=1.22.3 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (1.26.1)
Requirement already satisfied: requests<3.0.0,>=2.18.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (2.32.3)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0->google-api-python-client) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0->google-api-python-client) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0->google-api-python-client) (4.9)
Requirement already satisfied: pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from httplib2<1.0.0,>=0.19.0->google-api-python-client) (3.2.1)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (1.26.20)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (2025.1.31)
Requirement already satisfied: pyasn1>=0.1.3 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from rsa<5,>=3.1.4->google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0->google-api-python-client) (0.6.1)
Installing: google-auth 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: google-auth in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (2.40.3)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth) (4.9)
Requirement already satisfied: pyasn1>=0.1.3 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from rsa<5,>=3.1.4->google-auth) (0.6.1)
Installing: google-auth-oauthlib 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: google-auth-oauthlib in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (1.2.2)
Requirement already satisfied: google-auth>=2.15.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth-oauthlib) (2.40.3)
Requirement already satisfied: requests-oauthlib>=0.7.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth-oauthlib) (2.0.0)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth>=2.15.0->google-auth-oauthlib) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth>=2.15.0->google-auth-oauthlib) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth>=2.15.0->google-auth-oauthlib) (4.9)
Requirement already satisfied: pyasn1>=0.1.3 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from rsa<5,>=3.1.4->google-auth>=2.15.0->google-auth-oauthlib) (0.6.1)
Requirement already satisfied: oauthlib>=3.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests-oauthlib>=0.7.0->google-auth-oauthlib) (3.2.2)
Requirement already satisfied: requests>=2.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests-oauthlib>=0.7.0->google-auth-oauthlib) (2.32.3)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib) (1.26.20)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib) (2025.1.31)
Installing: google-auth-httplib2 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: google-auth-httplib2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (0.2.0)
Requirement already satisfied: google-auth in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth-httplib2) (2.40.3)
Requirement already satisfied: httplib2>=0.19.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth-httplib2) (0.22.0)
Requirement already satisfied: pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from httplib2>=0.19.0->google-auth-httplib2) (3.2.1)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth->google-auth-httplib2) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth->google-auth-httplib2) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth->google-auth-httplib2) (4.9)
Requirement already satisfied: pyasn1>=0.1.3 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from rsa<5,>=3.1.4->google-auth->google-auth-httplib2) (0.6.1)
Installing: requests 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: requests in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (2.32.3)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests) (1.26.20)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests) (2025.1.31)
Installing: fdb 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: fdb in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (2.0.3)
Requirement already satisfied: firebird-base~=2.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from fdb) (2.0.2)
Requirement already satisfied: python-dateutil~=2.8 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from fdb) (2.9.0.post0)
Requirement already satisfied: protobuf~=5.29 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from firebird-base~=2.0->fdb) (5.29.5)
Requirement already satisfied: six>=1.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from python-dateutil~=2.8->fdb) (1.17.0)
Installing: schedule 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: schedule in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (1.2.2)
Dependencies check completed 
[4/7] Building IFESS Hidden Client... 
Building ifess_client_hidden.exe... 
226 INFO: PyInstaller: 6.14.0, contrib hooks: 2025.4
226 INFO: Python: 3.13.3
278 INFO: Platform: Windows-11-10.0.26100-SP0
278 INFO: Python environment: C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0
296 INFO: Module search paths (PYTHONPATH):
['D:\\Gawean Rebinmas\\Monitoring '
 'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\python313.zip',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\win32',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\win32\\lib',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Pythonwin',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\site-packages',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\setuptools\\_vendor',
 'D:\\Gawean Rebinmas\\Monitoring '
 'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build',
 'D:\\Gawean Rebinmas\\Monitoring '
 'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build']
2534 INFO: Appending 'datas' from .spec
2535 INFO: checking Analysis
2535 INFO: Building Analysis because Analysis-00.toc is non existent
2535 INFO: Running Analysis Analysis-00.toc
2535 INFO: Target bytecode optimization level: 0
2535 INFO: Initializing module dependency graph...
2537 INFO: Initializing module graph hook caches...
2584 INFO: Analyzing modules for base_library.zip ...
3989 INFO: Processing standard module hook 'hook-heapq.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
4078 INFO: Processing standard module hook 'hook-encodings.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
5785 INFO: Processing standard module hook 'hook-pickle.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
6827 INFO: Caching module dependency graph...
6861 INFO: Looking for Python shared library...
6862 INFO: Using Python shared library: C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\python313.dll
6862 INFO: Analyzing D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ifess_client_hidden.py
6992 INFO: Processing standard module hook 'hook-platform.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
7084 INFO: Processing standard module hook 'hook-pytz.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
7276 INFO: Processing standard module hook 'hook-difflib.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
7536 INFO: Processing standard module hook 'hook-sysconfig.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
7956 INFO: Processing standard module hook 'hook-multiprocessing.util.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
8029 INFO: Processing standard module hook 'hook-xml.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
8882 INFO: Processing standard module hook 'hook-google.api_core.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
8956 INFO: Processing standard module hook 'hook-cryptography.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
10147 INFO: hook-cryptography: cryptography does not seem to be using dynamically linked OpenSSL.
10799 INFO: Processing standard module hook 'hook-urllib3.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
10844 INFO: Processing pre-safe-import-module hook 'hook-urllib3.packages.six.moves.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
11476 INFO: Processing standard module hook 'hook-charset_normalizer.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
11570 INFO: Processing pre-safe-import-module hook 'hook-typing_extensions.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
11584 INFO: SetuptoolsInfo: initializing cached setuptools info...
15067 INFO: Processing standard module hook 'hook-certifi.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
15315 INFO: Processing standard module hook 'hook-pycparser.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
15643 INFO: Processing standard module hook 'hook-setuptools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
15655 INFO: Processing pre-safe-import-module hook 'hook-distutils.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
15692 INFO: Processing pre-safe-import-module hook 'hook-jaraco.functools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
15692 INFO: Setuptools: 'jaraco.functools' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.jaraco.functools'!
15700 INFO: Processing pre-safe-import-module hook 'hook-more_itertools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
15711 INFO: Setuptools: 'more_itertools' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.more_itertools'!
15829 INFO: Processing pre-safe-import-module hook 'hook-packaging.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
15937 INFO: Processing pre-safe-import-module hook 'hook-jaraco.text.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
15938 INFO: Setuptools: 'jaraco.text' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.jaraco.text'!
15946 INFO: Processing standard module hook 'hook-setuptools._vendor.jaraco.text.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
15949 INFO: Processing pre-safe-import-module hook 'hook-importlib_resources.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
15950 INFO: Processing pre-safe-import-module hook 'hook-jaraco.context.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
15961 INFO: Setuptools: 'jaraco.context' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.jaraco.context'!
15970 INFO: Processing pre-safe-import-module hook 'hook-backports.tarfile.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
15970 INFO: Setuptools: 'backports.tarfile' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.backports.tarfile'!
16036 INFO: Processing standard module hook 'hook-backports.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
16101 INFO: Processing pre-safe-import-module hook 'hook-importlib_metadata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
16112 INFO: Setuptools: 'importlib_metadata' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.importlib_metadata'!
16134 INFO: Processing standard module hook 'hook-setuptools._vendor.importlib_metadata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
16148 INFO: Processing pre-safe-import-module hook 'hook-zipp.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
16149 INFO: Setuptools: 'zipp' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.zipp'!
16403 INFO: Processing pre-safe-import-module hook 'hook-tomli.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
16403 INFO: Setuptools: 'tomli' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.tomli'!
16939 INFO: Processing standard module hook 'hook-pkg_resources.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
17366 INFO: Processing pre-safe-import-module hook 'hook-platformdirs.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
17381 INFO: Processing standard module hook 'hook-platformdirs.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
17574 INFO: Processing pre-safe-import-module hook 'hook-wheel.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
18298 INFO: Processing standard module hook 'hook-httplib2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
18587 INFO: Processing standard module hook 'hook-jinja2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
19073 INFO: Processing standard module hook 'hook-googleapiclient.model.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
19588 INFO: Processing standard module hook 'hook-dateutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
19707 INFO: Processing pre-safe-import-module hook 'hook-six.moves.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
19903 INFO: Analyzing hidden import 'sqlite3'
19906 INFO: Processing standard module hook 'hook-sqlite3.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
20347 INFO: Analyzing hidden import 'fdb'
20803 INFO: Analyzing hidden import 'firebirdsql'
20949 INFO: Processing standard module hook 'hook-zoneinfo.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
21010 INFO: Processing standard module hook 'hook-Crypto.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
21746 INFO: Analyzing hidden import 'mega'
21951 INFO: Processing module hooks (post-graph stage)...
22079 INFO: Processing standard module hook 'hook-tzdata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
22601 INFO: Performing binary vs. data reclassification (1846 entries)
23070 INFO: Looking for ctypes DLLs
23186 INFO: Analyzing run-time hooks ...
23190 INFO: Including run-time hook 'pyi_rth_inspect.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
23197 INFO: Including run-time hook 'pyi_rth_pkgutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
23199 INFO: Including run-time hook 'pyi_rth_multiprocessing.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
23201 INFO: Including run-time hook 'pyi_rth_setuptools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
23202 INFO: Including run-time hook 'pyi_rth_pkgres.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
23206 INFO: Including run-time hook 'pyi_rth_cryptography_openssl.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\rthooks'
23257 INFO: Creating base_library.zip...
23297 INFO: Looking for dynamic libraries
24227 INFO: Extra DLL search directories (AddDllDirectory): []
24227 INFO: Extra DLL search directories (PATH): []
24758 INFO: Warnings written to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\build\ifess_client_hidden_comprehensive\warn-ifess_client_hidden_comprehensive.txt
24809 INFO: Graph cross-reference written to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\build\ifess_client_hidden_comprehensive\xref-ifess_client_hidden_comprehensive.html
24874 INFO: checking PYZ
24874 INFO: Building PYZ because PYZ-00.toc is non existent
24874 INFO: Building PYZ (ZlibArchive) D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\build\ifess_client_hidden_comprehensive\PYZ-00.pyz
25890 INFO: Building PYZ (ZlibArchive) D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\build\ifess_client_hidden_comprehensive\PYZ-00.pyz completed successfully.
25930 INFO: checking PKG
25930 INFO: Building PKG because PKG-00.toc is non existent
25930 INFO: Building PKG (CArchive) ifess_client_hidden.pkg
32330 INFO: Building PKG (CArchive) ifess_client_hidden.pkg completed successfully.
32359 INFO: Bootloader C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\bootloader\Windows-64bit-intel\runw.exe
32359 INFO: checking EXE
32359 INFO: Building EXE because EXE-00.toc is non existent
32359 INFO: Building EXE from EXE-00.toc
Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\__main__.py", line 321, in <module>
    run()
    ~~~^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\__main__.py", line 215, in run
    run_build(pyi_config, spec_file, **vars(args))
    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\__main__.py", line 70, in run_build
    PyInstaller.building.build_main.main(pyi_config, spec_file, **kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\building\build_main.py", line 1282, in main
    build(specfile, distpath, workpath, clean_build)
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\building\build_main.py", line 1220, in build
    exec(code, spec_namespace)
    ~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "ifess_client_hidden_comprehensive.spec", line 106, in <module>
    exe = EXE(
        pyz,
    ...<17 lines>...
        icon=str(PROJECT_ROOT / 'MAINICON.ico') if (PROJECT_ROOT / 'MAINICON.ico').exists() else None,
    )
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\building\api.py", line 678, in __init__
    self.__postinit__()
    ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\building\datastruct.py", line 184, in __postinit__
    self.assemble()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\building\api.py", line 772, in assemble
    os.remove(self.name)
    ~~~~~~~~~^^^^^^^^^^^
PermissionError: [WinError 5] Access is denied: 'D:\\Gawean Rebinmas\\Monitoring Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\dist\\ifess_client_hidden.exe'
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config_oauth_tokens.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_backup.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_oauth_simple.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_module.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_simple.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_oauth.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_secret.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_secrets.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\common -> common
ERROR: Failed to build ifess_client_hidden.exe 
[5/7] Skipping debug client build (using batch file for debugging)... 
Debug functionality will be provided by debug_hidden_client.bat 
[6/7] Building IFESS Config GUI... 
Building ifess_config_gui.exe... 
198 INFO: PyInstaller: 6.14.0, contrib hooks: 2025.4
198 INFO: Python: 3.13.3
242 INFO: Platform: Windows-11-10.0.26100-SP0
242 INFO: Python environment: C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0
249 INFO: Module search paths (PYTHONPATH):
['D:\\Gawean Rebinmas\\Monitoring '
 'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\python313.zip',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\win32',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\win32\\lib',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Pythonwin',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\site-packages',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\setuptools\\_vendor',
 'D:\\Gawean Rebinmas\\Monitoring '
 'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build',
 'D:\\Gawean Rebinmas\\Monitoring '
 'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build']
718 INFO: Appending 'datas' from .spec
719 INFO: checking Analysis
719 INFO: Building Analysis because Analysis-00.toc is non existent
719 INFO: Running Analysis Analysis-00.toc
719 INFO: Target bytecode optimization level: 0
719 INFO: Initializing module dependency graph...
720 INFO: Initializing module graph hook caches...
730 INFO: Analyzing modules for base_library.zip ...
2066 INFO: Processing standard module hook 'hook-heapq.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
2273 INFO: Processing standard module hook 'hook-encodings.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
3645 INFO: Processing standard module hook 'hook-pickle.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
4734 INFO: Caching module dependency graph...
4767 INFO: Looking for Python shared library...
4767 INFO: Using Python shared library: C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\python313.dll
4767 INFO: Analyzing D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ifess_config_gui.py
4802 INFO: Processing pre-find-module-path hook 'hook-tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
4806 INFO: TclTkInfo: initializing cached Tcl/Tk info...
5025 INFO: Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
5135 INFO: Processing standard module hook 'hook-platform.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
5238 INFO: Processing standard module hook 'hook-urllib3.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
5267 INFO: Processing pre-safe-import-module hook 'hook-urllib3.packages.six.moves.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
5953 INFO: Processing standard module hook 'hook-charset_normalizer.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
6024 INFO: Processing pre-safe-import-module hook 'hook-typing_extensions.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
6025 INFO: SetuptoolsInfo: initializing cached setuptools info...
9369 INFO: Processing standard module hook 'hook-cryptography.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
10445 INFO: hook-cryptography: cryptography does not seem to be using dynamically linked OpenSSL.
10680 INFO: Processing standard module hook 'hook-certifi.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
11145 INFO: Processing standard module hook 'hook-difflib.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
11373 INFO: Processing standard module hook 'hook-sysconfig.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
11691 INFO: Processing standard module hook 'hook-multiprocessing.util.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
11751 INFO: Processing standard module hook 'hook-xml.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
12584 INFO: Processing standard module hook 'hook-pycparser.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
12858 INFO: Processing standard module hook 'hook-setuptools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
12868 INFO: Processing pre-safe-import-module hook 'hook-distutils.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
12891 INFO: Processing pre-safe-import-module hook 'hook-jaraco.functools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
12891 INFO: Setuptools: 'jaraco.functools' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.jaraco.functools'!
12898 INFO: Processing pre-safe-import-module hook 'hook-more_itertools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
12899 INFO: Setuptools: 'more_itertools' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.more_itertools'!
13008 INFO: Processing pre-safe-import-module hook 'hook-packaging.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
13088 INFO: Processing pre-safe-import-module hook 'hook-jaraco.text.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
13088 INFO: Setuptools: 'jaraco.text' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.jaraco.text'!
13096 INFO: Processing standard module hook 'hook-setuptools._vendor.jaraco.text.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
13097 INFO: Processing pre-safe-import-module hook 'hook-importlib_resources.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
13097 INFO: Processing pre-safe-import-module hook 'hook-jaraco.context.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
13098 INFO: Setuptools: 'jaraco.context' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.jaraco.context'!
13104 INFO: Processing pre-safe-import-module hook 'hook-backports.tarfile.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
13104 INFO: Setuptools: 'backports.tarfile' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.backports.tarfile'!
13172 INFO: Processing standard module hook 'hook-backports.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
13230 INFO: Processing pre-safe-import-module hook 'hook-importlib_metadata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
13230 INFO: Setuptools: 'importlib_metadata' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.importlib_metadata'!
13249 INFO: Processing standard module hook 'hook-setuptools._vendor.importlib_metadata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
13251 INFO: Processing pre-safe-import-module hook 'hook-zipp.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
13251 INFO: Setuptools: 'zipp' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.zipp'!
13500 INFO: Processing pre-safe-import-module hook 'hook-tomli.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
13501 INFO: Setuptools: 'tomli' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.tomli'!
14017 INFO: Processing standard module hook 'hook-pkg_resources.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
14458 INFO: Processing pre-safe-import-module hook 'hook-platformdirs.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
14465 INFO: Processing standard module hook 'hook-platformdirs.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
14623 INFO: Processing pre-safe-import-module hook 'hook-wheel.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
15216 INFO: Processing standard module hook 'hook-google.api_core.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
15258 INFO: Processing standard module hook 'hook-httplib2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
15546 INFO: Processing standard module hook 'hook-jinja2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
15970 INFO: Processing standard module hook 'hook-googleapiclient.model.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
16197 INFO: Processing standard module hook 'hook-dateutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
16312 INFO: Processing pre-safe-import-module hook 'hook-six.moves.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
16498 INFO: Processing standard module hook 'hook-Crypto.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
17247 INFO: Analyzing hidden import 'tkinter.scrolledtext'
17259 INFO: Analyzing hidden import 'tkinter.font'
17267 INFO: Analyzing hidden import 'fdb'
17683 INFO: Analyzing hidden import 'firebirdsql'
17817 INFO: Processing standard module hook 'hook-zoneinfo.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
17933 INFO: Analyzing hidden import 'google_auth_oauthlib'
18141 INFO: Processing module hooks (post-graph stage)...
18247 INFO: Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
18248 INFO: Processing standard module hook 'hook-tzdata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
18788 INFO: Performing binary vs. data reclassification (2168 entries)
19094 INFO: Looking for ctypes DLLs
19209 INFO: Analyzing run-time hooks ...
19212 INFO: Including run-time hook 'pyi_rth_inspect.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
19214 INFO: Including run-time hook 'pyi_rth_pkgutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
19216 INFO: Including run-time hook 'pyi_rth_multiprocessing.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
19219 INFO: Including run-time hook 'pyi_rth_setuptools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
19220 INFO: Including run-time hook 'pyi_rth_pkgres.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
19223 INFO: Including run-time hook 'pyi_rth_cryptography_openssl.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\rthooks'
19225 INFO: Including run-time hook 'pyi_rth__tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
19277 INFO: Creating base_library.zip...
19318 INFO: Looking for dynamic libraries
20194 INFO: Extra DLL search directories (AddDllDirectory): []
20194 INFO: Extra DLL search directories (PATH): []
20672 INFO: Warnings written to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\build\ifess_config_gui_comprehensive\warn-ifess_config_gui_comprehensive.txt
20722 INFO: Graph cross-reference written to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\build\ifess_config_gui_comprehensive\xref-ifess_config_gui_comprehensive.html
20783 INFO: checking PYZ
20783 INFO: Building PYZ because PYZ-00.toc is non existent
20783 INFO: Building PYZ (ZlibArchive) D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\build\ifess_config_gui_comprehensive\PYZ-00.pyz
21748 INFO: Building PYZ (ZlibArchive) D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\build\ifess_config_gui_comprehensive\PYZ-00.pyz completed successfully.
21789 INFO: checking PKG
21789 INFO: Building PKG because PKG-00.toc is non existent
21789 INFO: Building PKG (CArchive) ifess_config_gui.pkg
28338 INFO: Building PKG (CArchive) ifess_config_gui.pkg completed successfully.
28373 INFO: Bootloader C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\bootloader\Windows-64bit-intel\runw.exe
28373 INFO: checking EXE
28373 INFO: Building EXE because EXE-00.toc is non existent
28373 INFO: Building EXE from EXE-00.toc
Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\__main__.py", line 321, in <module>
    run()
    ~~~^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\__main__.py", line 215, in run
    run_build(pyi_config, spec_file, **vars(args))
    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\__main__.py", line 70, in run_build
    PyInstaller.building.build_main.main(pyi_config, spec_file, **kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\building\build_main.py", line 1282, in main
    build(specfile, distpath, workpath, clean_build)
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\building\build_main.py", line 1220, in build
    exec(code, spec_namespace)
    ~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "ifess_config_gui_comprehensive.spec", line 111, in <module>
    exe = EXE(
        pyz,
    ...<17 lines>...
        icon=str(PROJECT_ROOT / 'MAINICON.ico') if (PROJECT_ROOT / 'MAINICON.ico').exists() else None,
    )
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\building\api.py", line 678, in __init__
    self.__postinit__()
    ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\building\datastruct.py", line 184, in __postinit__
    self.assemble()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\building\api.py", line 772, in assemble
    os.remove(self.name)
    ~~~~~~~~~^^^^^^^^^^^
PermissionError: [WinError 5] Access is denied: 'D:\\Gawean Rebinmas\\Monitoring Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\dist\\ifess_config_gui.exe'
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config_oauth_tokens.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_oauth_simple.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_module.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_simple.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_oauth.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_secret.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_secrets.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\common -> common
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py -> .
ERROR: Failed to build ifess_config_gui.exe 
[7/7] Post-build tasks and packaging... 
Copying additional files to dist directory... 
Copied: client_config.json 
Copied: client_config_oauth_tokens.json 
Copied: token.json 
Copied: token_backup.json 
Copied: ptrj-backup-services-account.json 
Copied: client_secret.json 
Copied: client_secrets.json 
Creating launcher batch files... 
Created launcher batch files 
Copied: setup_portable_credentials.bat 
Setting up portable credentials configuration... 
Creating distribution README... 
Created distribution README 
Build completed: 06/06/2025 19:50:07,07 
Results: 
  Successful builds: 0/3 
  Errors: 2 
  Warnings: 1 
✓ ifess_client_hidden.exe - Ready ( bytes) 
✓ ifess_config_gui.exe - Ready ( bytes) 
Distribution ready in: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\ 
Build log saved as: build_log_2025-06-06_19-48-43.txt 
   BUILD FAILED 
