#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script to verify encoding fix works in both normal and compiled scenarios
"""

import sys
import os

def simulate_pyinstaller_encoding_issue():
    """Simulate the PyInstaller encoding issue"""
    print("=== Testing Encoding Fix ===")
    
    # Store original values
    original_stdout_encoding = getattr(sys.stdout, 'encoding', None)
    original_stderr_encoding = getattr(sys.stderr, 'encoding', None)
    
    print(f"Original stdout encoding: {original_stdout_encoding}")
    print(f"Original stderr encoding: {original_stderr_encoding}")
    
    # Simulate PyInstaller condition where encoding is None
    if hasattr(sys.stdout, 'encoding'):
        sys.stdout.encoding = None
    if hasattr(sys.stderr, 'encoding'):
        sys.stderr.encoding = None
        
    print(f"After setting to None - stdout encoding: {getattr(sys.stdout, 'encoding', 'N/A')}")
    print(f"After setting to None - stderr encoding: {getattr(sys.stderr, 'encoding', 'N/A')}")
    
    # Now test the fix
    try:
        # This is the same code from the fixed version
        if hasattr(sys.stdout, 'encoding') and sys.stdout.encoding is not None:
            if sys.stdout.encoding.lower() != 'utf-8':
                import codecs
                sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
                print("Applied stdout encoding fix (condition 1)")
        elif hasattr(sys.stdout, 'buffer'):  # Fallback for compiled executables
            import codecs
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
            print("Applied stdout encoding fix (condition 2 - PyInstaller fallback)")
            
        if hasattr(sys.stderr, 'encoding') and sys.stderr.encoding is not None:
            if sys.stderr.encoding.lower() != 'utf-8':
                import codecs
                sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')
                print("Applied stderr encoding fix (condition 1)")
        elif hasattr(sys.stderr, 'buffer'):  # Fallback for compiled executables
            import codecs
            sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')
            print("Applied stderr encoding fix (condition 2 - PyInstaller fallback)")
            
        print("✅ Encoding fix test PASSED - no AttributeError occurred")
        return True
        
    except AttributeError as e:
        print(f"❌ Encoding fix test FAILED - AttributeError: {e}")
        return False
    except Exception as e:
        print(f"⚠️  Encoding fix test encountered unexpected error: {e}")
        return False

def test_normal_conditions():
    """Test under normal Python execution"""
    print("\n=== Testing Normal Conditions ===")
    
    try:
        # Test normal encoding check
        if hasattr(sys.stdout, 'encoding') and sys.stdout.encoding is not None:
            print(f"Normal stdout encoding: {sys.stdout.encoding}")
        if hasattr(sys.stderr, 'encoding') and sys.stderr.encoding is not None:
            print(f"Normal stderr encoding: {sys.stderr.encoding}")
            
        print("✅ Normal conditions test PASSED")
        return True
    except Exception as e:
        print(f"❌ Normal conditions test FAILED: {e}")
        return False

if __name__ == "__main__":
    print("Testing IFESS Client Encoding Fix")
    print("=" * 50)
    
    # Test normal conditions first
    normal_test = test_normal_conditions()
    
    # Test simulated PyInstaller conditions
    pyinstaller_test = simulate_pyinstaller_encoding_issue()
    
    print("\n" + "=" * 50)
    print("SUMMARY:")
    print(f"Normal conditions test: {'✅ PASSED' if normal_test else '❌ FAILED'}")
    print(f"PyInstaller simulation test: {'✅ PASSED' if pyinstaller_test else '❌ FAILED'}")
    
    if normal_test and pyinstaller_test:
        print("\n🎉 ALL TESTS PASSED - The encoding fix should work!")
        sys.exit(0)
    else:
        print("\n💥 SOME TESTS FAILED - Review the fix")
        sys.exit(1) 