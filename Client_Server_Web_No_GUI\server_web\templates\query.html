{% extends 'layout.html' %}

{% block title %}Query Execution - Firebird Query Server{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
<style>
    .CodeMirror {
        height: 300px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    .results-container {
        max-height: 600px;
        overflow-y: auto;
    }
    /* Improved table styling for query results */
    .query-result-table {
        width: 100%;
        table-layout: auto;
        margin-bottom: 1rem;
        border-collapse: collapse;
        border: 1px solid #dee2e6;
        min-width: 100%;
    }
    .query-result-table th {
        position: sticky;
        top: 0;
        background-color: #0d6efd !important;
        color: white;
        white-space: nowrap;
        padding: 8px 12px;
        text-align: left;
        font-weight: 600;
        z-index: 10;
        border-right: 1px solid #dee2e6;
        border-bottom: 1px solid #dee2e6;
        /* Ensure headers display fully without truncation */
        min-width: max-content;
        max-width: none;
    }
    .query-result-table th:last-child {
        border-right: none;
    }
    .query-result-table td {
        padding: 6px 12px;
        white-space: nowrap;
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        border-right: 1px solid #dee2e6;
        border-bottom: 1px solid #dee2e6;
    }
    .query-result-table td:last-child {
        border-right: none;
    }
    .query-result-table tr:last-child td {
        border-bottom: none;
    }
    .query-result-table tr:nth-child(even) {
        background-color: rgba(0,0,0,0.02);
    }
    .query-result-table tr:hover {
        background-color: rgba(13, 110, 253, 0.1);
    }
    .table-wrapper {
        position: relative;
        overflow-x: auto;
        max-width: 100%;
        border: 1px solid #dee2e6;
        border-radius: 4px;
    }
    .table-controls {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        align-items: center;
    }
    .table-controls .btn-group {
        margin-left: auto;
    }
    .table-info {
        font-size: 0.875rem;
        color: #6c757d;
    }
    /* Tooltip for truncated cells */
    .cell-content {
        position: relative;
        cursor: default;
    }
    .cell-content.truncated {
        text-decoration: underline dotted;
        cursor: help;
    }

    /* Query variables styling */
    .query-variables {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        margin-top: 8px;
        border: 1px solid #dee2e6;
    }

    .query-item {
        transition: all 0.2s ease;
    }

    .query-item:hover {
        background-color: #f8f9fa;
    }

    .query-variable-input {
        font-family: 'Consolas', 'Courier New', monospace;
        background-color: #f8fff8;
        border: 1px solid #28a745;
    }

    .query-variable-input:focus {
        background-color: #f0fff0;
        border-color: #28a745;
        box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25);
    }

    .query-variables .input-group {
        margin-bottom: 8px;
    }

    .query-variables .input-group-text {
        background-color: #e9f7ef;
        color: #28a745;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<!-- Toast container for notifications -->
<div class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 1100;"></div>

<div class="row">
    <div class="col-md-12">
        <h1 class="mb-4">Query Execution</h1>
    </div>
</div>

<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-code me-2"></i>SQL Query
                </h5>
            </div>
            <div class="card-body">
                <form id="query-form">
                    <div class="mb-3">
                        <label for="query-editor" class="form-label">Enter SQL Query:</label>
                        <textarea id="query-editor" name="query" class="form-control"></textarea>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="client-select" class="form-label">Target Clients:</label>
                            <select id="client-select" name="clients" class="form-select" multiple size="5">
                                <option value="all" selected>All Clients</option>
                                {% for client in clients %}
                                <option value="{{ client.id }}">{{ client.name }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">Hold Ctrl/Cmd to select multiple clients</div>
                        </div>

                        <div class="col-md-6">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label mb-0">Saved Queries:</label>
                                <button type="button" class="btn btn-sm btn-outline-primary" id="manage-queries-btn">
                                    <i class="fas fa-cog me-1"></i>Manage
                                </button>
                            </div>
                            <div class="list-group">
                                {% for name, query in predefined_queries.items() %}
                                <div class="list-group-item query-item" data-name="{{ name }}" data-query="{{ query }}">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">{{ name }}</h6>
                                        <button type="button" class="btn btn-sm btn-primary load-query-btn">
                                            <i class="fas fa-upload me-1"></i>Load
                                        </button>
                                    </div>
                                    <div class="query-variables mt-2" style="display: none;">
                                        <!-- Variables will be added here dynamically -->
                                    </div>
                                </div>
                                {% endfor %}
                            </div>


                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-1"></i>Execute Query
                            </button>
                            <button type="button" id="clear-query" class="btn btn-secondary ms-2">
                                <i class="fas fa-eraser me-1"></i>Clear
                            </button>
                        </div>
                        <div>
                            <button type="button" id="save-query" class="btn btn-success">
                                <i class="fas fa-save me-1"></i>Save Query
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>Query Results
                    <div class="spinner-border spinner-border-sm text-light ms-2 d-none" id="results-loading" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </h5>
            </div>
            <div class="card-body">
                <ul class="nav nav-tabs" id="results-tabs" role="tablist">
                    <!-- Tabs will be dynamically added here -->
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="all-results-tab" data-bs-toggle="tab" data-bs-target="#all-results" type="button" role="tab" aria-controls="all-results" aria-selected="true">All Results</button>
                    </li>
                </ul>
                <div class="tab-content mt-3" id="results-content">
                    <!-- Tab content will be dynamically added here -->
                    <div class="tab-pane fade show active" id="all-results" role="tabpanel" aria-labelledby="all-results-tab">
                        <div id="all-results-container">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>Execute a query to see results.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Save Query Modal -->
<div class="modal fade" id="saveQueryModal" tabindex="-1" aria-labelledby="saveQueryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="saveQueryModalLabel">Save Query</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="save-query-form">
                    <input type="hidden" id="query-id" name="query_id">
                    <div class="mb-3">
                        <label for="query-name" class="form-label">Query Name:</label>
                        <input type="text" class="form-control" id="query-name" name="query_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="query-description" class="form-label">Description (optional):</label>
                        <textarea class="form-control" id="query-description" name="query_description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="save-query-confirm">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Manage Queries Modal -->
<div class="modal fade" id="manageQueriesModal" tabindex="-1" aria-labelledby="manageQueriesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="manageQueriesModalLabel">Manage Saved Queries</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="queries-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Last Updated</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Queries will be loaded here dynamically -->
                        </tbody>
                    </table>
                </div>
                <div id="no-queries-message" class="alert alert-info d-none">
                    <i class="fas fa-info-circle me-2"></i>No saved queries found. Save a query to see it here.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Query Modal -->
<div class="modal fade" id="editQueryModal" tabindex="-1" aria-labelledby="editQueryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editQueryModalLabel">Edit Query</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="edit-query-form">
                    <input type="hidden" id="edit-query-id" name="query_id">
                    <input type="hidden" id="edit-query-old-name" name="old_name">
                    <div class="mb-3">
                        <label for="edit-query-name" class="form-label">Query Name:</label>
                        <input type="text" class="form-control" id="edit-query-name" name="query_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit-query-description" class="form-label">Description (optional):</label>
                        <textarea class="form-control" id="edit-query-description" name="query_description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="edit-query-content" class="form-label">Query:</label>
                        <textarea class="form-control" id="edit-query-content" name="query_content" rows="10" required></textarea>
                    </div>

                    <!-- Dynamic Variables Section -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label class="form-label mb-0">Dynamic Variables:</label>
                            <button type="button" class="btn btn-sm btn-success" id="add-variable-btn">
                                <i class="fas fa-plus me-1"></i>Add Variable
                            </button>
                        </div>
                        <div class="alert alert-info small">
                            <i class="fas fa-info-circle me-1"></i>
                            Dynamic variables use the format <code>#Variable_Name#</code> in your query.
                            They will be replaced with user input when the query is executed.
                        </div>
                        <div id="variables-container" class="border rounded p-3 bg-light">
                            <div class="text-muted small" id="no-variables-message">
                                No variables detected in this query. Add variables by using the format <code>#Variable_Name#</code> in your query.
                            </div>
                            <div id="detected-variables-list">
                                <!-- Variables will be added here dynamically -->
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="update-query-confirm">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Query Confirmation Modal -->
<div class="modal fade" id="deleteQueryModal" tabindex="-1" aria-labelledby="deleteQueryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteQueryModalLabel">Delete Query</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the query "<span id="delete-query-name"></span>"?</p>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
                <input type="hidden" id="delete-query-id">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="delete-query-confirm">Delete</button>
            </div>
        </div>
    </div>
</div>

<!-- Variables Modal -->
<div class="modal fade" id="variablesModal" tabindex="-1" aria-labelledby="variablesModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="variablesModalLabel">Enter Variable Values</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="variables-modal-body">
                <!-- Variable inputs will be dynamically added here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="variables-confirm">Confirm</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Variable Modal -->
<div class="modal fade" id="addVariableModal" tabindex="-1" aria-labelledby="addVariableModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addVariableModalLabel">Add Dynamic Variable</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="variable-name" class="form-label">Variable Name:</label>
                    <div class="input-group">
                        <span class="input-group-text">#</span>
                        <input type="text" class="form-control" id="variable-name" placeholder="Enter variable name (no spaces)" pattern="[A-Za-z0-9_]+" required>
                        <span class="input-group-text">#</span>
                    </div>
                    <div class="form-text">Use only letters, numbers, and underscores. No spaces allowed.</div>
                </div>
                <div class="mb-3">
                    <label for="variable-default" class="form-label">Default Value (optional):</label>
                    <input type="text" class="form-control" id="variable-default" placeholder="Default value when executing query">
                </div>
                <div class="mb-3">
                    <label for="variable-description" class="form-label">Description (optional):</label>
                    <textarea class="form-control" id="variable-description" rows="2" placeholder="Describe what this variable is used for"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="add-variable-confirm">Add Variable</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/sql/sql.min.js"></script>
<script>
    // Global variable to store the last query results
    window.lastQueryResults = [];

    $(document).ready(function() {
        // Initialize CodeMirror with lazy loading for better initial performance
        const editorTextarea = document.getElementById('query-editor');
        let editor;

        // Delay CodeMirror initialization until the page is fully loaded
        setTimeout(() => {
            editor = CodeMirror.fromTextArea(editorTextarea, {
                mode: 'text/x-sql',
                theme: 'default',
                lineNumbers: true,
                indentWithTabs: true,
                smartIndent: true,
                lineWrapping: true,
                matchBrackets: true,
                autofocus: false // Changed to false for better initial load performance
            });
        }, 100);

        // Initialize CodeMirror for edit query modal - lazy loaded
        let editQueryEditor;

        // Only initialize the editor when the modal is shown
        $('#editQueryModal').on('shown.bs.modal', function() {
            if (!editQueryEditor) {
                editQueryEditor = CodeMirror.fromTextArea(document.getElementById('edit-query-content'), {
                    mode: 'text/x-sql',
                    theme: 'default',
                    lineNumbers: true,
                    indentWithTabs: true,
                    smartIndent: true,
                    lineWrapping: true,
                    matchBrackets: true
                });

                // Add change event to update variables list
                editQueryEditor.on('change', function() {
                    updateVariablesList(editQueryEditor.getValue());
                });
            }
            // Refresh the editor to ensure proper sizing
            setTimeout(() => {
                editQueryEditor.refresh();

                // Update variables list
                updateVariablesList(editQueryEditor.getValue());
            }, 10);
        });

        // Add variable button in edit query modal
        $('#add-variable-btn').click(function() {
            // Reset the add variable form
            $('#variable-name').val('');
            $('#variable-default').val('');
            $('#variable-description').val('');

            // Reset the modal title and button text
            $('#addVariableModalLabel').text('Add Dynamic Variable');
            $('#add-variable-confirm').text('Add Variable');
            $('#add-variable-confirm').removeData('original-name');

            // Show add variable modal
            const addVariableModal = new bootstrap.Modal(document.getElementById('addVariableModal'));
            addVariableModal.show();
        });

        // Add variable confirm button
        $('#add-variable-confirm').click(function() {
            const variableName = $('#variable-name').val().trim();
            const defaultValue = $('#variable-default').val().trim();
            const description = $('#variable-description').val().trim();
            const originalName = $(this).data('original-name');

            if (!variableName) {
                alert('Please enter a variable name.');
                return;
            }

            // Validate variable name (only letters, numbers, and underscores)
            if (!/^[A-Za-z0-9_]+$/.test(variableName)) {
                alert('Variable name can only contain letters, numbers, and underscores. No spaces allowed.');
                return;
            }

            // If this is an edit (has original name) and the name changed, remove the old variable
            if (originalName && originalName !== variableName) {
                removeVariableFromQuery(originalName);
            }

            // Add variable to query
            if (addVariableToQuery(variableName, defaultValue, description)) {
                // Hide add variable modal
                bootstrap.Modal.getInstance(document.getElementById('addVariableModal')).hide();

                // Show success message
                if (originalName) {
                    showToast('success', `Variable #${originalName}# updated to #${variableName}#.`);
                } else {
                    showToast('success', `Variable #${variableName}# added to query.`);
                }
            }
        });

        // Remove variable button
        $(document).on('click', '.remove-variable-btn', function() {
            const variableName = $(this).data('variable');

            if (confirm(`Are you sure you want to remove the variable #${variableName}# from the query? This will remove all occurrences of this variable.`)) {
                if (removeVariableFromQuery(variableName)) {
                    showToast('success', `Variable #${variableName}# removed from query.`);
                }
            }
        });

        // Edit variable button
        $(document).on('click', '.edit-variable-btn', function() {
            const variableName = $(this).data('variable');
            const variableItem = $(`.variable-item[data-variable="${variableName}"]`);
            const defaultValue = variableItem.attr('data-default') || '';
            const description = variableItem.attr('data-description') || '';

            // Populate add variable form
            $('#variable-name').val(variableName);
            $('#variable-default').val(defaultValue);
            $('#variable-description').val(description);

            // Show add variable modal
            const addVariableModal = new bootstrap.Modal(document.getElementById('addVariableModal'));
            addVariableModal.show();

            // Change the modal title and button text
            $('#addVariableModalLabel').text('Edit Dynamic Variable');
            $('#add-variable-confirm').text('Update Variable');

            // Store the original variable name for update
            $('#add-variable-confirm').data('original-name', variableName);
        });

        // No need to load from localStorage, we'll use server-side storage

        // Function to extract variables from query
        function extractVariables(query) {
            const variableRegex = /#([A-Za-z0-9_]+)#/g;
            const variables = new Set();
            let match;

            while ((match = variableRegex.exec(query)) !== null) {
                variables.add(match[1]);
            }

            return Array.from(variables);
        }

        // Function to replace variables in query with their values
        function replaceVariables(query, variables) {
            let result = query;

            for (const [name, value] of Object.entries(variables)) {
                const regex = new RegExp('#' + name + '#', 'g');
                result = result.replace(regex, value);
            }

            return result;
        }

        // Function to update the variables list in the edit query modal
        function updateVariablesList(query) {
            const variables = extractVariables(query);
            const variablesContainer = $('#detected-variables-list');
            const noVariablesMessage = $('#no-variables-message');

            // Clear existing variables
            variablesContainer.empty();

            if (variables.length === 0) {
                // Show "no variables" message
                noVariablesMessage.show();
                return;
            }

            // Hide "no variables" message
            noVariablesMessage.hide();

            // Add each variable to the list
            variables.forEach(variable => {
                const variableItem = $(`
                    <div class="variable-item mb-2 p-2 border rounded bg-white" data-variable="${variable}">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0"><code>#${variable}#</code></h6>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary edit-variable-btn" data-variable="${variable}">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger remove-variable-btn" data-variable="${variable}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="small text-muted">
                            <div class="default-value-container" style="display: none;">
                                Default: <span class="default-value"></span>
                            </div>
                            <div class="description-container" style="display: none;">
                                Description: <span class="description"></span>
                            </div>
                        </div>
                    </div>
                `);

                variablesContainer.append(variableItem);
            });
        }

        // Function to add a variable to the query
        function addVariableToQuery(variableName, defaultValue, description) {
            // Get the current query content
            const queryContent = editQueryEditor ? editQueryEditor.getValue() : $('#edit-query-content').val();

            // Check if the variable already exists in the query
            const variables = extractVariables(queryContent);
            if (variables.includes(variableName)) {
                showToast('warning', `Variable #${variableName}# already exists in the query.`);
                return false;
            }

            // Add the variable to the query at the cursor position or at the end
            if (editQueryEditor) {
                const cursor = editQueryEditor.getCursor();
                editQueryEditor.replaceRange(`#${variableName}#`, cursor);
            } else {
                $('#edit-query-content').val(queryContent + ` #${variableName}#`);
            }

            // Update the variables list
            updateVariablesList(editQueryEditor ? editQueryEditor.getValue() : $('#edit-query-content').val());

            // Store default value and description as data attributes
            const variableItem = $(`.variable-item[data-variable="${variableName}"]`);

            if (defaultValue) {
                variableItem.attr('data-default', defaultValue);
                variableItem.find('.default-value').text(defaultValue);
                variableItem.find('.default-value-container').show();
            }

            if (description) {
                variableItem.attr('data-description', description);
                variableItem.find('.description').text(description);
                variableItem.find('.description-container').show();
            }

            return true;
        }

        // Function to remove a variable from the query
        function removeVariableFromQuery(variableName) {
            // Get the current query content
            const queryContent = editQueryEditor ? editQueryEditor.getValue() : $('#edit-query-content').val();

            // Replace all occurrences of the variable with an empty string
            const regex = new RegExp(`#${variableName}#`, 'g');
            const newContent = queryContent.replace(regex, '');

            // Update the query content
            if (editQueryEditor) {
                editQueryEditor.setValue(newContent);
            } else {
                $('#edit-query-content').val(newContent);
            }

            // Update the variables list
            updateVariablesList(newContent);

            return true;
        }

        // Process all query items to extract and display variables
        function processQueryItems() {
            $('.query-item').each(function() {
                const queryItem = $(this);
                const query = queryItem.data('query');
                const queryName = queryItem.data('name');
                const variables = extractVariables(query);
                const variablesContainer = queryItem.find('.query-variables');

                // Clear existing variables
                variablesContainer.empty();

                // If no variables, hide the container
                if (variables.length === 0) {
                    variablesContainer.hide();
                    return;
                }

                // Fetch variable metadata if available
                let variableMetadata = {};

                // Try to get variable metadata from the server
                $.ajax({
                    url: `/api/query/get/${queryName}`,
                    method: 'GET',
                    async: false,  // Make this synchronous to ensure we have metadata before continuing
                    success: function(response) {
                        if (response.success && response.query && response.query.variables) {
                            variableMetadata = response.query.variables;
                        }
                    }
                });

                // Add input fields for each variable
                variables.forEach(variable => {
                    const metadata = variableMetadata[variable] || {};
                    const defaultValue = metadata.default || '';
                    const description = metadata.description || '';

                    const inputGroup = $(`
                        <div class="input-group input-group-sm mb-2">
                            <span class="input-group-text">#${variable}#</span>
                            <input type="text" class="form-control query-variable-input"
                                   data-variable="${variable}"
                                   data-default="${defaultValue}"
                                   data-description="${description}"
                                   placeholder="Enter value for #${variable}#"
                                   value="${defaultValue}"
                                   onclick="event.stopPropagation();">
                            ${description ? `<span class="input-group-text" title="${description}"><i class="fas fa-info-circle"></i></span>` : ''}
                        </div>
                    `);

                    variablesContainer.append(inputGroup);
                });

                // Show the variables container
                variablesContainer.show();
            });
        }

        // Process query items on page load
        processQueryItems();

        // Remove click handler from query items and add it only to the header
        $('.query-item').off('click');

        // Add click handler to the query item header only
        $(document).on('click', '.query-item .d-flex', function(e) {
            // Don't trigger if the load button was clicked
            if ($(e.target).closest('.load-query-btn').length === 0) {
                const queryItem = $(this).closest('.query-item');
                const variablesContainer = queryItem.find('.query-variables');
                const toggleIcon = $(this).find('.toggle-icon');

                // Toggle the variables container
                variablesContainer.toggle();

                // Rotate the chevron icon
                if (variablesContainer.is(':visible')) {
                    toggleIcon.removeClass('fa-chevron-right').addClass('fa-chevron-down');
                } else {
                    toggleIcon.removeClass('fa-chevron-down').addClass('fa-chevron-right');
                }
            }
        });

        // Ensure input fields don't trigger parent events
        $(document).on('click', '.query-variable-input, .input-group, .input-group-text', function(e) {
            e.stopPropagation();
        });

        // Handle load query button click
        $(document).on('click', '.load-query-btn', function(e) {
            e.stopPropagation(); // Prevent the query item click event

            const queryItem = $(this).closest('.query-item');
            const query = queryItem.data('query');
            const queryName = queryItem.data('name');
            const variables = {};

            // Check if we need to show the variables modal
            const variableInputs = queryItem.find('.query-variable-input');

            if (variableInputs.length > 0) {
                // Show variables modal
                const variablesModalBody = $('#variables-modal-body');
                variablesModalBody.empty();

                // Add inputs for each variable
                variableInputs.each(function() {
                    const variable = $(this).data('variable');
                    const value = $(this).val() || '';
                    const defaultValue = $(this).data('default') || '';
                    const description = $(this).data('description') || '';

                    const inputGroup = $(`
                        <div class="mb-3">
                            <label for="var-${variable}" class="form-label">
                                <code>#${variable}#</code>
                                ${description ? `<small class="text-muted ms-2">${description}</small>` : ''}
                            </label>
                            <input type="text" class="form-control" id="var-${variable}"
                                   data-variable="${variable}" value="${value || defaultValue}">
                        </div>
                    `);

                    variablesModalBody.append(inputGroup);
                });

                // Store query info in the modal
                $('#variables-modal-body').data('query', query);
                $('#variables-modal-body').data('query-name', queryName);

                // Show the modal
                const variablesModal = new bootstrap.Modal(document.getElementById('variablesModal'));
                variablesModal.show();

                // Focus on the first input
                setTimeout(() => {
                    variablesModalBody.find('input').first().focus();
                }, 500);
            } else {
                // No variables, just load the query
                // Set the query in the editor
                editor.setValue(query);
                editor.focus();
            }
        });

        // Handle variables confirm button
        $('#variables-confirm').click(function() {
            const variablesModalBody = $('#variables-modal-body');
            const query = variablesModalBody.data('query');
            const variables = {};

            // Collect variable values
            variablesModalBody.find('input').each(function() {
                const variable = $(this).data('variable');
                const value = $(this).val();

                if (value) {
                    variables[variable] = value;
                }
            });

            // Replace variables in query
            const processedQuery = replaceVariables(query, variables);

            // Set the query in the editor
            editor.setValue(processedQuery);
            editor.focus();

            // Hide the modal
            bootstrap.Modal.getInstance(document.getElementById('variablesModal')).hide();
        });

        // Clear query button
        $('#clear-query').click(function() {
            editor.setValue('');
            editor.focus();
        });

        // Save query button
        $('#save-query').click(function() {
            const query = editor.getValue().trim();
            if (!query) {
                alert('Please enter a query to save.');
                return;
            }

            // Reset form
            $('#query-id').val('');
            $('#query-name').val('');
            $('#query-description').val('');

            // Show save query modal
            const modal = new bootstrap.Modal(document.getElementById('saveQueryModal'));
            modal.show();
        });

        // Save query confirm button
        $('#save-query-confirm').click(function() {
            const queryId = $('#query-id').val().trim();
            const queryName = $('#query-name').val().trim();
            const queryDescription = $('#query-description').val().trim();
            const query = editor.getValue().trim();

            if (!queryName) {
                alert('Please enter a name for the query.');
                return;
            }

            // Extract variables from query
            const variables = extractVariables(query);
            const variablesData = {};

            // Prepare variables data
            variables.forEach(variable => {
                variablesData[variable] = {
                    default: '',
                    description: ''
                };
            });

            // Prepare form data
            const formData = {
                query_name: queryName,
                query_content: query,
                description: queryDescription,
                variables: JSON.stringify(variablesData)
            };

            // If we have an ID, it's an update
            if (queryId) {
                formData.query_id = queryId;

                // Update query
                $.post('/api/query/update', formData, function(data) {
                    if (data.success) {
                        // Hide modal
                        bootstrap.Modal.getInstance(document.getElementById('saveQueryModal')).hide();

                        // Show success message
                        showToast('success', 'Query updated successfully.');

                        // Reset form
                        $('#query-id').val('');
                        $('#query-name').val('');
                        $('#query-description').val('');

                        // Refresh queries list if manage modal is open
                        if ($('#manageQueriesModal').hasClass('show')) {
                            loadSavedQueries();
                        }
                    } else {
                        showToast('danger', 'Failed to update query: ' + data.message);
                    }
                }).fail(function() {
                    showToast('danger', 'Failed to update query. Please try again.');
                });
            } else {
                // Save new query
                $.post('/api/query/save', formData, function(data) {
                    if (data.success) {
                        // Hide modal
                        bootstrap.Modal.getInstance(document.getElementById('saveQueryModal')).hide();

                        // Show success message
                        showToast('success', 'Query saved successfully.');

                        // Reset form
                        $('#query-id').val('');
                        $('#query-name').val('');
                        $('#query-description').val('');

                        // Refresh queries list if manage modal is open
                        if ($('#manageQueriesModal').hasClass('show')) {
                            loadSavedQueries();
                        }
                    } else {
                        showToast('danger', 'Failed to save query: ' + data.message);
                    }
                }).fail(function() {
                    showToast('danger', 'Failed to save query. Please try again.');
                });
            }
        });

        // Manage queries button
        $('#manage-queries-btn').click(function() {
            // Load saved queries
            loadSavedQueries();

            // Show manage queries modal
            const modal = new bootstrap.Modal(document.getElementById('manageQueriesModal'));
            modal.show();
        });

        // Function to load saved queries
        function loadSavedQueries() {
            console.log("Loading saved queries...");

            // Show loading indicator in the table
            $('#queries-table tbody').html(`
                <tr>
                    <td colspan="4" class="text-center">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span class="ms-2">Loading queries...</span>
                    </td>
                </tr>
            `);

            $.get('/api/query/list', function(data) {
                console.log("API response:", data);

                if (data.success) {
                    const queries = data.queries;
                    console.log(`Found ${queries.length} queries`);

                    if (queries.length === 0) {
                        // Show no queries message
                        $('#queries-table').addClass('d-none');
                        $('#no-queries-message').removeClass('d-none');
                    } else {
                        // Hide no queries message
                        $('#queries-table').removeClass('d-none');
                        $('#no-queries-message').addClass('d-none');

                        // Clear table
                        $('#queries-table tbody').empty();

                        // Add queries to table
                        queries.forEach(query => {
                            console.log(`Processing query: ${query.name}`);
                            // Use the query content from the API response
                            let queryContent = query.query_text || '';

                            const variablesInfo = query.has_variables ?
                                `<span class="badge bg-info ms-2" title="Contains dynamic variables">
                                    <i class="fas fa-code me-1"></i>${query.variables ? query.variables.length : 0} Variables
                                </span>` : '';

                            const row = `
                                <tr>
                                    <td>
                                        ${query.name}
                                        ${variablesInfo}
                                    </td>
                                    <td>${query.description || '<em>No description</em>'}</td>
                                    <td>${query.updated_at || 'N/A'}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-primary edit-query-btn" data-name="${query.name}">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button class="btn btn-danger delete-query-btn" data-name="${query.name}">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `;

                            $('#queries-table tbody').append(row);
                        });

                        // Attach event handlers
                        attachQueryActionHandlers();
                    }
                } else {
                    console.error("Failed to load queries:", data.message);
                    showToast('danger', 'Failed to load queries: ' + data.message);

                    // Show error in table
                    $('#queries-table tbody').html(`
                        <tr>
                            <td colspan="4" class="text-center text-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                Failed to load queries: ${data.message || 'Unknown error'}
                            </td>
                        </tr>
                    `);
                }
            }).fail(function(jqXHR, textStatus, errorThrown) {
                console.error("AJAX error:", textStatus, errorThrown);
                showToast('danger', `Failed to load queries. Error: ${textStatus} - ${errorThrown || 'Unknown error'}`);

                // Show error in table
                $('#queries-table tbody').html(`
                    <tr>
                        <td colspan="4" class="text-center text-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            AJAX Error: ${textStatus} - ${errorThrown || 'Unknown error'}
                        </td>
                    </tr>
                `);
            });
        }

        // Function to update predefined queries list
        function updatePredefinedQueriesList() {
            // Get the list container
            const listContainer = $('.list-group');

            // Clear the list
            listContainer.empty();

            // Add queries
            for (const [name, query] of Object.entries(predefined_queries)) {
                // Extract variables from query
                const variables = extractVariables(query);
                const hasVariables = variables.length > 0;
                const variablesInfo = hasVariables ?
                    `<span class="badge bg-info ms-2" title="Contains dynamic variables">
                        <i class="fas fa-code me-1"></i>${variables.length} Variables
                    </span>` : '';

                // Create query item
                const queryItem = $(`
                    <div class="list-group-item query-item" data-name="${name}" data-query="${query.replace(/"/g, '&quot;')}">
                        <div class="d-flex justify-content-between align-items-center" style="cursor: pointer;">
                            <h6 class="mb-0">
                                <i class="fas fa-chevron-right me-1 toggle-icon"></i>
                                ${name}
                                ${variablesInfo}
                            </h6>
                            <button type="button" class="btn btn-sm btn-primary load-query-btn">
                                <i class="fas fa-upload me-1"></i>Load
                            </button>
                        </div>
                        <div class="query-variables mt-2" style="display: none;">
                            <!-- Variables will be added here dynamically -->
                        </div>
                    </div>
                `);

                listContainer.append(queryItem);
            }

            // Add message if no queries
            if (Object.keys(predefined_queries).length === 0) {
                listContainer.append(`
                    <div class="list-group-item text-muted">
                        <i class="fas fa-info-circle me-2"></i>No saved queries
                    </div>
                `);
            }

            // Process query items to extract variables
            processQueryItems();

            // Remove click handler from query items and add it only to the header
            $('.query-item').off('click');

            // Add click handler to the query item header only
            $('.query-item .d-flex').off('click').on('click', function(e) {
                // Don't trigger if the load button was clicked
                if ($(e.target).closest('.load-query-btn').length === 0) {
                    const queryItem = $(this).closest('.query-item');
                    const variablesContainer = queryItem.find('.query-variables');
                    const toggleIcon = $(this).find('.toggle-icon');

                    // Toggle the variables container
                    variablesContainer.toggle();

                    // Rotate the chevron icon
                    if (variablesContainer.is(':visible')) {
                        toggleIcon.removeClass('fa-chevron-right').addClass('fa-chevron-down');
                    } else {
                        toggleIcon.removeClass('fa-chevron-down').addClass('fa-chevron-right');
                    }
                }
            });
        }

        // Function to attach event handlers to query action buttons
        function attachQueryActionHandlers() {
            // Edit query button
            $('.edit-query-btn').click(function() {
                const queryName = $(this).data('name');

                // Fetch the query data from the server to get the latest version
                $.get(`/api/query/get/${queryName}`, function(response) {
                    if (response.success && response.query) {
                        const query = response.query;
                        const queryDescription = query.description || '';
                        const queryContent = query.query_text || '';

                        // Populate edit form
                        $('#edit-query-name').val(queryName);
                        $('#edit-query-old-name').val(queryName); // Store original name for update
                        $('#edit-query-description').val(queryDescription);
                        $('#edit-query-content').val(queryContent); // Set content in textarea first

                        // Show edit modal - the editor will be initialized/refreshed when the modal is shown
                        const editModal = new bootstrap.Modal(document.getElementById('editQueryModal'));
                        editModal.show();

                        // Hide manage modal
                        bootstrap.Modal.getInstance(document.getElementById('manageQueriesModal')).hide();

                        // Update variables list after the editor is initialized
                        setTimeout(() => {
                            if (editQueryEditor) {
                                updateVariablesList(editQueryEditor.getValue());
                            } else {
                                updateVariablesList($('#edit-query-content').val());
                            }
                        }, 300);
                    } else {
                        showToast('danger', 'Failed to load query: ' + (response.message || 'Unknown error'));
                    }
                }).fail(function() {
                    showToast('danger', 'Failed to load query. Please try again.');
                });
            });

            // Delete query button
            $('.delete-query-btn').click(function() {
                const queryName = $(this).data('name');

                // Populate delete confirmation modal
                $('#delete-query-name').text(queryName);

                // Show delete confirmation modal
                const deleteModal = new bootstrap.Modal(document.getElementById('deleteQueryModal'));
                deleteModal.show();
            });
        }

        // Update query confirm button
        $('#update-query-confirm').click(function() {
            const oldName = $('#edit-query-old-name').val();
            const queryName = $('#edit-query-name').val().trim();
            const queryDescription = $('#edit-query-description').val().trim();
            // Get content from CodeMirror if initialized, otherwise from textarea
            const queryContent = (editQueryEditor ? editQueryEditor.getValue() : $('#edit-query-content').val()).trim();

            if (!queryName) {
                alert('Please enter a name for the query.');
                return;
            }

            if (!queryContent) {
                alert('Please enter query content.');
                return;
            }

            // Extract variables from query
            const variables = extractVariables(queryContent);
            const variablesData = {};

            // Create default variable data
            variables.forEach(variable => {
                variablesData[variable] = {
                    default: '',
                    description: ''
                };
            });

            // Update query
            $.post('/api/query/update', {
                old_name: oldName,
                query_name: queryName,
                query_content: queryContent,
                description: queryDescription,
                variables: JSON.stringify(variablesData)
            }, function(data) {
                if (data.success) {
                    // Hide edit modal
                    bootstrap.Modal.getInstance(document.getElementById('editQueryModal')).hide();

                    // Show success message
                    showToast('success', 'Query updated successfully.');

                    // Show manage modal again and refresh list
                    const manageModal = new bootstrap.Modal(document.getElementById('manageQueriesModal'));
                    manageModal.show();
                    loadSavedQueries();

                    // Update predefined queries
                    if (oldName !== queryName && oldName in predefined_queries) {
                        delete predefined_queries[oldName];
                    }
                    predefined_queries[queryName] = queryContent;

                    // Update predefined queries list
                    updatePredefinedQueriesList();
                } else {
                    showToast('danger', 'Failed to update query: ' + data.message);
                }
            }).fail(function() {
                showToast('danger', 'Failed to update query. Please try again.');
            });
        });

        // Delete query confirm button
        $('#delete-query-confirm').click(function() {
            const queryName = $('#delete-query-name').text();

            // Delete query
            $.post('/api/query/delete', {
                query_name: queryName
            }, function(data) {
                if (data.success) {
                    // Hide delete modal
                    bootstrap.Modal.getInstance(document.getElementById('deleteQueryModal')).hide();

                    // Show success message
                    showToast('success', 'Query deleted successfully.');

                    // Refresh queries list
                    loadSavedQueries();

                    // Remove from predefined queries
                    if (queryName in predefined_queries) {
                        delete predefined_queries[queryName];
                    }

                    // Update predefined queries list
                    updatePredefinedQueriesList();
                } else {
                    showToast('danger', 'Failed to delete query: ' + data.message);
                }
            }).fail(function() {
                showToast('danger', 'Failed to delete query. Please try again.');
            });
        });

        // Function to show toast notification
        function showToast(type, message) {
            const toast = $(`
                <div class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `);

            $('.toast-container').append(toast);
            const bsToast = new bootstrap.Toast(toast, { delay: 5000 });
            bsToast.show();

            // Remove toast after it's hidden
            toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }

        // Check URL parameters for query content
        let urlParams = new URLSearchParams(window.location.search);
        let queryParam = urlParams.get('query');

        if (queryParam) {
            editor.setValue(decodeURIComponent(queryParam));

        }

        // Handle query form submission
        $('#query-form').submit(function(e) {
            e.preventDefault();

            const query = editor.getValue().trim();
            if (!query) {
                alert('Please enter a query to execute.');
                return;
            }

            // Get selected clients
            let selectedClients = [];
            $('#client-select option:selected').each(function() {
                const value = $(this).val();
                if (value !== 'all') {
                    selectedClients.push(value);
                }
            });

            // If "All Clients" is selected, use empty array
            if ($('#client-select option[value="all"]').is(':selected')) {
                selectedClients = [];
            }

            // Extract variables from query
            const variables = extractVariables(query);
            const queryVariables = {};

            // If variables found, show a prompt to enter values
            if (variables.length > 0) {
                let html = '<div class="mb-3"><p>Please enter values for the following variables:</p>';

                variables.forEach(variable => {
                    html += `
                        <div class="input-group input-group-sm mb-2">
                            <span class="input-group-text">#${variable}#</span>
                            <input type="text" class="form-control variable-input"
                                   data-variable="${variable}" placeholder="Enter value for #${variable}#">
                        </div>
                    `;
                });

                html += '</div>';

                // Show modal with variable inputs
                $('#variables-modal-body').html(html);
                const variablesModal = new bootstrap.Modal(document.getElementById('variablesModal'));
                variablesModal.show();

                // Save reference to continue execution after variables are entered
                window.pendingQueryExecution = {
                    query: query,
                    selectedClients: selectedClients
                };

                return;
            }

            // Show loading indicator
            $('#results-loading').removeClass('d-none');

            // Send query
            $.post('/api/query/send', {
                query: query,
                clients: selectedClients
            }, function(data) {
                if (data.success) {
                    // Poll for results
                    pollForResults();
                } else {
                    // Hide loading indicator
                    $('#results-loading').addClass('d-none');

                    // Show error message
                    $('#all-results-container').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>${data.message}
                        </div>
                    `);
                }
            }).fail(function() {
                // Hide loading indicator
                $('#results-loading').addClass('d-none');

                // Show error message
                $('#all-results-container').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>Failed to send query. Please try again.
                    </div>
                `);
            });
        });

        // Variables modal confirm button
        $('#variables-confirm').click(function() {
            // Get the pending query execution
            const pendingExecution = window.pendingQueryExecution;
            if (!pendingExecution) return;

            // Collect variable values
            const queryVariables = {};
            $('.variable-input').each(function() {
                const variable = $(this).data('variable');
                const value = $(this).val();

                if (value) {
                    queryVariables[variable] = value;
                }
            });

            // Hide the modal
            bootstrap.Modal.getInstance(document.getElementById('variablesModal')).hide();

            // Show loading indicator
            $('#results-loading').removeClass('d-none');

            // Send query with variables
            $.post('/api/query/send', {
                query: pendingExecution.query,
                clients: pendingExecution.selectedClients,
                variables: JSON.stringify(queryVariables)
            }, function(data) {
                if (data.success) {
                    // Poll for results
                    pollForResults();
                } else {
                    // Hide loading indicator
                    $('#results-loading').addClass('d-none');

                    // Show error message
                    $('#all-results-container').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>${data.message}
                        </div>
                    `);
                }
            }).fail(function() {
                // Hide loading indicator
                $('#results-loading').addClass('d-none');

                // Show error message
                $('#all-results-container').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>Failed to send query. Please try again.
                    </div>
                `);
            });

            // Clear pending execution
            window.pendingQueryExecution = null;
        });

        // Function to poll for query results
        function pollForResults() {
            // Clear existing client tabs
            $('#results-tabs li:not(:first-child)').remove();
            $('#results-content div.tab-pane:not(:first-child)').remove();

            // Poll for results - reduced frequency to 2 seconds instead of 1 second
            const pollInterval = setInterval(function() {
                const pollCount = $('#results-loading').data('poll-count') || 0;

                // Reduced logging - only log every 5th attempt
                if (pollCount % 5 === 0) {
                    console.log(`Polling for results (attempt ${pollCount + 1})`);
                }

                $.get('/api/query/results', function(data) {
                    if (data.success) {
                        // Increment poll count
                        const newPollCount = (pollCount || 0) + 1;
                        $('#results-loading').data('poll-count', newPollCount);

                        // Hide loading indicator if we have results or it's been more than 120 seconds (2 minutes)
                        if (data.results.length > 0 || newPollCount > 60) { // Reduced max attempts to 60 (2 minutes with 2-second interval)
                            $('#results-loading').addClass('d-none');
                            clearInterval(pollInterval);
                        }

                        if (data.results.length > 0) {
                            // Process results
                            processResults(data.results);
                        } else if (newPollCount > 60) {
                            // No results after 120 seconds (2 minutes)
                            $('#all-results-container').html(`
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>No results received from clients after 2 minutes. The query might be too complex or the database might be busy. The query is still running in the background and results will appear when available.
                                </div>
                            `);
                        }
                    } else {
                        // Hide loading indicator
                        $('#results-loading').addClass('d-none');
                        clearInterval(pollInterval);

                        // Show error message
                        $('#all-results-container').html(`
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>Failed to retrieve results: ${data.message || 'Unknown error'}
                            </div>
                        `);
                    }
                }).fail(function(jqXHR, textStatus, errorThrown) {
                    // Hide loading indicator
                    $('#results-loading').addClass('d-none');
                    clearInterval(pollInterval);

                    // Show error message
                    $('#all-results-container').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>Failed to retrieve results: ${textStatus} - ${errorThrown || 'Unknown error'}
                        </div>
                    `);
                });
            }, 2000); // Changed from 1000 to 2000 ms

            // Initialize poll count
            $('#results-loading').data('poll-count', 0);
        }

        // Function to process and display query results
        function processResults(results) {
            // Store results globally for later use (e.g., loading more rows)
            window.lastQueryResults = results;

            // Group results by client
            const clientResults = {};
            let allResultsHtml = '';

            // Create document fragment for better performance
            const tabsFragment = document.createDocumentFragment();
            const contentFragment = document.createDocumentFragment();

            results.forEach(result => {
                const clientName = result.client_name || 'Unknown Client';
                const clientId = result.client_id || 'unknown';

                if (!clientResults[clientId]) {
                    clientResults[clientId] = {
                        name: clientName,
                        results: []
                    };
                }

                // Ensure result has rows and headers
                if (!result.rows) result.rows = [];
                if (!result.headers) result.headers = [];

                clientResults[clientId].results.push(result);

                // Add to all results
                allResultsHtml += generateResultHtml(result, clientName);
            });

            // Update all results tab
            if (allResultsHtml) {
                $('#all-results-container').html(allResultsHtml);
            } else {
                $('#all-results-container').html(`
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>No results received from clients.
                    </div>
                `);
            }

            // Create tabs for each client
            const $resultsTabs = $('#results-tabs');
            const $resultsContent = $('#results-content');

            Object.entries(clientResults).forEach(([clientId, client]) => {
                // Create tab
                const tabId = `client-${clientId.replace(/[^a-zA-Z0-9]/g, '-')}-tab`;
                const contentId = `client-${clientId.replace(/[^a-zA-Z0-9]/g, '-')}-content`;

                // Add tab if it doesn't exist
                if ($(`#${tabId}`).length === 0) {
                    // Create tab element
                    const tabLi = document.createElement('li');
                    tabLi.className = 'nav-item';
                    tabLi.setAttribute('role', 'presentation');
                    tabLi.innerHTML = `
                        <button class="nav-link" id="${tabId}" data-bs-toggle="tab" data-bs-target="#${contentId}"
                        type="button" role="tab" aria-controls="${contentId}" aria-selected="false">${client.name}</button>
                    `;
                    tabsFragment.appendChild(tabLi);

                    // Create content element
                    const contentDiv = document.createElement('div');
                    contentDiv.className = 'tab-pane fade';
                    contentDiv.id = contentId;
                    contentDiv.setAttribute('role', 'tabpanel');
                    contentDiv.setAttribute('aria-labelledby', tabId);
                    contentDiv.innerHTML = `
                        <div id="${contentId}-container" class="results-container">
                            <!-- Client results will be added here -->
                        </div>
                    `;
                    contentFragment.appendChild(contentDiv);
                }
            });

            // Append all tabs and content at once for better performance
            $resultsTabs.append(tabsFragment);
            $resultsContent.append(contentFragment);

            // Add results to tab content
            Object.entries(clientResults).forEach(([clientId, client]) => {
                const contentId = `client-${clientId.replace(/[^a-zA-Z0-9]/g, '-')}-content`;

                let clientResultsHtml = '';
                client.results.forEach(result => {
                    clientResultsHtml += generateResultHtml(result);
                });

                $(`#${contentId}-container`).html(clientResultsHtml);
            });

            // Make sure the first tab is active
            if ($('#results-tabs .nav-link.active').length === 0) {
                $('#results-tabs .nav-link:first').tab('show');
            }
        }

        // Function to generate HTML for a result
        function generateResultHtml(result, clientName = null) {
            let html = '';

            // Add client name if provided
            if (clientName) {
                html += `<h5 class="mb-3">${clientName}</h5>`;
            }

            // Add query info if available
            if (result.query) {
                const queryPreview = result.query.length > 100 ? result.query.substring(0, 100) + '...' : result.query;
                html += `
                    <div class="alert alert-secondary mb-3">
                        <strong>Query:</strong> <code>${queryPreview}</code>
                    </div>
                `;
            }

            // Check if result has status and it's error
            if (result.status === 'error' || result.error) {
                html += `
                    <div class="alert alert-danger mb-4">
                        <i class="fas fa-exclamation-circle me-2"></i>${result.error || 'Query execution failed'}
                    </div>
                `;
                return html;
            }

            // Check if result has data
            if (!result.headers || !result.rows || result.rows.length === 0) {
                html += `
                    <div class="alert alert-info mb-4">
                        <i class="fas fa-info-circle me-2"></i>Query executed successfully, but no data returned.
                    </div>
                `;
                return html;
            }

            // Add table controls
            const resultId = 'result-' + Math.random().toString(36).substring(2, 10);
            html += `
                <div class="table-controls mb-2">
                    <div class="table-info">
                        <i class="fas fa-table me-1"></i>
                        <span class="fw-bold">${result.rows.length}</span> rows,
                        <span class="fw-bold">${result.headers.length}</span> columns
                        ${result.timestamp ? `<span class="ms-2"><i class="far fa-clock me-1"></i>${new Date(result.timestamp * 1000).toLocaleString()}</span>` : ''}
                    </div>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-secondary toggle-wrap-btn" data-target="#${resultId}">
                            <i class="fas fa-text-width me-1"></i>Toggle Wrap
                        </button>
                        <button type="button" class="btn btn-outline-secondary resize-columns-btn" data-target="#${resultId}">
                            <i class="fas fa-arrows-alt-h me-1"></i>Auto-size Columns
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="exportTableToCSV('#${resultId}', 'query_result.csv')">
                            <i class="fas fa-download me-1"></i>Export CSV
                        </button>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="input-group input-group-sm">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control table-search-input" placeholder="Search in results..." data-target="#${resultId}">
                    </div>
                </div>
            `;

            // Add result table with improved styling
            html += `<div class="table-wrapper mb-4"><table id="${resultId}" class="query-result-table"><thead><tr>`;

            // Add headers - optimized to reduce string concatenations
            const headers = result.headers.map(header => `<th>${header}</th>`).join('');
            html += headers;

            html += '</tr></thead><tbody>';

            // Optimize row generation - use array join instead of string concatenation in a loop
            const rowsHtml = [];

            // Limit the number of rows to render initially for better performance
            const maxInitialRows = 500;
            const rowsToRender = result.rows.length > maxInitialRows ? result.rows.slice(0, maxInitialRows) : result.rows;

            for (let i = 0; i < rowsToRender.length; i++) {
                const row = rowsToRender[i];
                let cellsHtml = '';

                // Handle different row formats
                if (Array.isArray(row)) {
                    // Row is an array, use index to access values
                    for (let j = 0; j < result.headers.length; j++) {
                        const rawValue = j < row.length && row[j] !== null && row[j] !== undefined ? row[j] : '';
                        const value = String(rawValue);
                        const isTruncated = value.length > 50;
                        const displayValue = isTruncated ? value.substring(0, 50) + '...' : value;

                        if (isTruncated) {
                            cellsHtml += `<td><span class="cell-content truncated" title="${value.replace(/"/g, '&quot;')}">${displayValue}</span></td>`;
                        } else {
                            cellsHtml += `<td><span class="cell-content">${displayValue}</span></td>`;
                        }
                    }
                } else if (typeof row === 'object') {
                    // Row is an object, use headers as keys
                    for (let j = 0; j < result.headers.length; j++) {
                        const header = result.headers[j];
                        const rawValue = row[header] !== null && row[header] !== undefined ? row[header] : '';
                        const value = String(rawValue);
                        const isTruncated = value.length > 50;
                        const displayValue = isTruncated ? value.substring(0, 50) + '...' : value;

                        if (isTruncated) {
                            cellsHtml += `<td><span class="cell-content truncated" title="${value.replace(/"/g, '&quot;')}">${displayValue}</span></td>`;
                        } else {
                            cellsHtml += `<td><span class="cell-content">${displayValue}</span></td>`;
                        }
                    }
                } else {
                    // Unexpected row format, create empty cells
                    cellsHtml = result.headers.map(() => '<td><span class="cell-content">Error: Invalid data format</span></td>').join('');
                }

                rowsHtml.push(`<tr>${cellsHtml}</tr>`);
            }

            html += rowsHtml.join('');

            // Add a message if we limited the number of rows
            if (result.rows.length > maxInitialRows) {
                html += `<tr><td colspan="${result.headers.length}" class="text-center">
                    <div class="alert alert-info mb-0 mt-2">
                        Showing ${maxInitialRows} of ${result.rows.length} rows for better performance.
                        <button class="btn btn-sm btn-primary ms-2 load-more-rows" data-result-id="${resultId}" data-total-rows="${result.rows.length}">
                            Load All Rows
                        </button>
                    </div>
                </td></tr>`;
            }

            html += '</tbody></table></div>';

            // Add note if results are truncated
            if (result.truncated) {
                html += `
                    <div class="alert alert-warning mb-4">
                        <i class="fas fa-exclamation-triangle me-2"></i>Results have been truncated due to size limitations.
                    </div>
                `;
            }

            return html;
        }

        // Check for client parameter in URL
        urlParams = new URLSearchParams(window.location.search);
        const clientParam = urlParams.get('client');
        if (clientParam) {
            // Select the specified client
            $('#client-select option[value="all"]').prop('selected', false);
            $(`#client-select option[value="${clientParam}"]`).prop('selected', true);

        }

        // Function to toggle text wrapping in table cells
        $(document).on('click', '.toggle-wrap-btn', function() {
            const targetTable = $($(this).data('target'));
            const isWrapped = targetTable.hasClass('text-wrapped');

            if (isWrapped) {
                // Remove wrapping
                targetTable.removeClass('text-wrapped');
                targetTable.find('td .cell-content').css({
                    'white-space': 'nowrap',
                    'max-width': '300px',
                    'overflow': 'hidden',
                    'text-overflow': 'ellipsis'
                });
                $(this).html('<i class="fas fa-text-width me-1"></i>Enable Wrap');
            } else {
                // Add wrapping
                targetTable.addClass('text-wrapped');
                targetTable.find('td .cell-content').css({
                    'white-space': 'normal',
                    'max-width': 'none',
                    'overflow': 'visible',
                    'text-overflow': 'clip'
                });
                $(this).html('<i class="fas fa-text-width me-1"></i>Disable Wrap');
            }
        });

        // Function to export table to CSV
        function exportTableToCSV(tableSelector, filename) {
            const table = document.querySelector(tableSelector);
            if (!table) return;

            // Get headers
            const headers = [];
            const headerCells = table.querySelectorAll('thead th');
            headerCells.forEach(cell => headers.push(cell.textContent.trim()));

            // Get rows
            const rows = [];
            const rowElements = table.querySelectorAll('tbody tr');
            rowElements.forEach(row => {
                const rowData = [];
                row.querySelectorAll('td').forEach(cell => {
                    // Get the full text (including truncated parts)
                    const cellContent = cell.querySelector('.cell-content');
                    let value = '';

                    if (cellContent) {
                        // If it's truncated, use the title attribute which has the full text
                        if (cellContent.classList.contains('truncated') && cellContent.title) {
                            value = cellContent.title;
                        } else {
                            value = cellContent.textContent.trim();
                        }
                    } else {
                        value = cell.textContent.trim();
                    }

                    // Escape quotes and wrap in quotes if contains comma
                    value = value.replace(/"/g, '""');
                    if (value.includes(',')) {
                        value = `"${value}"`;
                    }

                    rowData.push(value);
                });
                rows.push(rowData.join(','));
            });

            // Combine headers and rows
            const csv = [headers.join(','), ...rows].join('\n');

            // Create download link
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Initialize tooltips for truncated cells
        $(document).tooltip({
            selector: '.cell-content.truncated',
            container: 'body',
            placement: 'auto',
            html: true
        });

        // Function to auto-size columns
        $(document).on('click', '.resize-columns-btn', function() {
            const targetTable = $($(this).data('target'));
            if (!targetTable.length) return;

            // Get number of columns
            const numColumns = targetTable.find('thead th').length;
            if (numColumns <= 0) return;

            // Calculate optimal column width based on content
            const tableWidth = targetTable.parent().width();
            const minColWidth = 100; // Minimum column width in pixels
            const maxColWidth = 300; // Maximum column width in pixels

            // Get content lengths for each column
            const colLengths = [];
            for (let i = 0; i < numColumns; i++) {
                colLengths.push(0);
            }

            // Check header lengths
            targetTable.find('thead th').each(function(idx) {
                const headerLength = $(this).text().length;
                colLengths[idx] = Math.max(colLengths[idx], headerLength);
            });

            // Check cell content lengths (sample first 50 rows)
            targetTable.find('tbody tr:lt(50)').each(function() {
                $(this).find('td').each(function(idx) {
                    if (idx < numColumns) {
                        const cellLength = $(this).text().length;
                        colLengths[idx] = Math.max(colLengths[idx], cellLength);
                    }
                });
            });

            // Calculate width percentages
            const totalLength = colLengths.reduce((sum, len) => sum + Math.min(Math.max(len * 8, minColWidth), maxColWidth), 0);
            const widthPercentages = colLengths.map(len => {
                const pixelWidth = Math.min(Math.max(len * 8, minColWidth), maxColWidth);
                return (pixelWidth / totalLength) * 100;
            });

            // Apply widths to columns
            targetTable.find('thead th').each(function(idx) {
                if (idx < widthPercentages.length) {
                    $(this).css('width', widthPercentages[idx] + '%');
                }
            });

            // Notify user
            const toast = $(`
                <div class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-check-circle me-2"></i>Column widths optimized based on content.
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `);

            $('.toast-container').append(toast);
            const bsToast = new bootstrap.Toast(toast, { delay: 3000 });
            bsToast.show();

            // Remove toast after it's hidden
            toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        });

        // Function to handle "Load All Rows" button click
        $(document).on('click', '.load-more-rows', function() {
            const resultId = $(this).data('result-id');
            const totalRows = $(this).data('total-rows');
            const table = $(`#${resultId}`);

            // Show loading indicator
            $(this).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...');
            $(this).prop('disabled', true);

            // Use setTimeout to allow the UI to update before starting the heavy operation
            setTimeout(() => {
                // Find the result object in the results array
                const result = window.lastQueryResults.find(r => {
                    // Check if this result contains this table
                    return r.rows && r.rows.length === totalRows;
                });

                if (result) {
                    // Generate HTML for remaining rows
                    const tbody = table.find('tbody');
                    const loadMoreRow = tbody.find('tr:last-child');
                    const remainingRows = result.rows.slice(500);

                    // Create a document fragment for better performance
                    const fragment = document.createDocumentFragment();

                    // Process rows in batches to avoid UI freezing
                    const batchSize = 100;
                    let currentBatch = 0;

                    function processNextBatch() {
                        const start = currentBatch * batchSize;
                        const end = Math.min(start + batchSize, remainingRows.length);

                        if (start >= remainingRows.length) {
                            // All batches processed, remove the "Load More" row
                            loadMoreRow.remove();
                            return;
                        }

                        // Process this batch
                        for (let i = start; i < end; i++) {
                            const row = remainingRows[i];
                            const tr = document.createElement('tr');

                            // Handle different row formats
                            if (Array.isArray(row)) {
                                // Row is an array, use index to access values
                                for (let j = 0; j < result.headers.length; j++) {
                                    const rawValue = j < row.length && row[j] !== null && row[j] !== undefined ? row[j] : '';
                                    const value = String(rawValue);
                                    const isTruncated = value.length > 50;
                                    const displayValue = isTruncated ? value.substring(0, 50) + '...' : value;

                                    const td = document.createElement('td');
                                    const span = document.createElement('span');
                                    span.className = isTruncated ? 'cell-content truncated' : 'cell-content';
                                    if (isTruncated) span.title = value;
                                    span.textContent = displayValue;
                                    td.appendChild(span);
                                    tr.appendChild(td);
                                }
                            } else if (typeof row === 'object') {
                                // Row is an object, use headers as keys
                                for (let j = 0; j < result.headers.length; j++) {
                                    const header = result.headers[j];
                                    const rawValue = row[header] !== null && row[header] !== undefined ? row[header] : '';
                                    const value = String(rawValue);
                                    const isTruncated = value.length > 50;
                                    const displayValue = isTruncated ? value.substring(0, 50) + '...' : value;

                                    const td = document.createElement('td');
                                    const span = document.createElement('span');
                                    span.className = isTruncated ? 'cell-content truncated' : 'cell-content';
                                    if (isTruncated) span.title = value;
                                    span.textContent = displayValue;
                                    td.appendChild(span);
                                    tr.appendChild(td);
                                }
                            } else {
                                // Unexpected row format, create empty cells
                                for (let j = 0; j < result.headers.length; j++) {
                                    const td = document.createElement('td');
                                    const span = document.createElement('span');
                                    span.className = 'cell-content';
                                    span.textContent = 'Error: Invalid data format';
                                    td.appendChild(span);
                                    tr.appendChild(td);
                                }
                            }

                            fragment.appendChild(tr);
                        }

                        // Insert before the "Load More" row
                        tbody[0].insertBefore(fragment, loadMoreRow[0]);

                        // Update progress in the button
                        const processedRows = Math.min((currentBatch + 1) * batchSize, remainingRows.length);
                        const percentComplete = Math.round((processedRows / remainingRows.length) * 100);
                        $(`.load-more-rows[data-result-id="${resultId}"]`).html(
                            `Loading... ${percentComplete}% (${processedRows}/${remainingRows.length})`
                        );

                        // Process next batch
                        currentBatch++;
                        setTimeout(processNextBatch, 0);
                    }

                    // Start processing batches
                    processNextBatch();
                } else {
                    // Result not found, show error
                    $(this).html('Error: Could not load remaining rows');
                    $(this).removeClass('btn-primary').addClass('btn-danger');
                }
            }, 50);
        });

        // Function to search in table - optimized version
        $(document).on('input', '.table-search-input', function() {
            const searchText = $(this).val().toLowerCase();
            const targetTable = $($(this).data('target'));

            if (!targetTable.length) return;

            // If search is empty, show all rows
            if (!searchText) {
                targetTable.find('tbody tr').show();
                return;
            }

            // Debounce search for better performance
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                // Search in each row
                targetTable.find('tbody tr').each(function() {
                    const row = $(this);
                    const rowText = row.text().toLowerCase();

                    if (rowText.includes(searchText)) {
                        row.show();
                    } else {
                        row.hide();
                    }
                });

                // Only highlight the first 20 matches for performance
                let matchCount = 0;
                targetTable.find('tbody tr:visible').each(function() {
                    if (matchCount >= 20) return false; // Break the loop

                    const row = $(this);
                    // Reset previous highlights
                    row.find('.bg-warning').each(function() {
                        const span = $(this);
                        const parent = span.parent();
                        parent.text(parent.text());
                    });

                    // Add new highlights
                    row.find('td').each(function() {
                        if (matchCount >= 20) return false; // Break the loop

                        const cell = $(this);
                        const cellText = cell.text();

                        // Skip highlighting for performance reasons if cell is too long
                        if (cellText.length > 500) return;

                        // Find match position (case insensitive)
                        const matchPos = cellText.toLowerCase().indexOf(searchText);
                        if (matchPos >= 0) {
                            matchCount++;
                            const cellContent = cell.find('.cell-content');
                            if (cellContent.length) {
                                const originalText = cellContent.text();
                                const beforeMatch = originalText.substring(0, matchPos);
                                const match = originalText.substring(matchPos, matchPos + searchText.length);
                                const afterMatch = originalText.substring(matchPos + searchText.length);

                                cellContent.html(beforeMatch + '<span class="bg-warning">' + match + '</span>' + afterMatch);
                            }
                        }
                    });
                });
            }, 300); // 300ms debounce
        });
    });
</script>
{% endblock %}
