2025-04-24 20:36:01,316 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-24 20:36:01,579 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-24 20:36:09,605 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-24 20:36:09,606 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-24 21:35:35,219 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-24 21:35:35,460 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-24 21:35:35,460 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-26 07:56:34,955 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-26 07:56:35,174 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-26 07:56:35,175 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-26 07:56:54,599 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-26 07:56:54,600 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-26 08:44:32,665 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-26 08:44:32,942 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-26 08:44:32,943 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-26 08:45:48,590 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-26 08:45:48,591 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-26 08:45:51,976 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-26 08:45:51,977 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-26 09:09:31,015 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-26 09:09:31,283 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-26 09:09:31,283 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-26 09:09:42,792 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-26 09:09:42,793 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-28 15:51:48,128 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-28 15:51:48,431 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-28 15:51:48,431 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-28 15:52:06,668 - IFESS-Config - INFO - Testing Google Drive connection with:
2025-04-28 15:52:06,670 - IFESS-Config - INFO -   Credentials file: D:/Gawean Rebinmas/Monitoring Database/ifess/Client_Server_Web_No_GUI/staging_build/token.json
2025-04-28 15:52:06,946 - IFESS-Config - INFO - Loading service account credentials from: D:/Gawean Rebinmas/Monitoring Database/ifess/Client_Server_Web_No_GUI/staging_build/token.json
2025-04-28 15:52:06,947 - IFESS-Config - ERROR - Error authenticating with Google Drive: Service account info was not in the expected format, missing fields client_email.
2025-04-28 15:52:06,954 - IFESS-Config - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ifess_config_gui.py", line 671, in run_test
    creds = service_account.Credentials.from_service_account_file(
        credentials_file,
        scopes=SCOPES
    )
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\google\oauth2\service_account.py", line 260, in from_service_account_file
    info, signer = _service_account_info.from_filename(
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        filename, require=["client_email", "token_uri"]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\google\auth\_service_account_info.py", line 82, in from_filename
    return data, from_dict(data, require=require, use_rsa_signer=use_rsa_signer)
                 ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\google\auth\_service_account_info.py", line 52, in from_dict
    raise exceptions.MalformedError(
    ...<2 lines>...
    )
google.auth.exceptions.MalformedError: Service account info was not in the expected format, missing fields client_email.

2025-04-28 16:15:12,198 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-28 16:15:12,459 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-28 16:15:12,459 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-28 16:15:20,271 - IFESS-Config - INFO - Using token_downloader to get token from Google Drive
2025-04-28 16:15:20,273 - token_downloader - INFO - Downloading token from Google Drive (attempt 1/5)...
2025-04-28 16:15:20,273 - token_downloader - INFO - Backing up existing token to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_backup.json
2025-04-28 16:15:24,195 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-28 16:15:24,209 - IFESS-Config - INFO - Token downloaded successfully from Google Drive
2025-04-28 16:15:24,503 - IFESS-Config - INFO - Token is expired, refreshing...
2025-04-28 16:15:25,246 - IFESS-Config - INFO - Token refreshed and saved
2025-04-28 16:15:25,246 - IFESS-Config - INFO - Building Google Drive API service...
2025-04-28 16:15:25,253 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-04-28 16:15:25,257 - IFESS-Config - INFO - Google Drive API service built successfully
2025-04-28 16:15:26,069 - IFESS-Config - INFO - Logged in as: atha rizki p .developer (<EMAIL>)
2025-04-28 16:15:26,070 - IFESS-Config - INFO - Storage: 5.78 GB used of 15.00 GB total
2025-04-28 16:15:26,071 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-28 22:31:11,468 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-28 22:31:11,699 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-28 22:31:11,699 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-28 22:31:11,700 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-04-28 22:31:12,128 - IFESS-Config - INFO - token_downloader module imported successfully
2025-04-28 22:31:12,129 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-04-28 22:31:12,129 - token_downloader - INFO - Getting token (attempt 1/3)...
2025-04-28 22:31:12,129 - token_downloader - INFO - Token expires in 46.0 minutes (threshold: 60.0 minutes)
2025-04-28 22:31:12,130 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-04-28 22:31:12,130 - token_downloader - INFO - Getting token file from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-28 22:31:12,172 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-04-28 22:31:12,175 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-04-28 22:31:12,177 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-04-28 22:31:12,178 - token_downloader - INFO - Using service account to list files
2025-04-28 22:31:13,476 - token_downloader - INFO - Found 1 files in the folder using service account
2025-04-28 22:31:13,477 - token_downloader - INFO - Found file: token_20250428_221749.json (Modified: 2025-04-28T15:17:51.723Z)
2025-04-28 22:31:13,477 - token_downloader - WARNING - Newest token file doesn't have date/time information, using existing token
2025-04-28 22:31:13,477 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-04-28 22:31:13,478 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-28 22:31:40,236 - IFESS-Config - INFO - Using token_downloader to get token from Google Drive
2025-04-28 22:31:40,239 - token_downloader - INFO - Getting token (attempt 1/3)...
2025-04-28 22:31:40,240 - token_downloader - INFO - Token expires in 45.6 minutes (threshold: 60.0 minutes)
2025-04-28 22:31:40,240 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-04-28 22:31:40,241 - token_downloader - INFO - Getting token file from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-28 22:31:40,241 - token_downloader - INFO - Using service account to list files
2025-04-28 22:31:40,619 - token_downloader - INFO - Found 1 files in the folder using service account
2025-04-28 22:31:40,619 - token_downloader - INFO - Found file: token_20250428_221749.json (Modified: 2025-04-28T15:17:51.723Z)
2025-04-28 22:31:40,620 - token_downloader - WARNING - Newest token file doesn't have date/time information, using existing token
2025-04-28 22:31:40,620 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-04-28 22:31:40,629 - IFESS-Config - INFO - Building Google Drive API service...
2025-04-28 22:31:40,633 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-04-28 22:31:40,638 - IFESS-Config - INFO - Google Drive API service built successfully
2025-04-28 22:31:41,483 - IFESS-Config - INFO - Logged in as: atha rizki p .developer (<EMAIL>)
2025-04-28 22:31:41,484 - IFESS-Config - INFO - Storage: 6.84 GB used of 15.00 GB total
2025-04-28 22:31:41,485 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-28 22:32:03,053 - IFESS-Config - INFO - Testing MEGA connection with:
2025-04-28 22:32:03,054 - IFESS-Config - INFO -   Email: <EMAIL>
2025-04-28 22:32:03,178 - IFESS-Config - ERROR - Error in test_mega_connection: module 'asyncio' has no attribute 'coroutine'
2025-04-28 22:32:03,194 - IFESS-Config - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ifess_config_gui.py", line 602, in run_test
    from mega import Mega
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\mega\__init__.py", line 1, in <module>
    from .mega import Mega  # noqa
    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\mega\mega.py", line 18, in <module>
    from tenacity import retry, wait_exponential, retry_if_exception_type
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\tenacity\__init__.py", line 451, in <module>
    from tenacity._asyncio import AsyncRetrying
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\tenacity\_asyncio.py", line 33, in <module>
    class AsyncRetrying(BaseRetrying):
    ...<26 lines>...
                    return do
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\tenacity\_asyncio.py", line 41, in AsyncRetrying
    @asyncio.coroutine
     ^^^^^^^^^^^^^^^^^
AttributeError: module 'asyncio' has no attribute 'coroutine'. Did you mean: 'coroutines'?

2025-04-28 22:33:48,315 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-28 22:33:48,563 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-28 22:33:48,563 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-28 22:33:48,564 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-04-28 22:33:49,038 - IFESS-Config - INFO - token_downloader module imported successfully
2025-04-28 22:33:49,038 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-04-28 22:33:49,038 - token_downloader - INFO - Getting token (attempt 1/3)...
2025-04-28 22:33:49,039 - token_downloader - INFO - Token expires in 43.4 minutes (threshold: 60.0 minutes)
2025-04-28 22:33:49,040 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-04-28 22:33:49,040 - token_downloader - INFO - Getting token file from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-28 22:33:49,100 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-04-28 22:33:49,106 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-04-28 22:33:49,110 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-04-28 22:33:49,111 - token_downloader - INFO - Using service account to list files
2025-04-28 22:33:50,309 - token_downloader - INFO - Found 1 files in the folder using service account
2025-04-28 22:33:50,310 - token_downloader - INFO - Found file: token_20250428_221749.json (Modified: 2025-04-28T15:17:51.723Z)
2025-04-28 22:33:50,311 - token_downloader - WARNING - Newest token file doesn't have date/time information, using existing token
2025-04-28 22:33:50,311 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-04-28 22:33:50,312 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-28 22:34:04,011 - IFESS-Config - INFO - Testing MEGA connection with:
2025-04-28 22:34:04,012 - IFESS-Config - INFO -   Email: <EMAIL>
2025-04-28 22:34:04,140 - IFESS-Config - ERROR - Error importing MEGA library: module 'asyncio' has no attribute 'coroutine'
2025-04-28 22:42:37,488 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-28 22:42:37,708 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-28 22:42:37,709 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-28 22:42:37,709 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-04-28 22:42:38,139 - IFESS-Config - INFO - token_downloader module imported successfully
2025-04-28 22:42:38,139 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-04-28 22:42:38,139 - token_downloader - INFO - Getting token (attempt 1/3)...
2025-04-28 22:42:38,140 - token_downloader - INFO - Token expires in 34.6 minutes (threshold: 60.0 minutes)
2025-04-28 22:42:38,140 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-04-28 22:42:38,140 - token_downloader - INFO - Getting token file from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-28 22:42:38,191 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-04-28 22:42:38,194 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-04-28 22:42:38,200 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-04-28 22:42:38,200 - token_downloader - INFO - Using service account to list files
2025-04-28 22:42:39,456 - token_downloader - INFO - Found 1 files in the folder using service account
2025-04-28 22:42:39,457 - token_downloader - INFO - Found file: token_20250428_221749.json (Modified: 2025-04-28T15:17:51.723Z)
2025-04-28 22:42:39,457 - token_downloader - WARNING - Newest token file doesn't have date/time information, using existing token
2025-04-28 22:42:39,457 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-04-28 22:42:39,457 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-28 22:43:22,556 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-28 22:43:22,766 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-28 22:43:22,766 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-28 22:43:22,767 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-04-28 22:43:23,200 - IFESS-Config - INFO - token_downloader module imported successfully
2025-04-28 22:43:23,200 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-04-28 22:43:23,201 - token_downloader - INFO - Getting token (attempt 1/3)...
2025-04-28 22:43:23,201 - token_downloader - INFO - Token expires in 33.8 minutes (threshold: 60.0 minutes)
2025-04-28 22:43:23,202 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-04-28 22:43:23,202 - token_downloader - INFO - Getting token file from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-28 22:43:23,253 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-04-28 22:43:23,256 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-04-28 22:43:23,261 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-04-28 22:43:23,261 - token_downloader - INFO - Using service account to list files
2025-04-28 22:43:24,407 - token_downloader - INFO - Found 1 files in the folder using service account
2025-04-28 22:43:24,407 - token_downloader - INFO - Found file: token_20250428_221749.json (Modified: 2025-04-28T15:17:51.723Z)
2025-04-28 22:43:24,408 - token_downloader - WARNING - Newest token file doesn't have date/time information, using existing token
2025-04-28 22:43:24,409 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-04-28 22:43:24,410 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-28 22:43:25,270 - IFESS-Config - INFO - Testing MEGA connection with:
2025-04-28 22:43:25,271 - IFESS-Config - INFO -   Email: <EMAIL>
2025-04-28 22:43:25,272 - IFESS-Config - ERROR - MEGA library is not compatible with Python 3.13+
2025-04-28 22:46:07,470 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-28 22:46:07,635 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-28 22:46:07,636 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-28 22:46:07,636 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-04-28 22:46:08,082 - IFESS-Config - INFO - token_downloader module imported successfully
2025-04-28 22:46:08,083 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-04-28 22:46:08,083 - token_downloader - INFO - Getting token (attempt 1/3)...
2025-04-28 22:46:08,084 - token_downloader - INFO - Token expires in 31.1 minutes (threshold: 60.0 minutes)
2025-04-28 22:46:08,084 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-04-28 22:46:08,084 - token_downloader - INFO - Getting token file from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-28 22:46:08,129 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-04-28 22:46:08,132 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-04-28 22:46:08,139 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-04-28 22:46:08,139 - token_downloader - INFO - Using service account to list files
2025-04-28 22:46:09,361 - token_downloader - INFO - Found 1 files in the folder using service account
2025-04-28 22:46:09,361 - token_downloader - INFO - Found file: token_20250428_221749.json (Modified: 2025-04-28T15:17:51.723Z)
2025-04-28 22:46:09,362 - token_downloader - WARNING - Newest token file doesn't have date/time information, using existing token
2025-04-28 22:46:09,362 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-04-28 22:46:09,363 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-28 22:46:10,627 - IFESS-Config - INFO - Testing MEGA connection with:
2025-04-28 22:46:10,628 - IFESS-Config - INFO -   Email: <EMAIL>
2025-04-28 22:46:10,628 - IFESS-Config - ERROR - MEGA library is not compatible with Python 3.13+
2025-04-29 07:49:55,779 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-29 07:49:56,190 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-29 07:49:56,191 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-29 07:49:56,191 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-04-29 07:49:56,850 - IFESS-Config - INFO - token_downloader module imported successfully
2025-04-29 07:49:56,853 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-04-29 07:49:56,854 - token_downloader - INFO - Getting token (attempt 1/3)...
2025-04-29 07:49:56,855 - token_downloader - ERROR - Token is already expired
2025-04-29 07:49:56,855 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-04-29 07:49:56,856 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-29 07:49:56,856 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/5)...
2025-04-29 07:49:56,857 - token_downloader - INFO - Getting token file from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-29 07:49:56,928 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-04-29 07:49:56,935 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-04-29 07:49:56,944 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-04-29 07:49:56,945 - token_downloader - INFO - Using service account to list files
2025-04-29 07:49:58,472 - token_downloader - INFO - Found 1 files in the folder using service account
2025-04-29 07:49:58,473 - token_downloader - INFO - Found file: token_20250428_233015.json (Modified: 2025-04-28T16:30:17.436Z)
2025-04-29 07:49:58,473 - token_downloader - INFO - Downloading token file: token_20250428_233015.json (Modified: 2025-04-28T16:30:17.436Z, ID: 1pFlBca1_3WjKURJ111ZNEF6MjTTqwEnA)
2025-04-29 07:49:58,474 - token_downloader - INFO - Using service account to download token
2025-04-29 07:49:59,966 - token_downloader - INFO - Download progress: 100%
2025-04-29 07:49:59,966 - token_downloader - INFO - Processing token data from file: token_20250428_233015.json
2025-04-29 07:49:59,968 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-29 07:49:59,969 - token_downloader - INFO - Token expiry: 2025-04-28T17:30:12.448377Z
2025-04-29 07:49:59,972 - token_downloader - ERROR - Token is already expired
2025-04-29 07:49:59,972 - token_downloader - ERROR - Downloaded token is invalid
2025-04-29 07:49:59,972 - token_downloader - INFO - Retrying in 10 seconds...
2025-04-29 07:50:09,974 - token_downloader - INFO - Getting token (attempt 2/3)...
2025-04-29 07:50:09,976 - token_downloader - ERROR - Token is already expired
2025-04-29 07:50:09,976 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-04-29 07:50:09,977 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-29 07:50:09,977 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/5)...
2025-04-29 07:50:09,978 - token_downloader - INFO - Getting token file from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-29 07:50:09,979 - token_downloader - INFO - Using service account to list files
2025-04-29 07:50:10,326 - token_downloader - INFO - Found 1 files in the folder using service account
2025-04-29 07:50:10,326 - token_downloader - INFO - Found file: token_20250428_233015.json (Modified: 2025-04-28T16:30:17.436Z)
2025-04-29 07:50:10,327 - token_downloader - INFO - Downloading token file: token_20250428_233015.json (Modified: 2025-04-28T16:30:17.436Z, ID: 1pFlBca1_3WjKURJ111ZNEF6MjTTqwEnA)
2025-04-29 07:50:10,328 - token_downloader - INFO - Using service account to download token
2025-04-29 07:50:11,473 - token_downloader - INFO - Download progress: 100%
2025-04-29 07:50:11,474 - token_downloader - INFO - Processing token data from file: token_20250428_233015.json
2025-04-29 07:50:11,476 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-29 07:50:11,477 - token_downloader - INFO - Token expiry: 2025-04-28T17:30:12.448377Z
2025-04-29 07:50:11,481 - token_downloader - ERROR - Token is already expired
2025-04-29 07:50:11,481 - token_downloader - ERROR - Downloaded token is invalid
2025-04-29 07:50:11,482 - token_downloader - INFO - Retrying in 15 seconds...
2025-04-29 07:50:26,483 - token_downloader - INFO - Getting token (attempt 3/3)...
2025-04-29 07:50:26,484 - token_downloader - ERROR - Token is already expired
2025-04-29 07:50:26,485 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-04-29 07:50:26,485 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-29 07:50:26,486 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/5)...
2025-04-29 07:50:26,486 - token_downloader - INFO - Getting token file from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-29 07:50:26,487 - token_downloader - INFO - Using service account to list files
2025-04-29 07:50:26,889 - token_downloader - INFO - Found 1 files in the folder using service account
2025-04-29 07:50:26,890 - token_downloader - INFO - Found file: token_20250428_233015.json (Modified: 2025-04-28T16:30:17.436Z)
2025-04-29 07:50:26,890 - token_downloader - INFO - Downloading token file: token_20250428_233015.json (Modified: 2025-04-28T16:30:17.436Z, ID: 1pFlBca1_3WjKURJ111ZNEF6MjTTqwEnA)
2025-04-29 07:50:26,891 - token_downloader - INFO - Using service account to download token
2025-04-29 07:50:28,189 - token_downloader - INFO - Download progress: 100%
2025-04-29 07:50:28,189 - token_downloader - INFO - Processing token data from file: token_20250428_233015.json
2025-04-29 07:50:28,191 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-29 07:50:28,193 - token_downloader - INFO - Token expiry: 2025-04-28T17:30:12.448377Z
2025-04-29 07:50:28,197 - token_downloader - ERROR - Token is already expired
2025-04-29 07:50:28,198 - token_downloader - ERROR - Downloaded token is invalid
2025-04-29 07:50:28,198 - token_downloader - INFO - Trying to use backup token as last resort: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_backup.json
2025-04-29 07:50:28,199 - token_downloader - ERROR - Token is already expired
2025-04-29 07:50:28,200 - token_downloader - ERROR - Backup token is also invalid
2025-04-29 07:50:28,200 - token_downloader - CRITICAL - Failed to get a valid token after multiple attempts
2025-04-29 07:50:28,201 - IFESS-Config - WARNING - Failed to get token from Google Drive in the background
2025-04-29 10:01:28,353 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-29 10:01:28,773 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-29 10:01:28,774 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-29 10:01:28,784 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-04-29 10:01:29,208 - IFESS-Config - INFO - token_downloader module imported successfully
2025-04-29 10:01:29,209 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-04-29 10:01:29,209 - token_downloader - INFO - Getting token (attempt 1/3)...
2025-04-29 10:01:29,210 - token_downloader - ERROR - Token is already expired
2025-04-29 10:01:29,210 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-04-29 10:01:29,210 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-29 10:01:29,211 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/5)...
2025-04-29 10:01:29,211 - token_downloader - INFO - Getting token file from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-29 10:01:29,278 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-04-29 10:01:29,282 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-04-29 10:01:29,289 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-04-29 10:01:29,289 - token_downloader - INFO - Using service account to list files
2025-04-29 10:01:30,700 - token_downloader - INFO - Found 1 files in the folder using service account
2025-04-29 10:01:30,701 - token_downloader - INFO - Found file: token_20250429_082102.json (Modified: 2025-04-29T01:21:05.888Z)
2025-04-29 10:01:30,701 - token_downloader - INFO - Downloading token file: token_20250429_082102.json (Modified: 2025-04-29T01:21:05.888Z, ID: 1GY4vv_ibiWbNzPtPmMGMUM5KEI4G3Awi)
2025-04-29 10:01:30,702 - token_downloader - INFO - Using service account to download token
2025-04-29 10:01:32,030 - token_downloader - INFO - Download progress: 100%
2025-04-29 10:01:32,030 - token_downloader - INFO - Processing token data from file: token_20250429_082102.json
2025-04-29 10:01:32,032 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-29 10:01:32,034 - token_downloader - INFO - Token expiry: 2025-04-29T02:20:59.787472Z
2025-04-29 10:01:32,038 - token_downloader - ERROR - Token is already expired
2025-04-29 10:01:32,038 - token_downloader - ERROR - Downloaded token is invalid
2025-04-29 10:01:32,039 - token_downloader - INFO - Retrying in 10 seconds...
2025-04-29 10:01:42,040 - token_downloader - INFO - Getting token (attempt 2/3)...
2025-04-29 10:01:42,041 - token_downloader - ERROR - Token is already expired
2025-04-29 10:01:42,042 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-04-29 10:01:42,043 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-29 10:01:42,043 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/5)...
2025-04-29 10:01:42,044 - token_downloader - INFO - Getting token file from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-29 10:01:42,045 - token_downloader - INFO - Using service account to list files
2025-04-29 10:01:42,384 - token_downloader - INFO - Found 1 files in the folder using service account
2025-04-29 10:01:42,385 - token_downloader - INFO - Found file: token_20250429_082102.json (Modified: 2025-04-29T01:21:05.888Z)
2025-04-29 10:01:42,386 - token_downloader - INFO - Downloading token file: token_20250429_082102.json (Modified: 2025-04-29T01:21:05.888Z, ID: 1GY4vv_ibiWbNzPtPmMGMUM5KEI4G3Awi)
2025-04-29 10:01:42,386 - token_downloader - INFO - Using service account to download token
2025-04-29 10:01:43,018 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-04-29 10:01:43,019 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-29 10:01:43,502 - token_downloader - INFO - Download progress: 100%
2025-04-29 10:01:43,503 - token_downloader - INFO - Processing token data from file: token_20250429_082102.json
2025-04-29 10:01:43,505 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-29 10:01:43,506 - token_downloader - INFO - Token expiry: 2025-04-29T02:20:59.787472Z
2025-04-29 10:01:43,510 - token_downloader - ERROR - Token is already expired
2025-04-29 10:01:43,511 - token_downloader - ERROR - Downloaded token is invalid
2025-04-29 10:01:43,511 - token_downloader - INFO - Retrying in 15 seconds...
2025-05-20 07:51:31,092 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-05-20 07:51:31,487 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-05-20 07:51:31,488 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-05-20 07:51:31,488 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-05-20 07:51:34,823 - IFESS-Config - INFO - token_downloader module imported successfully
2025-05-20 07:51:34,824 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-05-20 07:51:34,825 - token_downloader - INFO - Getting token (attempt 1/3)...
2025-05-20 07:51:34,836 - token_downloader - ERROR - Token is already expired
2025-05-20 07:51:34,837 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-05-20 07:51:34,840 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-05-20 07:51:34,841 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/5)...
2025-05-20 07:51:34,841 - token_downloader - INFO - Getting token file from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-05-20 07:51:34,914 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-05-20 07:51:34,938 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-20 07:51:34,961 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-05-20 07:51:34,961 - token_downloader - INFO - Using service account to list files
2025-05-20 07:51:36,451 - token_downloader - INFO - Found 2 files in the folder using service account
2025-05-20 07:51:36,451 - token_downloader - INFO - Found file: token_20250506_121523.json (Modified: 2025-05-06T05:15:26.882Z)
2025-05-20 07:51:36,451 - token_downloader - INFO - Found file: token_20250506_112224.json (Modified: 2025-05-06T04:22:27.260Z)
2025-05-20 07:51:36,452 - token_downloader - INFO - Downloading token file: token_20250506_121523.json (Modified: 2025-05-06T05:15:26.882Z, ID: 1BLoGOH1fy2P6jpMdBEUyjm_G3p7x2X9c)
2025-05-20 07:51:36,452 - token_downloader - INFO - Using service account to download token
2025-05-20 07:51:39,109 - token_downloader - INFO - Download progress: 100%
2025-05-20 07:51:39,109 - token_downloader - INFO - Processing token data from file: token_20250506_121523.json
2025-05-20 07:51:39,111 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-05-20 07:51:39,111 - token_downloader - INFO - Token expiry: 2025-05-06T06:15:21.538853Z
2025-05-20 07:51:39,127 - token_downloader - ERROR - Token is already expired
2025-05-20 07:51:39,128 - token_downloader - ERROR - Downloaded token is invalid
2025-05-20 07:51:39,130 - token_downloader - INFO - Retrying in 10 seconds...
2025-05-20 07:51:49,132 - token_downloader - INFO - Getting token (attempt 2/3)...
2025-05-20 07:51:49,133 - token_downloader - ERROR - Token is already expired
2025-05-20 07:51:49,133 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-05-20 07:51:49,134 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-05-20 07:51:49,135 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/5)...
2025-05-20 07:51:49,135 - token_downloader - INFO - Getting token file from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-05-20 07:51:49,136 - token_downloader - INFO - Using service account to list files
2025-05-20 07:51:49,509 - token_downloader - INFO - Found 2 files in the folder using service account
2025-05-20 07:51:49,510 - token_downloader - INFO - Found file: token_20250506_121523.json (Modified: 2025-05-06T05:15:26.882Z)
2025-05-20 07:51:49,511 - token_downloader - INFO - Found file: token_20250506_112224.json (Modified: 2025-05-06T04:22:27.260Z)
2025-05-20 07:51:49,512 - token_downloader - INFO - Downloading token file: token_20250506_121523.json (Modified: 2025-05-06T05:15:26.882Z, ID: 1BLoGOH1fy2P6jpMdBEUyjm_G3p7x2X9c)
2025-05-20 07:51:49,513 - token_downloader - INFO - Using service account to download token
2025-05-20 07:51:52,279 - token_downloader - INFO - Download progress: 100%
2025-05-20 07:51:52,279 - token_downloader - INFO - Processing token data from file: token_20250506_121523.json
2025-05-20 07:51:52,281 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-05-20 07:51:52,281 - token_downloader - INFO - Token expiry: 2025-05-06T06:15:21.538853Z
2025-05-20 07:51:52,284 - token_downloader - ERROR - Token is already expired
2025-05-20 07:51:52,285 - token_downloader - ERROR - Downloaded token is invalid
2025-05-20 07:51:52,285 - token_downloader - INFO - Retrying in 15 seconds...
2025-05-20 07:52:07,286 - token_downloader - INFO - Getting token (attempt 3/3)...
2025-05-20 07:52:07,289 - token_downloader - ERROR - Token is already expired
2025-05-20 07:52:07,290 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-05-20 07:52:07,292 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-05-20 07:52:07,293 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/5)...
2025-05-20 07:52:07,295 - token_downloader - INFO - Getting token file from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-05-20 07:52:07,296 - token_downloader - INFO - Using service account to list files
2025-05-20 07:52:07,764 - token_downloader - INFO - Found 2 files in the folder using service account
2025-05-20 07:52:07,765 - token_downloader - INFO - Found file: token_20250506_121523.json (Modified: 2025-05-06T05:15:26.882Z)
2025-05-20 07:52:07,765 - token_downloader - INFO - Found file: token_20250506_112224.json (Modified: 2025-05-06T04:22:27.260Z)
2025-05-20 07:52:07,765 - token_downloader - INFO - Downloading token file: token_20250506_121523.json (Modified: 2025-05-06T05:15:26.882Z, ID: 1BLoGOH1fy2P6jpMdBEUyjm_G3p7x2X9c)
2025-05-20 07:52:07,766 - token_downloader - INFO - Using service account to download token
2025-05-20 07:52:11,132 - token_downloader - INFO - Download progress: 100%
2025-05-20 07:52:11,133 - token_downloader - INFO - Processing token data from file: token_20250506_121523.json
2025-05-20 07:52:11,135 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-05-20 07:52:11,136 - token_downloader - INFO - Token expiry: 2025-05-06T06:15:21.538853Z
2025-05-20 07:52:11,149 - token_downloader - ERROR - Token is already expired
2025-05-20 07:52:11,149 - token_downloader - ERROR - Downloaded token is invalid
2025-05-20 07:52:11,150 - token_downloader - INFO - Trying to use backup token as last resort: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_backup.json
2025-05-20 07:52:11,199 - token_downloader - ERROR - Token is already expired
2025-05-20 07:52:11,200 - token_downloader - ERROR - Backup token is also invalid
2025-05-20 07:52:11,201 - token_downloader - CRITICAL - Failed to get a valid token after multiple attempts
2025-05-20 07:52:11,202 - IFESS-Config - WARNING - Failed to get token from Google Drive in the background
2025-05-20 08:27:23,705 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-05-20 08:27:23,905 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-05-20 08:27:23,906 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-05-20 08:27:23,906 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-05-20 08:27:24,493 - IFESS-Config - INFO - token_downloader module imported successfully
2025-05-20 08:27:24,494 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-05-20 08:27:24,494 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-05-20 08:27:24,494 - token_downloader - ERROR - Token is already expired
2025-05-20 08:27:24,495 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-05-20 08:27:24,495 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-05-20 08:27:24,495 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/10)...
2025-05-20 08:27:24,495 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-05-20 08:27:24,550 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-05-20 08:27:24,554 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-20 08:27:24,559 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-05-20 08:27:24,559 - token_downloader - INFO - Using service account to list files
2025-05-20 08:27:27,593 - token_downloader - INFO - Found 1 files in the folder using service account
2025-05-20 08:27:27,593 - token_downloader - INFO - Found file: token_20250520_073334.json (Modified: 2025-05-20T00:33:35.929Z)
2025-05-20 08:27:27,594 - token_downloader - INFO - Downloading token file: token_20250520_073334.json (Modified: 2025-05-20T00:33:35.929Z, ID: 1xWIfZr07Rib3XJjOAZhvr0nbd7BwtAMt)
2025-05-20 08:27:27,594 - token_downloader - INFO - Using service account to download token
2025-05-20 08:27:30,312 - token_downloader - INFO - Download progress: 100%
2025-05-20 08:27:30,313 - token_downloader - INFO - Processing token data from file: token_20250520_073334.json
2025-05-20 08:27:30,314 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-05-20 08:27:30,315 - token_downloader - INFO - Token expiry: 2025-05-20T00:38:17.334944Z
2025-05-20 08:27:30,333 - token_downloader - ERROR - Token is already expired
2025-05-20 08:27:30,333 - token_downloader - ERROR - Downloaded token is invalid
2025-05-20 08:27:30,334 - token_downloader - INFO - Retrying in 6 seconds...
2025-05-20 08:27:32,858 - IFESS-Config - INFO - Using token_downloader to get token from Google Drive
2025-05-20 08:27:32,863 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-05-20 08:27:32,865 - token_downloader - ERROR - Token is already expired
2025-05-20 08:27:32,866 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-05-20 08:27:32,868 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-05-20 08:27:32,868 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/10)...
2025-05-20 08:27:32,869 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-05-20 08:27:32,869 - token_downloader - INFO - Using service account to list files
2025-05-20 08:27:33,362 - token_downloader - INFO - Found 1 files in the folder using service account
2025-05-20 08:27:33,362 - token_downloader - INFO - Found file: token_20250520_073334.json (Modified: 2025-05-20T00:33:35.929Z)
2025-05-20 08:27:33,362 - token_downloader - INFO - Downloading token file: token_20250520_073334.json (Modified: 2025-05-20T00:33:35.929Z, ID: 1xWIfZr07Rib3XJjOAZhvr0nbd7BwtAMt)
2025-05-20 08:27:33,363 - token_downloader - INFO - Using service account to download token
2025-05-20 08:27:35,891 - token_downloader - INFO - Download progress: 100%
2025-05-20 08:27:35,891 - token_downloader - INFO - Processing token data from file: token_20250520_073334.json
2025-05-20 08:27:35,893 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-05-20 08:27:35,893 - token_downloader - INFO - Token expiry: 2025-05-20T00:38:17.334944Z
2025-05-20 08:27:35,896 - token_downloader - ERROR - Token is already expired
2025-05-20 08:27:35,897 - token_downloader - ERROR - Downloaded token is invalid
2025-05-20 08:27:35,897 - token_downloader - INFO - Retrying in 6 seconds...
2025-05-20 08:27:36,335 - token_downloader - INFO - Getting token (attempt 2/5)...
2025-05-20 08:27:36,336 - token_downloader - ERROR - Token is already expired
2025-05-20 08:27:36,336 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-05-20 08:27:36,337 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-05-20 08:27:36,337 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/10)...
2025-05-20 08:27:36,337 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-05-20 08:27:36,338 - token_downloader - INFO - Using service account to list files
2025-05-20 08:27:36,801 - token_downloader - INFO - Found 1 files in the folder using service account
2025-05-20 08:27:36,801 - token_downloader - INFO - Found file: token_20250520_073334.json (Modified: 2025-05-20T00:33:35.929Z)
2025-05-20 08:27:36,802 - token_downloader - INFO - Downloading token file: token_20250520_073334.json (Modified: 2025-05-20T00:33:35.929Z, ID: 1xWIfZr07Rib3XJjOAZhvr0nbd7BwtAMt)
2025-05-20 08:27:36,802 - token_downloader - INFO - Using service account to download token
2025-05-20 08:27:39,889 - token_downloader - INFO - Download progress: 100%
2025-05-20 08:27:39,890 - token_downloader - INFO - Processing token data from file: token_20250520_073334.json
2025-05-20 08:27:39,892 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-05-20 08:27:39,892 - token_downloader - INFO - Token expiry: 2025-05-20T00:38:17.334944Z
2025-05-20 08:27:39,895 - token_downloader - ERROR - Token is already expired
2025-05-20 08:27:39,895 - token_downloader - ERROR - Downloaded token is invalid
2025-05-20 08:27:39,895 - token_downloader - INFO - Retrying in 9 seconds...
2025-05-20 08:27:41,898 - token_downloader - INFO - Getting token (attempt 2/5)...
2025-05-20 08:27:41,899 - token_downloader - ERROR - Token is already expired
2025-05-20 08:27:41,899 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-05-20 08:27:41,900 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-05-20 08:27:41,900 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/10)...
2025-05-20 08:27:41,901 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-05-20 08:27:41,901 - token_downloader - INFO - Using service account to list files
2025-05-20 08:27:42,592 - token_downloader - INFO - Found 1 files in the folder using service account
2025-05-20 08:27:42,593 - token_downloader - INFO - Found file: token_20250520_073334.json (Modified: 2025-05-20T00:33:35.929Z)
2025-05-20 08:27:42,593 - token_downloader - INFO - Downloading token file: token_20250520_073334.json (Modified: 2025-05-20T00:33:35.929Z, ID: 1xWIfZr07Rib3XJjOAZhvr0nbd7BwtAMt)
2025-05-20 08:27:42,594 - token_downloader - INFO - Using service account to download token
2025-05-20 08:27:45,446 - token_downloader - INFO - Download progress: 100%
2025-05-20 08:27:45,446 - token_downloader - INFO - Processing token data from file: token_20250520_073334.json
2025-05-20 08:27:45,447 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-05-20 08:27:45,448 - token_downloader - INFO - Token expiry: 2025-05-20T00:38:17.334944Z
2025-05-20 08:27:45,449 - token_downloader - ERROR - Token is already expired
2025-05-20 08:27:45,450 - token_downloader - ERROR - Downloaded token is invalid
2025-05-20 08:27:45,450 - token_downloader - INFO - Retrying in 9 seconds...
2025-05-20 08:27:48,896 - token_downloader - INFO - Getting token (attempt 3/5)...
2025-05-20 08:27:48,897 - token_downloader - ERROR - Token is already expired
2025-05-20 08:27:48,897 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-05-20 08:27:48,898 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-05-20 08:27:48,899 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/10)...
2025-05-20 08:27:48,899 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-05-20 08:27:48,899 - token_downloader - INFO - Using service account to list files
2025-05-20 08:27:49,316 - token_downloader - INFO - Found 1 files in the folder using service account
2025-05-20 08:27:49,317 - token_downloader - INFO - Found file: token_20250520_073334.json (Modified: 2025-05-20T00:33:35.929Z)
2025-05-20 08:27:49,317 - token_downloader - INFO - Downloading token file: token_20250520_073334.json (Modified: 2025-05-20T00:33:35.929Z, ID: 1xWIfZr07Rib3XJjOAZhvr0nbd7BwtAMt)
2025-05-20 08:27:49,318 - token_downloader - INFO - Using service account to download token
2025-05-20 08:27:54,451 - token_downloader - INFO - Getting token (attempt 3/5)...
2025-05-20 08:27:54,451 - token_downloader - INFO - Token file not found, downloading from Google Drive
2025-05-20 08:27:54,452 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/10)...
2025-05-20 08:27:54,452 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-05-20 08:27:54,453 - token_downloader - INFO - Using service account to list files
2025-05-20 08:40:46,447 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-05-20 08:40:46,616 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-05-20 08:40:46,616 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-05-20 08:40:46,617 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-05-20 08:40:46,888 - IFESS-Config - INFO - token_downloader module imported successfully
2025-05-20 08:40:46,888 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-05-20 08:40:46,888 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-05-20 08:40:46,889 - token_downloader - INFO - Token file not found, downloading from Google Drive
2025-05-20 08:40:46,889 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/10)...
2025-05-20 08:40:46,889 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-05-20 08:40:46,922 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-05-20 08:40:46,925 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-20 08:40:46,930 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-05-20 08:40:46,930 - token_downloader - INFO - Using service account to list files
2025-05-20 08:40:48,640 - token_downloader - INFO - Found 1 files in the folder using service account
2025-05-20 08:40:48,640 - token_downloader - INFO - Found file: token_20250520_082848.json (Modified: 2025-05-20T01:28:49.330Z)
2025-05-20 08:40:48,642 - token_downloader - INFO - Downloading token file: token_20250520_082848.json (Modified: 2025-05-20T01:28:49.330Z, ID: 1oA4DJbTONyvv5-M94uxLdnsIkWBKpgOt)
2025-05-20 08:40:48,642 - token_downloader - INFO - Using service account to download token
2025-05-20 08:40:50,634 - IFESS-Config - INFO - Using token_downloader to get token from Google Drive
2025-05-20 08:40:50,637 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-05-20 08:40:50,638 - token_downloader - INFO - Token file not found, downloading from Google Drive
2025-05-20 08:40:50,638 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/10)...
2025-05-20 08:40:50,638 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-05-20 08:40:50,639 - token_downloader - INFO - Using service account to list files
2025-05-20 08:40:51,768 - token_downloader - ERROR - Error using service account to download token: [SSL: WRONG_VERSION_NUMBER] wrong version number (_ssl.c:2648)
2025-05-20 08:40:51,823 - token_downloader - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py", line 446, in download_token
    status, done = downloader.next_chunk()
                   ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\_helpers.py", line 130, in positional_wrapper
    return wrapped(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 741, in next_chunk
    resp, content = _retry_request(
                    ~~~~~~~~~~~~~~^
        http,
        ^^^^^
    ...<6 lines>...
        headers=headers,
        ^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 222, in _retry_request
    raise exception
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 191, in _retry_request
    resp, content = http.request(uri, method, *args, **kwargs)
                    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\google_auth_httplib2.py", line 218, in request
    response, content = self.http.request(
                        ~~~~~~~~~~~~~~~~~^
        uri,
        ^^^^
    ...<5 lines>...
        **kwargs
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1724, in request
    (response, content) = self._request(
                          ~~~~~~~~~~~~~^
        conn, authority, uri, request_uri, method, body, headers, redirections, cachekey,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1444, in _request
    (response, content) = self._conn_request(conn, request_uri, method, body, headers)
                          ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1396, in _conn_request
    response = conn.getresponse()
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\ssl.py", line 1304, in recv_into
    return self.read(nbytes, buffer)
           ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\ssl.py", line 1138, in read
    return self._sslobj.read(len, buffer)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
ssl.SSLError: [SSL: WRONG_VERSION_NUMBER] wrong version number (_ssl.c:2648)

2025-05-20 08:40:51,824 - token_downloader - INFO - Retrying in 3 seconds...
2025-05-20 08:40:52,935 - token_downloader - INFO - Found 1 files in the folder using service account
2025-05-20 08:40:52,935 - token_downloader - INFO - Found file: token_20250520_082848.json (Modified: 2025-05-20T01:28:49.330Z)
2025-05-20 08:40:52,935 - token_downloader - INFO - Downloading token file: token_20250520_082848.json (Modified: 2025-05-20T01:28:49.330Z, ID: 1oA4DJbTONyvv5-M94uxLdnsIkWBKpgOt)
2025-05-20 08:40:52,936 - token_downloader - INFO - Using service account to download token
2025-05-20 08:40:54,825 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 2/10)...
2025-05-20 08:40:54,826 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-05-20 08:40:54,827 - token_downloader - INFO - Using service account to list files
2025-05-20 08:40:55,869 - token_downloader - ERROR - Error using service account to download token: [SSL: WRONG_VERSION_NUMBER] wrong version number (_ssl.c:2648)
2025-05-20 08:40:55,869 - token_downloader - ERROR - Error using service account: [SSL: WRONG_VERSION_NUMBER] wrong version number (_ssl.c:2648)
2025-05-20 08:40:55,875 - token_downloader - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py", line 446, in download_token
    status, done = downloader.next_chunk()
                   ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\_helpers.py", line 130, in positional_wrapper
    return wrapped(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 741, in next_chunk
    resp, content = _retry_request(
                    ~~~~~~~~~~~~~~^
        http,
        ^^^^^
    ...<6 lines>...
        headers=headers,
        ^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 222, in _retry_request
    raise exception
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 191, in _retry_request
    resp, content = http.request(uri, method, *args, **kwargs)
                    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\google_auth_httplib2.py", line 218, in request
    response, content = self.http.request(
                        ~~~~~~~~~~~~~~~~~^
        uri,
        ^^^^
    ...<5 lines>...
        **kwargs
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1724, in request
    (response, content) = self._request(
                          ~~~~~~~~~~~~~^
        conn, authority, uri, request_uri, method, body, headers, redirections, cachekey,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1444, in _request
    (response, content) = self._conn_request(conn, request_uri, method, body, headers)
                          ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1396, in _conn_request
    response = conn.getresponse()
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\ssl.py", line 1304, in recv_into
    return self.read(nbytes, buffer)
           ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\ssl.py", line 1138, in read
    return self._sslobj.read(len, buffer)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
ssl.SSLError: [SSL: WRONG_VERSION_NUMBER] wrong version number (_ssl.c:2648)

2025-05-20 08:40:55,875 - token_downloader - INFO - Retrying in 3 seconds...
2025-05-20 08:40:55,888 - token_downloader - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py", line 446, in download_token
    status, done = downloader.next_chunk()
                   ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\_helpers.py", line 130, in positional_wrapper
    return wrapped(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 741, in next_chunk
    resp, content = _retry_request(
                    ~~~~~~~~~~~~~~^
        http,
        ^^^^^
    ...<6 lines>...
        headers=headers,
        ^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 222, in _retry_request
    raise exception
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 191, in _retry_request
    resp, content = http.request(uri, method, *args, **kwargs)
                    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\google_auth_httplib2.py", line 218, in request
    response, content = self.http.request(
                        ~~~~~~~~~~~~~~~~~^
        uri,
        ^^^^
    ...<5 lines>...
        **kwargs
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1724, in request
    (response, content) = self._request(
                          ~~~~~~~~~~~~~^
        conn, authority, uri, request_uri, method, body, headers, redirections, cachekey,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1444, in _request
    (response, content) = self._conn_request(conn, request_uri, method, body, headers)
                          ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1396, in _conn_request
    response = conn.getresponse()
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\ssl.py", line 1304, in recv_into
    return self.read(nbytes, buffer)
           ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\ssl.py", line 1138, in read
    return self._sslobj.read(len, buffer)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
ssl.SSLError: [SSL: WRONG_VERSION_NUMBER] wrong version number (_ssl.c:2648)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py", line 163, in get_token_list_from_folder
    ).execute()
      ~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\_helpers.py", line 130, in positional_wrapper
    return wrapped(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 923, in execute
    resp, content = _retry_request(
                    ~~~~~~~~~~~~~~^
        http,
        ^^^^^
    ...<7 lines>...
        headers=self.headers,
        ^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 222, in _retry_request
    raise exception
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 191, in _retry_request
    resp, content = http.request(uri, method, *args, **kwargs)
                    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\google_auth_httplib2.py", line 218, in request
    response, content = self.http.request(
                        ~~~~~~~~~~~~~~~~~^
        uri,
        ^^^^
    ...<5 lines>...
        **kwargs
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1724, in request
    (response, content) = self._request(
                          ~~~~~~~~~~~~~^
        conn, authority, uri, request_uri, method, body, headers, redirections, cachekey,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1444, in _request
    (response, content) = self._conn_request(conn, request_uri, method, body, headers)
                          ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1396, in _conn_request
    response = conn.getresponse()
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\ssl.py", line 1304, in recv_into
    return self.read(nbytes, buffer)
           ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\ssl.py", line 1138, in read
    return self._sslobj.read(len, buffer)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
ssl.SSLError: [SSL: WRONG_VERSION_NUMBER] wrong version number (_ssl.c:2648)

2025-05-20 08:40:55,889 - token_downloader - INFO - Retrying in 3 seconds...
2025-05-20 08:40:58,886 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 2/10)...
2025-05-20 08:40:58,887 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-05-20 08:40:58,888 - token_downloader - INFO - Using service account to list files
2025-05-20 08:40:58,893 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 2/10)...
2025-05-20 08:40:58,893 - token_downloader - INFO - Using service account to list files
2025-05-20 08:40:59,528 - token_downloader - INFO - Found 1 files in the folder using service account
2025-05-20 08:40:59,528 - token_downloader - INFO - Found file: token_20250520_082848.json (Modified: 2025-05-20T01:28:49.330Z)
2025-05-20 08:40:59,529 - token_downloader - INFO - Downloading token file: token_20250520_082848.json (Modified: 2025-05-20T01:28:49.330Z, ID: 1oA4DJbTONyvv5-M94uxLdnsIkWBKpgOt)
2025-05-20 08:40:59,529 - token_downloader - INFO - Using service account to download token
2025-05-20 08:41:02,187 - token_downloader - ERROR - Error using service account: [SSL] internal error (_ssl.c:2648)
2025-05-20 08:41:02,187 - token_downloader - INFO - Download progress: 100%
2025-05-20 08:41:02,188 - token_downloader - INFO - Processing token data from file: token_20250520_082848.json
2025-05-20 08:41:02,189 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-05-20 08:41:02,189 - token_downloader - INFO - Token expiry: 2025-05-20T02:28:45.435646Z
2025-05-20 08:41:02,192 - token_downloader - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py", line 446, in download_token
    status, done = downloader.next_chunk()
                   ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\_helpers.py", line 130, in positional_wrapper
    return wrapped(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 741, in next_chunk
    resp, content = _retry_request(
                    ~~~~~~~~~~~~~~^
        http,
        ^^^^^
    ...<6 lines>...
        headers=headers,
        ^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 222, in _retry_request
    raise exception
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 191, in _retry_request
    resp, content = http.request(uri, method, *args, **kwargs)
                    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\google_auth_httplib2.py", line 218, in request
    response, content = self.http.request(
                        ~~~~~~~~~~~~~~~~~^
        uri,
        ^^^^
    ...<5 lines>...
        **kwargs
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1724, in request
    (response, content) = self._request(
                          ~~~~~~~~~~~~~^
        conn, authority, uri, request_uri, method, body, headers, redirections, cachekey,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1444, in _request
    (response, content) = self._conn_request(conn, request_uri, method, body, headers)
                          ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1396, in _conn_request
    response = conn.getresponse()
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\ssl.py", line 1304, in recv_into
    return self.read(nbytes, buffer)
           ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\ssl.py", line 1138, in read
    return self._sslobj.read(len, buffer)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
ssl.SSLError: [SSL: WRONG_VERSION_NUMBER] wrong version number (_ssl.c:2648)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py", line 163, in get_token_list_from_folder
    ).execute()
      ~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\_helpers.py", line 130, in positional_wrapper
    return wrapped(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 923, in execute
    resp, content = _retry_request(
                    ~~~~~~~~~~~~~~^
        http,
        ^^^^^
    ...<7 lines>...
        headers=self.headers,
        ^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 222, in _retry_request
    raise exception
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 191, in _retry_request
    resp, content = http.request(uri, method, *args, **kwargs)
                    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\google_auth_httplib2.py", line 218, in request
    response, content = self.http.request(
                        ~~~~~~~~~~~~~~~~~^
        uri,
        ^^^^
    ...<5 lines>...
        **kwargs
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1724, in request
    (response, content) = self._request(
                          ~~~~~~~~~~~~~^
        conn, authority, uri, request_uri, method, body, headers, redirections, cachekey,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1444, in _request
    (response, content) = self._conn_request(conn, request_uri, method, body, headers)
                          ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1396, in _conn_request
    response = conn.getresponse()
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\ssl.py", line 1304, in recv_into
    return self.read(nbytes, buffer)
           ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\ssl.py", line 1138, in read
    return self._sslobj.read(len, buffer)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
ssl.SSLError: [SSL: WRONG_VERSION_NUMBER] wrong version number (_ssl.c:2648)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py", line 163, in get_token_list_from_folder
    ).execute()
      ~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\_helpers.py", line 130, in positional_wrapper
    return wrapped(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 923, in execute
    resp, content = _retry_request(
                    ~~~~~~~~~~~~~~^
        http,
        ^^^^^
    ...<7 lines>...
        headers=self.headers,
        ^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 222, in _retry_request
    raise exception
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\googleapiclient\http.py", line 191, in _retry_request
    resp, content = http.request(uri, method, *args, **kwargs)
                    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\google_auth_httplib2.py", line 218, in request
    response, content = self.http.request(
                        ~~~~~~~~~~~~~~~~~^
        uri,
        ^^^^
    ...<5 lines>...
        **kwargs
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1724, in request
    (response, content) = self._request(
                          ~~~~~~~~~~~~~^
        conn, authority, uri, request_uri, method, body, headers, redirections, cachekey,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1444, in _request
    (response, content) = self._conn_request(conn, request_uri, method, body, headers)
                          ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\httplib2\__init__.py", line 1396, in _conn_request
    response = conn.getresponse()
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\ssl.py", line 1304, in recv_into
    return self.read(nbytes, buffer)
           ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\Lib\ssl.py", line 1138, in read
    return self._sslobj.read(len, buffer)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
ssl.SSLError: [SSL] internal error (_ssl.c:2648)

2025-05-20 08:41:02,201 - token_downloader - INFO - Successfully downloaded and validated new token
2025-05-20 08:41:02,202 - token_downloader - INFO - Retrying in 3 seconds...
2025-05-20 08:41:02,202 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-05-20 08:41:02,222 - IFESS-Config - INFO - Building Google Drive API service...
2025-05-20 08:41:02,224 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-20 08:41:02,225 - IFESS-Config - INFO - Google Drive API service built successfully
2025-05-20 08:41:03,149 - IFESS-Config - INFO - Logged in as: PTRJ IFES (<EMAIL>)
2025-05-20 08:41:03,150 - IFESS-Config - INFO - Storage: 70.75 GB used of 100.00 GB total
2025-05-20 08:41:03,150 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-05-20 08:41:05,203 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 3/10)...
2025-05-20 08:41:05,203 - token_downloader - INFO - Using service account to list files
2025-05-20 08:41:05,701 - token_downloader - INFO - Found 1 files in the folder using service account
2025-05-20 08:41:05,701 - token_downloader - INFO - Found file: token_20250520_082848.json (Modified: 2025-05-20T01:28:49.330Z)
2025-05-20 08:41:05,702 - token_downloader - INFO - Downloading token file: token_20250520_082848.json (Modified: 2025-05-20T01:28:49.330Z, ID: 1oA4DJbTONyvv5-M94uxLdnsIkWBKpgOt)
2025-05-20 08:41:05,702 - token_downloader - INFO - Using service account to download token
2025-05-20 08:41:08,360 - token_downloader - INFO - Download progress: 100%
2025-05-20 08:41:08,360 - token_downloader - INFO - Processing token data from file: token_20250520_082848.json
2025-05-20 08:41:08,362 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-05-20 08:41:08,362 - token_downloader - INFO - Token expiry: 2025-05-20T02:28:45.435646Z
2025-05-20 08:41:08,365 - token_downloader - INFO - Successfully downloaded and validated new token
2025-05-20 08:41:08,365 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-05-20 08:41:08,366 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-05-20 08:41:38,022 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-05-20 08:41:38,023 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-06-05 08:08:00,071 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-05 08:08:00,281 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-06-05 08:08:00,282 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-05 08:08:00,282 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-06-05 08:08:00,588 - IFESS-Config - INFO - token_downloader module imported successfully
2025-06-05 08:08:00,589 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-06-05 08:08:00,589 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-05 08:08:00,590 - token_downloader - INFO - Token expires in 58.4 minutes (threshold: 60.0 minutes)
2025-06-05 08:08:00,591 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-06-05 08:08:00,591 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-05 08:08:00,627 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-06-05 08:08:00,632 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-05 08:08:00,639 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-06-05 08:08:00,641 - token_downloader - INFO - Using service account to list files
2025-06-05 08:08:01,690 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-05 08:08:01,691 - token_downloader - INFO - Found file: token_20250605_080716.json (Modified: 2025-06-05T01:07:17.514Z)
2025-06-05 08:08:01,691 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-06-05 08:08:01,693 - token_downloader - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py", line 286, in process_token_files
    max_age = datetime.timedelta(days=MAX_TOKEN_AGE_DAYS)
              ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-06-05 08:08:01,693 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-06-05 08:08:01,694 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-05 08:08:01,694 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-05 08:08:04,488 - IFESS-Config - INFO - Using token_downloader to get token from Google Drive
2025-06-05 08:08:04,490 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-05 08:08:04,491 - token_downloader - INFO - Token expires in 58.3 minutes (threshold: 60.0 minutes)
2025-06-05 08:08:04,491 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-06-05 08:08:04,491 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-05 08:08:04,492 - token_downloader - INFO - Using service account to list files
2025-06-05 08:08:04,854 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-05 08:08:04,855 - token_downloader - INFO - Found file: token_20250605_080716.json (Modified: 2025-06-05T01:07:17.514Z)
2025-06-05 08:08:04,855 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-06-05 08:08:04,856 - token_downloader - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py", line 286, in process_token_files
    max_age = datetime.timedelta(days=MAX_TOKEN_AGE_DAYS)
              ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-06-05 08:08:04,857 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-06-05 08:08:04,857 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-05 08:08:04,861 - IFESS-Config - INFO - Building Google Drive API service...
2025-06-05 08:08:04,862 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-05 08:08:04,864 - IFESS-Config - INFO - Google Drive API service built successfully
2025-06-05 08:08:05,498 - IFESS-Config - INFO - Logged in as: PTRJ IFES (<EMAIL>)
2025-06-05 08:08:05,499 - IFESS-Config - INFO - Storage: 70.75 GB used of 100.00 GB total
2025-06-05 08:08:05,500 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-05 08:08:11,286 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-05 08:08:11,287 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-06-05 08:08:56,232 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-05 08:08:56,243 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-06-05 12:33:38,975 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-05 12:33:39,135 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-06-05 12:33:39,136 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-05 12:33:39,136 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-06-05 12:33:39,595 - IFESS-Config - INFO - token_downloader module imported successfully
2025-06-05 12:33:39,595 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-06-05 12:33:39,595 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-05 12:33:39,597 - token_downloader - INFO - Token expires in 32.3 minutes (threshold: 60.0 minutes)
2025-06-05 12:33:39,597 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-06-05 12:33:39,597 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-05 12:33:39,635 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-06-05 12:33:39,639 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-05 12:33:39,655 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-06-05 12:33:39,656 - token_downloader - INFO - Using service account to list files
2025-06-05 12:33:40,913 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-05 12:33:40,913 - token_downloader - INFO - Found file: token_20250605_120827.json (Modified: 2025-06-05T05:08:28.171Z)
2025-06-05 12:33:40,913 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-06-05 12:33:40,915 - token_downloader - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py", line 286, in process_token_files
    max_age = datetime.timedelta(days=MAX_TOKEN_AGE_DAYS)
              ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-06-05 12:33:40,916 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-06-05 12:33:40,916 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-05 12:33:40,916 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-05 12:33:42,371 - IFESS-Config - INFO - Using token_downloader to get token from Google Drive
2025-06-05 12:33:42,374 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-05 12:33:42,374 - token_downloader - INFO - Token expires in 32.3 minutes (threshold: 60.0 minutes)
2025-06-05 12:33:42,375 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-06-05 12:33:42,375 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-05 12:33:42,375 - token_downloader - INFO - Using service account to list files
2025-06-05 12:33:42,728 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-05 12:33:42,728 - token_downloader - INFO - Found file: token_20250605_120827.json (Modified: 2025-06-05T05:08:28.171Z)
2025-06-05 12:33:42,729 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-06-05 12:33:42,729 - token_downloader - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py", line 286, in process_token_files
    max_age = datetime.timedelta(days=MAX_TOKEN_AGE_DAYS)
              ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-06-05 12:33:42,730 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-06-05 12:33:42,730 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-05 12:33:42,733 - IFESS-Config - INFO - Building Google Drive API service...
2025-06-05 12:33:42,734 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-05 12:33:42,736 - IFESS-Config - INFO - Google Drive API service built successfully
2025-06-05 12:33:43,309 - IFESS-Config - INFO - Logged in as: PTRJ IFES (<EMAIL>)
2025-06-05 12:33:43,309 - IFESS-Config - INFO - Storage: 70.81 GB used of 100.00 GB total
2025-06-05 12:33:43,311 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-05 12:41:47,850 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-05 12:41:48,005 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-06-05 12:41:48,006 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-05 12:41:48,006 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-06-05 12:41:48,488 - IFESS-Config - INFO - token_downloader module imported successfully
2025-06-05 12:41:48,488 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-06-05 12:41:48,489 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-05 12:41:48,489 - token_downloader - INFO - Token expires in 26.6 minutes (threshold: 60.0 minutes)
2025-06-05 12:41:48,489 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-06-05 12:41:48,489 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-05 12:41:48,526 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-06-05 12:41:48,531 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-05 12:41:48,541 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-06-05 12:41:48,541 - token_downloader - INFO - Using service account to list files
2025-06-05 12:41:49,963 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-05 12:41:49,963 - token_downloader - INFO - Found file: token_20250605_120827.json (Modified: 2025-06-05T05:08:28.171Z)
2025-06-05 12:41:49,964 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-06-05 12:41:49,965 - token_downloader - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py", line 286, in process_token_files
    max_age = datetime.timedelta(days=MAX_TOKEN_AGE_DAYS)
              ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-06-05 12:41:49,965 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-06-05 12:41:49,966 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-05 12:41:49,966 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-05 12:41:54,032 - IFESS-Config - INFO - Using token_downloader to get token from Google Drive
2025-06-05 12:41:54,035 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-05 12:41:54,036 - token_downloader - INFO - Token expires in 26.5 minutes (threshold: 60.0 minutes)
2025-06-05 12:41:54,037 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-06-05 12:41:54,037 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-05 12:41:54,037 - token_downloader - INFO - Using service account to list files
2025-06-05 12:41:54,481 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-05 12:41:54,482 - token_downloader - INFO - Found file: token_20250605_120827.json (Modified: 2025-06-05T05:08:28.171Z)
2025-06-05 12:41:54,482 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-06-05 12:41:54,483 - token_downloader - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py", line 286, in process_token_files
    max_age = datetime.timedelta(days=MAX_TOKEN_AGE_DAYS)
              ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-06-05 12:41:54,484 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-06-05 12:41:54,484 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-05 12:41:54,490 - IFESS-Config - INFO - Building Google Drive API service...
2025-06-05 12:41:54,495 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-05 12:41:54,501 - IFESS-Config - INFO - Google Drive API service built successfully
2025-06-05 12:41:55,456 - IFESS-Config - INFO - Logged in as: PTRJ IFES (<EMAIL>)
2025-06-05 12:41:55,457 - IFESS-Config - INFO - Storage: 70.81 GB used of 100.00 GB total
2025-06-05 12:41:55,458 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-05 12:41:58,339 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-05 12:41:58,340 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-06-05 12:42:05,976 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-05 12:42:05,977 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-06-05 14:32:02,653 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-05 14:32:02,867 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-05 14:32:02,868 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-06-05 14:32:03,459 - IFESS-Config - INFO - token_downloader module imported successfully
2025-06-05 14:32:03,459 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-06-05 14:32:03,460 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-05 14:32:03,461 - token_downloader - INFO - Token expires in 36.9 minutes (threshold: 60.0 minutes)
2025-06-05 14:32:03,461 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-06-05 14:32:03,461 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-05 14:32:03,507 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-06-05 14:32:03,510 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-05 14:32:03,518 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-06-05 14:32:03,518 - token_downloader - INFO - Using service account to list files
2025-06-05 14:32:04,614 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-05 14:32:04,614 - token_downloader - INFO - Found file: token_20250605_140859.json (Modified: 2025-06-05T07:09:00.889Z)
2025-06-05 14:32:04,614 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-06-05 14:32:04,615 - token_downloader - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py", line 286, in process_token_files
    max_age = datetime.timedelta(days=MAX_TOKEN_AGE_DAYS)
              ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-06-05 14:32:04,615 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-06-05 14:32:04,616 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-05 14:32:04,616 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-05 14:32:14,658 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-05 14:32:14,659 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-06-05 15:04:50,389 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-05 15:04:50,521 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-05 15:04:50,521 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-06-05 15:04:50,925 - IFESS-Config - INFO - token_downloader module imported successfully
2025-06-05 15:04:50,926 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-06-05 15:04:50,926 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-05 15:04:50,926 - token_downloader - INFO - Token expires in 4.1 minutes (threshold: 60.0 minutes)
2025-06-05 15:04:50,927 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-06-05 15:04:50,927 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-05 15:04:50,962 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-06-05 15:04:50,974 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-05 15:04:50,982 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-06-05 15:04:50,983 - token_downloader - INFO - Using service account to list files
2025-06-05 15:04:52,042 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-05 15:04:52,043 - token_downloader - INFO - Found file: token_20250605_140859.json (Modified: 2025-06-05T07:09:00.889Z)
2025-06-05 15:04:52,043 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-06-05 15:04:52,044 - token_downloader - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py", line 286, in process_token_files
    max_age = datetime.timedelta(days=MAX_TOKEN_AGE_DAYS)
              ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-06-05 15:04:52,045 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-06-05 15:04:52,045 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-05 15:04:52,045 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-05 15:05:00,980 - IFESS-Config - INFO - Using token_downloader to get token from Google Drive
2025-06-05 15:05:00,980 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-05 15:05:00,981 - token_downloader - INFO - Token expires in 3.9 minutes (threshold: 60.0 minutes)
2025-06-05 15:05:00,981 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-06-05 15:05:00,981 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-05 15:05:00,981 - token_downloader - INFO - Using service account to list files
2025-06-05 15:05:01,342 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-05 15:05:01,342 - token_downloader - INFO - Found file: token_20250605_140859.json (Modified: 2025-06-05T07:09:00.889Z)
2025-06-05 15:05:01,343 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-06-05 15:05:01,344 - token_downloader - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py", line 286, in process_token_files
    max_age = datetime.timedelta(days=MAX_TOKEN_AGE_DAYS)
              ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-06-05 15:05:01,346 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-06-05 15:05:01,347 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-05 15:05:01,361 - IFESS-Config - INFO - Building Google Drive API service...
2025-06-05 15:05:01,363 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-05 15:05:01,368 - IFESS-Config - INFO - Google Drive API service built successfully
2025-06-05 15:05:02,003 - IFESS-Config - INFO - Logged in as: PTRJ IFES (<EMAIL>)
2025-06-05 15:05:02,003 - IFESS-Config - INFO - Storage: 70.96 GB used of 100.00 GB total
2025-06-05 15:05:02,004 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-06 12:49:44,364 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-06 12:49:44,531 - IFESS-Config - INFO - Configuration loaded from OAuth config: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config_oauth_tokens.json
2025-06-06 12:49:44,531 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-06-06 12:49:44,531 - IFESS-Config - INFO - Loaded Google Drive token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-06 12:49:44,531 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config_oauth_tokens.json
2025-06-06 12:49:44,532 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-06-06 12:49:47,064 - IFESS-Config - INFO - token_downloader module imported successfully
2025-06-06 12:49:47,064 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-06-06 12:49:47,065 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-06 12:49:47,065 - token_downloader - ERROR - Token is already expired
2025-06-06 12:49:47,065 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-06-06 12:49:47,065 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-06 12:49:47,065 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/10)...
2025-06-06 12:49:47,066 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-06 12:49:47,099 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-06-06 12:49:47,131 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-06 12:49:47,163 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-06-06 12:49:47,164 - token_downloader - INFO - Using service account to list files
2025-06-06 12:49:48,403 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-06 12:49:48,404 - token_downloader - INFO - Found file: token_20250606_121458.json (Modified: 2025-06-06T05:14:58.984Z)
2025-06-06 12:49:48,405 - token_downloader - INFO - Downloading token file: token_20250606_121458.json (Modified: 2025-06-06T05:14:58.984Z, ID: 14sqDzEobQJSJf2fD20JQl--oFnwC_Fkp)
2025-06-06 12:49:48,406 - token_downloader - INFO - Using service account to download token
2025-06-06 12:49:51,209 - token_downloader - INFO - Download progress: 100%
2025-06-06 12:49:51,209 - token_downloader - INFO - Processing token data from file: token_20250606_121458.json
2025-06-06 12:49:51,210 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-06 12:49:51,211 - token_downloader - INFO - Token expiry: 2025-06-06T06:14:55.059635Z
2025-06-06 12:49:51,223 - token_downloader - INFO - Successfully downloaded and validated new token
2025-06-06 12:49:51,223 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-06 12:49:51,224 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-06 12:49:53,572 - IFESS-Config - INFO - Using token_downloader to get token from Google Drive
2025-06-06 12:49:53,573 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-06 12:49:53,574 - token_downloader - INFO - Token expires in 25.0 minutes (threshold: 60.0 minutes)
2025-06-06 12:49:53,574 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-06-06 12:49:53,575 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-06 12:49:53,575 - token_downloader - INFO - Using service account to list files
2025-06-06 12:49:54,081 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-06 12:49:54,082 - token_downloader - INFO - Found file: token_20250606_121458.json (Modified: 2025-06-06T05:14:58.984Z)
2025-06-06 12:49:54,082 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-06-06 12:49:54,084 - token_downloader - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py", line 286, in process_token_files
    max_age = datetime.timedelta(days=MAX_TOKEN_AGE_DAYS)
              ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-06-06 12:49:54,085 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-06-06 12:49:54,085 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-06 12:49:54,103 - IFESS-Config - INFO - Building Google Drive API service...
2025-06-06 12:49:54,106 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-06 12:49:54,107 - IFESS-Config - INFO - Google Drive API service built successfully
2025-06-06 12:49:54,697 - IFESS-Config - INFO - Logged in as: PTRJ IFES (<EMAIL>)
2025-06-06 12:49:54,697 - IFESS-Config - INFO - Storage: 71.05 GB used of 100.00 GB total
2025-06-06 12:49:54,697 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-06 13:15:55,110 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-06 13:15:55,110 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-06-07 08:35:38,377 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-07 08:35:38,665 - IFESS-Config - INFO - Configuration loaded from OAuth config: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config_oauth_tokens.json
2025-06-07 08:35:38,666 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-06-07 08:35:38,667 - IFESS-Config - INFO - Loaded Google Drive token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-07 08:35:38,667 - IFESS-Config - INFO - Loaded scheduled upload settings: enabled=True, time=7:53, service=gdrive
2025-06-07 08:35:38,667 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config_oauth_tokens.json
2025-06-07 08:35:38,668 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-06-07 08:35:44,502 - IFESS-Config - INFO - token_downloader module imported successfully
2025-06-07 08:35:44,502 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-06-07 08:35:44,503 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-07 08:35:44,536 - token_downloader - ERROR - Token is already expired
2025-06-07 08:35:44,536 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-06-07 08:35:44,538 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-07 08:35:44,539 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/10)...
2025-06-07 08:35:44,540 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-07 08:35:44,680 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-06-07 08:35:44,737 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-07 08:35:44,786 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-06-07 08:35:44,786 - token_downloader - INFO - Using service account to list files
2025-06-07 08:35:46,507 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-07 08:35:46,508 - token_downloader - INFO - Found file: token_20250607_083127.json (Modified: 2025-06-07T01:31:27.967Z)
2025-06-07 08:35:46,508 - token_downloader - INFO - Downloading token file: token_20250607_083127.json (Modified: 2025-06-07T01:31:27.967Z, ID: 1cmSJmLYnhOkOxmO1cGzzi6NtLQma3QXh)
2025-06-07 08:35:46,508 - token_downloader - INFO - Using service account to download token
2025-06-07 08:35:49,128 - token_downloader - INFO - Download progress: 100%
2025-06-07 08:35:49,129 - token_downloader - INFO - Processing token data from file: token_20250607_083127.json
2025-06-07 08:35:49,129 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-07 08:35:49,130 - token_downloader - INFO - Token expiry: 2025-06-07T02:31:24.022306Z
2025-06-07 08:35:49,149 - token_downloader - INFO - Successfully downloaded and validated new token
2025-06-07 08:35:49,150 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-07 08:35:49,152 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-07 08:39:25,760 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-07 08:39:25,895 - IFESS-Config - INFO - Configuration loaded from OAuth config: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config_oauth_tokens.json
2025-06-07 08:39:25,896 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-06-07 08:39:25,896 - IFESS-Config - INFO - Loaded Google Drive token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-07 08:39:25,896 - IFESS-Config - INFO - Loaded scheduled upload settings: enabled=True, time=7:53, service=gdrive
2025-06-07 08:39:25,896 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config_oauth_tokens.json
2025-06-07 08:39:25,898 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-06-07 08:39:26,439 - IFESS-Config - INFO - token_downloader module imported successfully
2025-06-07 08:39:26,439 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-06-07 08:39:26,440 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-07 08:39:26,440 - token_downloader - INFO - Token expires in 52.0 minutes (threshold: 60.0 minutes)
2025-06-07 08:39:26,440 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-06-07 08:39:26,440 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-07 08:39:26,481 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-06-07 08:39:26,485 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-07 08:39:26,493 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-06-07 08:39:26,493 - token_downloader - INFO - Using service account to list files
2025-06-07 08:39:27,591 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-07 08:39:27,591 - token_downloader - INFO - Found file: token_20250607_083127.json (Modified: 2025-06-07T01:31:27.967Z)
2025-06-07 08:39:27,591 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-06-07 08:39:27,593 - token_downloader - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py", line 286, in process_token_files
    max_age = datetime.timedelta(days=MAX_TOKEN_AGE_DAYS)
              ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-06-07 08:39:27,593 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-06-07 08:39:27,594 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-07 08:39:27,594 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-07 08:39:31,366 - IFESS-Config - INFO - Testing database connection with:
2025-06-07 08:39:31,367 - IFESS-Config - INFO -   DB Path: D:/Gawean Rebinmas/Monitoring Database/Database Ifess/PTRJ_AB1_08042025/PTRJ_AB1.FDB
2025-06-07 08:39:31,368 - IFESS-Config - INFO -   Username: sysdba
2025-06-07 08:39:31,368 - IFESS-Config - INFO -   ISQL Path: C:/Program Files (x86)/Firebird/Firebird_1_5/bin/isql.exe
2025-06-07 08:39:31,368 - IFESS-Config - INFO -   Use localhost: True
2025-06-07 08:39:31,568 - IFESS-Config - INFO - test_connection result: True
2025-06-07 08:39:43,201 - IFESS-Config - INFO - Using token_downloader to get token from Google Drive
2025-06-07 08:39:43,202 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-07 08:39:43,203 - token_downloader - INFO - Token expires in 51.7 minutes (threshold: 60.0 minutes)
2025-06-07 08:39:43,203 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-06-07 08:39:43,203 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-07 08:39:43,203 - token_downloader - INFO - Using service account to list files
2025-06-07 08:39:43,638 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-07 08:39:43,639 - token_downloader - INFO - Found file: token_20250607_083127.json (Modified: 2025-06-07T01:31:27.967Z)
2025-06-07 08:39:43,639 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-06-07 08:39:43,640 - token_downloader - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py", line 286, in process_token_files
    max_age = datetime.timedelta(days=MAX_TOKEN_AGE_DAYS)
              ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-06-07 08:39:43,640 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-06-07 08:39:43,640 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-07 08:39:43,656 - IFESS-Config - INFO - Building Google Drive API service...
2025-06-07 08:39:43,658 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-07 08:39:43,662 - IFESS-Config - INFO - Google Drive API service built successfully
2025-06-07 08:39:44,344 - IFESS-Config - INFO - Logged in as: PTRJ IFES (<EMAIL>)
2025-06-07 08:39:44,344 - IFESS-Config - INFO - Storage: 71.35 GB used of 100.00 GB total
2025-06-07 08:39:44,345 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-06-07 08:39:57,973 - IFESS-Config - INFO - Using OAuth config as primary configuration file
2025-06-07 08:39:57,974 - IFESS-Config - INFO - Configuration saved to primary file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config_oauth_tokens.json
2025-06-07 08:39:57,975 - IFESS-Config - INFO - Configuration also saved to secondary file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-07 08:39:57,976 - IFESS-Config - INFO - Configuration saved successfully
2025-06-07 08:39:57,976 - IFESS-Config - INFO - Primary config file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config_oauth_tokens.json
2025-06-07 08:39:57,977 - IFESS-Config - INFO - MEGA credentials saved for: <EMAIL>
2025-06-07 08:40:00,491 - IFESS-Config - INFO - Using OAuth config as primary configuration file
2025-06-07 08:40:00,492 - IFESS-Config - INFO - Configuration saved to primary file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config_oauth_tokens.json
2025-06-07 08:40:00,493 - IFESS-Config - INFO - Configuration also saved to secondary file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json
2025-06-07 08:40:00,493 - IFESS-Config - INFO - Configuration saved successfully
2025-06-07 08:40:00,493 - IFESS-Config - INFO - Primary config file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config_oauth_tokens.json
2025-06-07 08:40:00,493 - IFESS-Config - INFO - MEGA credentials saved for: <EMAIL>
