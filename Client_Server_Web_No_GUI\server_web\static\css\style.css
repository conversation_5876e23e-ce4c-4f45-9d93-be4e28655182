/* Custom styles for Firebird Query Server Web Interface */

/* General styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.footer {
    margin-top: 2rem;
    padding: 1rem 0;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

/* Card styles */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: none;
    margin-bottom: 1.5rem;
}

.card-header {
    font-weight: 500;
}

/* Table styles */
.table {
    margin-bottom: 0;
}

.table th {
    background-color: #f8f9fa;
    border-bottom-width: 1px;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Query preview */
.query-preview {
    font-family: 'Consolas', 'Courier New', monospace;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300px;
}

/* Pre for code display */
pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    padding: 1rem;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 0.875rem;
    overflow-x: auto;
}

/* Results container */
.results-container {
    max-height: 500px;
    overflow-y: auto;
}

/* Query result table optimizations */
.query-result-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: auto; /* Use auto layout to accommodate headers properly */
    border: 1px solid #dee2e6;
    min-width: 100%;
}

.query-result-table th {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 8px 12px;
    position: sticky;
    top: 0;
    z-index: 10;
    font-weight: 600;
    white-space: nowrap;
    /* Remove overflow constraints on headers to show full column names */
    min-width: max-content;
}

.query-result-table td {
    border: 1px solid #dee2e6;
    padding: 6px 8px;
    vertical-align: top;
}

.query-result-table .cell-content {
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    width: 100%;
}

.query-result-table.text-wrapped .cell-content {
    white-space: normal;
    word-break: break-word;
}

/* Table wrapper with horizontal scrolling */
.table-wrapper {
    width: 100%;
    overflow-x: auto;
    position: relative;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Table controls */
.table-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    flex-wrap: wrap;
}

.table-info {
    margin-bottom: 0.5rem;
}

/* Optimize for performance */
.query-result-table tbody tr:nth-child(even) {
    background-color: rgba(0,0,0,0.02);
}

/* Reduce repaints */
.query-result-table tbody tr {
    will-change: contents;
    contain: content;
}

/* Optimize search highlighting */
.bg-warning {
    background-color: #fff3cd !important;
    border-radius: 2px;
    padding: 0 2px;
    display: inline;
}

/* Server status indicator */
.server-status .badge {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

/* Navbar styles */
.navbar-brand {
    font-weight: 600;
}

.nav-link {
    font-weight: 500;
}

/* Button styles */
.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* Modal styles */
.modal-header {
    border-bottom: 0;
}

.modal-footer {
    border-top: 0;
}

/* List group styles */
.list-group-item-action:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Tab styles */
.nav-tabs .nav-link {
    color: #495057;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    color: #0d6efd;
    font-weight: 600;
}

/* Alert styles */
.alert {
    border: none;
    border-radius: 0.25rem;
}

/* Form styles */
.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Badge styles */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
}

/* Spinner styles */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: 0.2em;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-title {
        font-size: 1rem;
    }

    .table th, .table td {
        padding: 0.5rem;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}
