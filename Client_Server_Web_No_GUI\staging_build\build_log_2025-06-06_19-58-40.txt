IFESS Client Suite - Comprehensive Build Log 
Build started: 06/06/2025 19:58:40,15 
================================================ 
 
Current directory: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build 
[1/7] Performing pre-build checks... 
Python 3.13.3
PyInstaller command line access verified 
WARNING: UPX not found - compression will be disabled 
Checking source files... 
Found: ifess_client_hidden.py 
Found: ifess_config_gui.py 
Pre-build checks completed successfully 
[2/7] Cleaning build environment... 
Removing old dist directory... 
Removing old build directory... 
Build environment cleaned 
[3/7] Checking and installing dependencies... 
Installing/updating Python dependencies... 
Installing: google-api-python-client 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: google-api-python-client in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (2.171.0)
Requirement already satisfied: httplib2<1.0.0,>=0.19.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-python-client) (0.22.0)
Requirement already satisfied: google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-python-client) (2.40.3)
Requirement already satisfied: google-auth-httplib2<1.0.0,>=0.2.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-python-client) (0.2.0)
Requirement already satisfied: google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-python-client) (2.24.2)
Requirement already satisfied: uritemplate<5,>=3.0.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-python-client) (4.1.1)
Requirement already satisfied: googleapis-common-protos<2.0.0,>=1.56.2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (1.70.0)
Requirement already satisfied: protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<7.0.0,>=3.19.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (5.29.5)
Requirement already satisfied: proto-plus<2.0.0,>=1.22.3 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (1.26.1)
Requirement already satisfied: requests<3.0.0,>=2.18.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (2.32.3)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0->google-api-python-client) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0->google-api-python-client) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0->google-api-python-client) (4.9)
Requirement already satisfied: pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from httplib2<1.0.0,>=0.19.0->google-api-python-client) (3.2.1)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (1.26.20)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (2025.1.31)
Requirement already satisfied: pyasn1>=0.1.3 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from rsa<5,>=3.1.4->google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0->google-api-python-client) (0.6.1)
Installing: google-auth 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: google-auth in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (2.40.3)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth) (4.9)
Requirement already satisfied: pyasn1>=0.1.3 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from rsa<5,>=3.1.4->google-auth) (0.6.1)
Installing: google-auth-oauthlib 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: google-auth-oauthlib in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (1.2.2)
Requirement already satisfied: google-auth>=2.15.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth-oauthlib) (2.40.3)
Requirement already satisfied: requests-oauthlib>=0.7.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth-oauthlib) (2.0.0)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth>=2.15.0->google-auth-oauthlib) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth>=2.15.0->google-auth-oauthlib) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth>=2.15.0->google-auth-oauthlib) (4.9)
Requirement already satisfied: pyasn1>=0.1.3 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from rsa<5,>=3.1.4->google-auth>=2.15.0->google-auth-oauthlib) (0.6.1)
Requirement already satisfied: oauthlib>=3.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests-oauthlib>=0.7.0->google-auth-oauthlib) (3.2.2)
Requirement already satisfied: requests>=2.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests-oauthlib>=0.7.0->google-auth-oauthlib) (2.32.3)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib) (1.26.20)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib) (2025.1.31)
Installing: google-auth-httplib2 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: google-auth-httplib2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (0.2.0)
Requirement already satisfied: google-auth in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth-httplib2) (2.40.3)
Requirement already satisfied: httplib2>=0.19.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth-httplib2) (0.22.0)
Requirement already satisfied: pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from httplib2>=0.19.0->google-auth-httplib2) (3.2.1)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth->google-auth-httplib2) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth->google-auth-httplib2) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth->google-auth-httplib2) (4.9)
Requirement already satisfied: pyasn1>=0.1.3 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from rsa<5,>=3.1.4->google-auth->google-auth-httplib2) (0.6.1)
Installing: requests 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: requests in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (2.32.3)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests) (1.26.20)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests) (2025.1.31)
Installing: fdb 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: fdb in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (2.0.3)
Requirement already satisfied: firebird-base~=2.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from fdb) (2.0.2)
Requirement already satisfied: python-dateutil~=2.8 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from fdb) (2.9.0.post0)
Requirement already satisfied: protobuf~=5.29 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from firebird-base~=2.0->fdb) (5.29.5)
Requirement already satisfied: six>=1.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from python-dateutil~=2.8->fdb) (1.17.0)
Installing: schedule 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: schedule in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (1.2.2)
Dependencies check completed 
[4/7] Building IFESS Hidden Client... 
Building ifess_client_hidden.exe... 
352 INFO: PyInstaller: 6.14.0, contrib hooks: 2025.4
353 INFO: Python: 3.13.3
439 INFO: Platform: Windows-11-10.0.26100-SP0
439 INFO: Python environment: C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0
450 INFO: Module search paths (PYTHONPATH):
['D:\\Gawean Rebinmas\\Monitoring '
 'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\python313.zip',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\win32',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\win32\\lib',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Pythonwin',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\site-packages',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\setuptools\\_vendor',
 'D:\\Gawean Rebinmas\\Monitoring '
 'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build',
 'D:\\Gawean Rebinmas\\Monitoring '
 'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build']
1229 INFO: Appending 'datas' from .spec
1230 INFO: checking Analysis
1231 INFO: Building Analysis because Analysis-00.toc is non existent
1231 INFO: Running Analysis Analysis-00.toc
1231 INFO: Target bytecode optimization level: 0
1231 INFO: Initializing module dependency graph...
1233 INFO: Initializing module graph hook caches...
1248 INFO: Analyzing modules for base_library.zip ...
3438 INFO: Processing standard module hook 'hook-encodings.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
5661 INFO: Processing standard module hook 'hook-pickle.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
6517 INFO: Processing standard module hook 'hook-heapq.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
7013 INFO: Caching module dependency graph...
7061 INFO: Looking for Python shared library...
7061 INFO: Using Python shared library: C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\python313.dll
7061 INFO: Analyzing D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ifess_client_hidden.py

Syntax error in D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ifess_client_hidden.py
    co_ast = compile(contents, pathname, 'exec', ast.PyCF_ONLY_AST, True)
   File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ifess_client_hidden.py", line 169
     try:
 IndentationError: unexpected indent

Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config_oauth_tokens.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_backup.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_oauth_simple.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_module.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_simple.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_oauth.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_secret.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_secrets.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\common -> common
ERROR: Failed to build ifess_client_hidden.exe 
[5/7] Skipping debug client build (using batch file for debugging)... 
Debug functionality will be provided by debug_hidden_client.bat 
[6/7] Building IFESS Config GUI... 
Building ifess_config_gui.exe... 
344 INFO: PyInstaller: 6.14.0, contrib hooks: 2025.4
344 INFO: Python: 3.13.3
406 INFO: Platform: Windows-11-10.0.26100-SP0
406 INFO: Python environment: C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0
414 INFO: Module search paths (PYTHONPATH):
['D:\\Gawean Rebinmas\\Monitoring '
 'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\python313.zip',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\win32',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\win32\\lib',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Pythonwin',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\site-packages',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\setuptools\\_vendor',
 'D:\\Gawean Rebinmas\\Monitoring '
 'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build',
 'D:\\Gawean Rebinmas\\Monitoring '
 'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build']
1229 INFO: Appending 'datas' from .spec
1230 INFO: checking Analysis
1230 INFO: Building Analysis because Analysis-00.toc is non existent
1230 INFO: Running Analysis Analysis-00.toc
1230 INFO: Target bytecode optimization level: 0
1230 INFO: Initializing module dependency graph...
1232 INFO: Initializing module graph hook caches...
1247 INFO: Analyzing modules for base_library.zip ...
3551 INFO: Processing standard module hook 'hook-encodings.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
5612 INFO: Processing standard module hook 'hook-pickle.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
6560 INFO: Processing standard module hook 'hook-heapq.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
7014 INFO: Caching module dependency graph...
7073 INFO: Looking for Python shared library...
7074 INFO: Using Python shared library: C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\python313.dll
7074 INFO: Analyzing D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ifess_config_gui.py
7118 INFO: Processing pre-find-module-path hook 'hook-tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
7119 INFO: TclTkInfo: initializing cached Tcl/Tk info...
7436 INFO: Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
7555 INFO: Processing standard module hook 'hook-platform.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
7729 INFO: Processing standard module hook 'hook-urllib3.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
7780 INFO: Processing pre-safe-import-module hook 'hook-urllib3.packages.six.moves.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
8759 INFO: Processing standard module hook 'hook-charset_normalizer.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
8857 INFO: Processing pre-safe-import-module hook 'hook-typing_extensions.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
8857 INFO: SetuptoolsInfo: initializing cached setuptools info...
13920 INFO: Processing standard module hook 'hook-cryptography.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
15576 INFO: hook-cryptography: cryptography does not seem to be using dynamically linked OpenSSL.
15908 INFO: Processing standard module hook 'hook-certifi.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
16584 INFO: Processing standard module hook 'hook-difflib.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
16888 INFO: Processing standard module hook 'hook-sysconfig.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
17338 INFO: Processing standard module hook 'hook-multiprocessing.util.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
17416 INFO: Processing standard module hook 'hook-xml.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
18576 INFO: Processing standard module hook 'hook-pycparser.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
18957 INFO: Processing standard module hook 'hook-setuptools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
18971 INFO: Processing pre-safe-import-module hook 'hook-distutils.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
19005 INFO: Processing pre-safe-import-module hook 'hook-jaraco.functools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
19006 INFO: Setuptools: 'jaraco.functools' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.jaraco.functools'!
19015 INFO: Processing pre-safe-import-module hook 'hook-more_itertools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
19016 INFO: Setuptools: 'more_itertools' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.more_itertools'!
19169 INFO: Processing pre-safe-import-module hook 'hook-packaging.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
19274 INFO: Processing pre-safe-import-module hook 'hook-jaraco.text.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
19275 INFO: Setuptools: 'jaraco.text' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.jaraco.text'!
19288 INFO: Processing standard module hook 'hook-setuptools._vendor.jaraco.text.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
19290 INFO: Processing pre-safe-import-module hook 'hook-importlib_resources.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
19291 INFO: Processing pre-safe-import-module hook 'hook-jaraco.context.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
19292 INFO: Setuptools: 'jaraco.context' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.jaraco.context'!
19300 INFO: Processing pre-safe-import-module hook 'hook-backports.tarfile.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
19300 INFO: Setuptools: 'backports.tarfile' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.backports.tarfile'!
19405 INFO: Processing standard module hook 'hook-backports.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
19482 INFO: Processing pre-safe-import-module hook 'hook-importlib_metadata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
19483 INFO: Setuptools: 'importlib_metadata' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.importlib_metadata'!
19511 INFO: Processing standard module hook 'hook-setuptools._vendor.importlib_metadata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
19513 INFO: Processing pre-safe-import-module hook 'hook-zipp.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
19514 INFO: Setuptools: 'zipp' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.zipp'!
19837 INFO: Processing pre-safe-import-module hook 'hook-tomli.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
19837 INFO: Setuptools: 'tomli' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.tomli'!
20566 INFO: Processing standard module hook 'hook-pkg_resources.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
21253 INFO: Processing pre-safe-import-module hook 'hook-platformdirs.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
21262 INFO: Processing standard module hook 'hook-platformdirs.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
21527 INFO: Processing pre-safe-import-module hook 'hook-wheel.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
22384 INFO: Processing standard module hook 'hook-google.api_core.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
22453 INFO: Processing standard module hook 'hook-httplib2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
22889 INFO: Processing standard module hook 'hook-jinja2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
23458 INFO: Processing standard module hook 'hook-googleapiclient.model.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
23793 INFO: Processing standard module hook 'hook-dateutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
23957 INFO: Processing pre-safe-import-module hook 'hook-six.moves.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
24202 INFO: Processing standard module hook 'hook-Crypto.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
25369 INFO: Analyzing hidden import 'tkinter.scrolledtext'
25372 INFO: Analyzing hidden import 'tkinter.font'
25381 INFO: Analyzing hidden import 'fdb'
25960 INFO: Analyzing hidden import 'firebirdsql'
26138 INFO: Processing standard module hook 'hook-zoneinfo.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
26296 INFO: Analyzing hidden import 'google_auth_oauthlib'
26596 INFO: Processing module hooks (post-graph stage)...
26748 INFO: Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
26749 INFO: Processing standard module hook 'hook-tzdata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
27570 INFO: Performing binary vs. data reclassification (2168 entries)
27793 INFO: Looking for ctypes DLLs
27924 INFO: Analyzing run-time hooks ...
27930 INFO: Including run-time hook 'pyi_rth_inspect.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
27933 INFO: Including run-time hook 'pyi_rth_pkgutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
27936 INFO: Including run-time hook 'pyi_rth_multiprocessing.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
27938 INFO: Including run-time hook 'pyi_rth_setuptools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
27940 INFO: Including run-time hook 'pyi_rth_pkgres.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
27945 INFO: Including run-time hook 'pyi_rth_cryptography_openssl.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\rthooks'
27946 INFO: Including run-time hook 'pyi_rth__tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
28021 INFO: Creating base_library.zip...
28077 INFO: Looking for dynamic libraries
29379 INFO: Extra DLL search directories (AddDllDirectory): []
29379 INFO: Extra DLL search directories (PATH): []
30125 INFO: Warnings written to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\build\ifess_config_gui_comprehensive\warn-ifess_config_gui_comprehensive.txt
30203 INFO: Graph cross-reference written to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\build\ifess_config_gui_comprehensive\xref-ifess_config_gui_comprehensive.html
30297 INFO: checking PYZ
30297 INFO: Building PYZ because PYZ-00.toc is non existent
30297 INFO: Building PYZ (ZlibArchive) D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\build\ifess_config_gui_comprehensive\PYZ-00.pyz
31541 INFO: Building PYZ (ZlibArchive) D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\build\ifess_config_gui_comprehensive\PYZ-00.pyz completed successfully.
31595 INFO: checking PKG
31595 INFO: Building PKG because PKG-00.toc is non existent
31595 INFO: Building PKG (CArchive) ifess_config_gui.pkg
40204 INFO: Building PKG (CArchive) ifess_config_gui.pkg completed successfully.
40266 INFO: Bootloader C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\bootloader\Windows-64bit-intel\runw.exe
40267 INFO: checking EXE
40267 INFO: Building EXE because EXE-00.toc is non existent
40267 INFO: Building EXE from EXE-00.toc
40267 INFO: Copying bootloader EXE to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\ifess_config_gui.exe
40290 INFO: Copying icon to EXE
40305 INFO: Copying 0 resources to EXE
40305 INFO: Embedding manifest in EXE
40319 INFO: Appending PKG archive to EXE
40616 INFO: Fixing EXE headers
48627 INFO: Building EXE from EXE-00.toc completed successfully.
48753 INFO: Build complete! The results are available in: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config_oauth_tokens.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_oauth_simple.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_module.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_simple.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_oauth.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_secret.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_secrets.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\common -> common
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py -> .
SUCCESS: ifess_config_gui.exe built successfully 
File size: ******** bytes 
[7/7] Post-build tasks and packaging... 
Copying additional files to dist directory... 
Copied: client_config.json 
Copied: client_config_oauth_tokens.json 
Copied: token.json 
Copied: token_backup.json 
Copied: ptrj-backup-services-account.json 
Copied: client_secret.json 
Copied: client_secrets.json 
Creating launcher batch files... 
Created launcher batch files 
Copied: setup_portable_credentials.bat 
Setting up portable credentials configuration... 
Creating distribution README... 
Created distribution README 
Build completed: 06/06/2025 19:59:56,34 
Results: 
  Successful builds: 1/3 
  Errors: 1 
  Warnings: 1 
✓ ifess_client_hidden.exe - Ready ( bytes) 
✓ ifess_config_gui.exe - Ready (******** bytes) 
Distribution ready in: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\ 
Build log saved as: build_log_2025-06-06_19-58-40.txt 
   BUILD FAILED 
