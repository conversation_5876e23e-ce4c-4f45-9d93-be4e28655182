{"server_address": "localhost", "server_port": 5555, "reconnect_interval": 5, "client_id": "client_coba", "display_name": "FDB-Client-Monitoring", "database": {"path": "D:/Gawean Rebinmas/Monitoring Database/Database Ifess/PTRJ_P1A_08042025/PTRJ_P1A.FDB", "username": "sysdba", "password": "masterkey", "isql_path": "C:/Program Files (x86)/Firebird/Firebird_1_5/bin/isql.exe", "use_localhost": true}, "mega": {"email": "<EMAIL>", "password": "ptrj@123"}, "gdrive": {"credentials_file": "D:\\Gawean Rebinmas\\Monitoring Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\token.json"}, "connection": {"exponential_backoff": {"enabled": true, "base_interval": 5, "max_interval": 300, "jitter_percent": 20}, "keep_alive": true, "timeout": null}, "scheduled_upload": {"enabled": true, "hour": 9, "minute": 43, "service": "gdrive"}}